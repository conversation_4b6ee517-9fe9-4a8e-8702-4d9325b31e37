-- Migration: Add notes field to api_keys table
-- This migration ensures the api_keys table has the optional notes field
-- and proper constraints for data integrity.

-- 1. Add notes column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'api_keys' 
        AND column_name = 'notes'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE api_keys ADD COLUMN notes TEXT;
        RAISE NOTICE 'Added notes column to api_keys table';
    ELSE
        RAISE NOTICE 'Notes column already exists in api_keys table';
    END IF;
END $$;

-- 2. Add constraint for notes field length (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.constraint_column_usage 
        WHERE constraint_name = 'api_keys_notes_length'
        AND table_name = 'api_keys'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE api_keys ADD CONSTRAINT api_keys_notes_length 
        CHECK (notes IS NULL OR char_length(notes) <= 500);
        RAISE NOTICE 'Added notes length constraint to api_keys table';
    ELSE
        RAISE NOTICE 'Notes length constraint already exists in api_keys table';
    END IF;
END $$;

-- 3. Update the sanitization trigger to handle notes field for api_keys
CREATE OR REPLACE FUNCTION sanitize_before_insert_update()
RETURNS TRIGGER AS $$
BEGIN
    -- Sanitize name fields
    IF TG_TABLE_NAME IN ('channels', 'folders', 'tags', 'api_keys') THEN
        NEW.name = sanitize_text(NEW.name);
    END IF;
    
    -- Sanitize notes field for saved_videos
    IF TG_TABLE_NAME = 'saved_videos' AND NEW.notes IS NOT NULL THEN
        NEW.notes = sanitize_text(NEW.notes);
    END IF;
    
    -- Sanitize notes field for api_keys (if present)
    IF TG_TABLE_NAME = 'api_keys' AND NEW.notes IS NOT NULL THEN
        NEW.notes = sanitize_text(NEW.notes);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 4. Verify the migration
DO $$
DECLARE
    column_exists BOOLEAN;
    constraint_exists BOOLEAN;
BEGIN
    -- Check if notes column exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'api_keys' 
        AND column_name = 'notes'
        AND table_schema = 'public'
    ) INTO column_exists;
    
    -- Check if constraint exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.constraint_column_usage 
        WHERE constraint_name = 'api_keys_notes_length'
        AND table_name = 'api_keys'
        AND table_schema = 'public'
    ) INTO constraint_exists;
    
    IF column_exists AND constraint_exists THEN
        RAISE NOTICE '✅ Migration completed successfully';
        RAISE NOTICE '   - Notes column: EXISTS';
        RAISE NOTICE '   - Length constraint: EXISTS';
        RAISE NOTICE '   - Sanitization trigger: UPDATED';
    ELSE
        RAISE WARNING '⚠️ Migration may not have completed properly';
        RAISE NOTICE '   - Notes column: %', CASE WHEN column_exists THEN 'EXISTS' ELSE 'MISSING' END;
        RAISE NOTICE '   - Length constraint: %', CASE WHEN constraint_exists THEN 'EXISTS' ELSE 'MISSING' END;
    END IF;
END $$;

-- 5. Test the schema (optional - uncomment to test)
/*
-- Test inserting an API key with notes
INSERT INTO api_keys (name, key, notes) 
VALUES ('Test Key', 'AIzaSyDummyKeyForTesting1234567890123456', 'This is a test note');

-- Test updating notes
UPDATE api_keys SET notes = 'Updated test note' WHERE name = 'Test Key';

-- Clean up test data
DELETE FROM api_keys WHERE name = 'Test Key';
*/
