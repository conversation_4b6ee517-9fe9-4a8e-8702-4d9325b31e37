


import React, { useState, useRef, useEffect } from 'react';
import type { SavedVideoWithTags } from '../types';
import { MoreHorizontalIcon, TrashIcon, NotesIcon, CheckIcon, CloseIcon } from './Icons';
import { useClickOutside } from '../lib/utils';

type GridLayout = '2-grid' | '4-grid' | '6-grid';

interface SavedVideoCardProps {
    video: SavedVideoWithTags;
    layout: GridLayout;
    onDelete: (id: string) => void;
    onUpdateNotes: (id: string, notes: string) => void;
}

const cardStyles: Record<GridLayout, {
    container: string;
    thumbnail: string;
    title: string;
    channel: string;
    notesContainer: string;
    notesText: string;
}> = {
    '2-grid': {
        container: 'space-y-3',
        thumbnail: 'rounded-lg',
        title: 'text-base font-semibold leading-snug line-clamp-2',
        channel: 'text-sm text-gray-400 mt-1 truncate',
        notesContainer: 'bg-dark-card/50 p-2 rounded-md',
        notesText: 'text-sm text-gray-300 line-clamp-3'
    },
    '4-grid': {
        container: 'space-y-2',
        thumbnail: 'rounded-lg',
        title: 'text-sm font-semibold leading-snug line-clamp-2',
        channel: 'text-xs text-gray-400 mt-0.5 truncate',
        notesContainer: 'bg-dark-card/50 p-2 rounded-md',
        notesText: 'text-xs text-gray-300 line-clamp-2'
    },
    '6-grid': {
        container: 'space-y-1.5',
        thumbnail: 'rounded-md',
        title: 'text-xs font-semibold leading-snug line-clamp-2',
        channel: 'hidden',
        notesContainer: 'hidden',
        notesText: 'hidden'
    },
};


export const SavedVideoCard: React.FC<SavedVideoCardProps> = ({ video, layout, onDelete, onUpdateNotes }) => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);
    useClickOutside(menuRef, () => setIsMenuOpen(false));
    
    const [isEditingNotes, setIsEditingNotes] = useState(false);
    const [editedNotes, setEditedNotes] = useState(video.notes || '');
    const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);
    
    const styles = cardStyles[layout];

    const handleEditNotesClick = () => {
        setEditedNotes(video.notes || '');
        setIsEditingNotes(true);
        setIsMenuOpen(false);
    };

    const handleSaveNotes = () => {
        onUpdateNotes(video.id, editedNotes);
        setIsEditingNotes(false);
    };

    const handleDeleteClick = () => {
        if (isConfirmingDelete) {
            onDelete(video.id);
            setIsMenuOpen(false);
        } else {
            setIsConfirmingDelete(true);
        }
    };

    useEffect(() => {
        if (!isMenuOpen) {
            setIsConfirmingDelete(false);
        }
    }, [isMenuOpen]);

    return (
        <div className={`flex flex-col group ${styles.container}`}>
            <a href={video.video_url} target="_blank" rel="noopener noreferrer" className="relative block">
                <img 
                    src={video.video_thumbnail_url || 'https://placehold.co/1600x900/1a1a1a/242424/png?text=?'} 
                    alt={video.video_title} 
                    className={`w-full h-auto rounded-lg object-cover aspect-video transition-transform duration-300 group-hover:scale-105 ${styles.thumbnail}`} 
                />
            </a>
            
            <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0 pr-2">
                    <a href={video.video_url} target="_blank" rel="noopener noreferrer" title={video.video_title}>
                        <h3 className={`${styles.title} text-white`}>{video.video_title || 'Untitled Video'}</h3>
                    </a>
                     <p className={styles.channel} title={video.channel_name}>{video.channel_name || 'Unknown Channel'}</p>
                </div>

                <div className="relative" ref={menuRef}>
                    <button
                        onClick={() => setIsMenuOpen(p => !p)}
                        className="p-1 rounded-full text-gray-400 hover:text-white hover:bg-gray-700 transition-colors opacity-0 group-hover:opacity-100"
                        aria-label="Video options"
                    >
                        <MoreHorizontalIcon className="w-5 h-5" />
                    </button>
                    {isMenuOpen && (
                         <div className="absolute top-full right-0 mt-2 w-48 bg-[#2a2a2a] border border-dark-border rounded-lg shadow-xl z-10 overflow-hidden">
                            <ul className="text-sm text-gray-200">
                                {layout !== '6-grid' &&
                                    <li onClick={handleEditNotesClick} className="flex items-center px-4 py-2.5 cursor-pointer hover:bg-gray-700/50">
                                        <NotesIcon className="w-4 h-4 mr-3" /> Edit Notes
                                    </li>
                                }
                                <li onClick={handleDeleteClick} className={`flex items-center px-4 py-2.5 cursor-pointer ${isConfirmingDelete ? 'text-white bg-red-500/80' : 'text-red-400 hover:bg-red-500/10'}`}>
                                    <TrashIcon className="w-4 h-4 mr-3" /> {isConfirmingDelete ? 'Confirm Delete' : 'Delete'}
                                </li>
                            </ul>
                        </div>
                    )}
                </div>
            </div>

            {isEditingNotes && layout !== '6-grid' && (
                <div className="bg-dark-card p-2 rounded-md border border-accent">
                    <textarea 
                        value={editedNotes}
                        onChange={(e) => setEditedNotes(e.target.value)}
                        className="w-full bg-[#1a1a1a] border border-gray-600 rounded-md px-2 py-1 text-sm text-white focus:outline-none focus:ring-1 focus:ring-accent"
                        rows={3}
                        autoFocus
                    />
                    <div className="flex justify-end items-center gap-2 mt-2">
                        <button onClick={() => setIsEditingNotes(false)} className="p-1 text-gray-400 hover:text-white"><CloseIcon className="w-5 h-5"/></button>
                        <button onClick={handleSaveNotes} className="p-1 text-accent hover:text-white"><CheckIcon className="w-5 h-5"/></button>
                    </div>
                </div>
            )}
            
            {(video.notes && !isEditingNotes && layout !== '6-grid') && (
                <div className={styles.notesContainer}>
                    <p className="font-semibold text-gray-400 text-xs mb-1 flex items-center gap-1.5"><NotesIcon /> NOTES</p>
                    <p className={styles.notesText}>{video.notes}</p>
                </div>
            )}

            {video.tags.length > 0 && layout !== '6-grid' && (
                <div className="flex flex-wrap gap-1.5">
                    {video.tags.map(tag => (
                        <span key={tag.id} className="px-2 py-0.5 text-xs rounded-full bg-accent/10 text-accent font-medium">
                            {tag.name}
                        </span>
                    ))}
                </div>
            )}
        </div>
    );
};
