
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>YouTube Channel Stats Tracker</title>
  <script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "recharts": "https://esm.sh/recharts@^3.0.2",
    "react-icons/ri": "https://esm.sh/react-icons@^5.2.1/ri",
    "react-icons/": "https://esm.sh/react-icons@^5.5.0/",
    "@supabase/supabase-js": "https://esm.sh/@supabase/supabase-js@^2.44.4"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-[#0d0d0d] text-gray-200 font-sans">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>