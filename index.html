
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>YouTube Channel Stats Tracker</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              darkbg: '#1a1a1a',
              'dark-card': '#242424',
              'dark-border': '#3a3a3a',
              accent: '#00ff88',
              'accent-hover': '#00e67a',
            },
            animation: {
              'tag-pulse': 'tag-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
              'tag-bounce': 'tag-bounce 1s ease-in-out',
              'tag-select': 'tag-select 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)',
              'tag-glow': 'tag-glow 2s ease-in-out infinite alternate',
              'shimmer': 'shimmer 2s linear infinite',
            },
            keyframes: {
              'tag-pulse': {
                '0%, 100%': {
                  opacity: '1',
                  transform: 'scale(1)'
                },
                '50%': {
                  opacity: '0.8',
                  transform: 'scale(1.02)'
                },
              },
              'tag-bounce': {
                '0%, 20%, 53%, 80%, 100%': {
                  transform: 'translate3d(0,0,0)',
                },
                '40%, 43%': {
                  transform: 'translate3d(0, -8px, 0)',
                },
                '70%': {
                  transform: 'translate3d(0, -4px, 0)',
                },
                '90%': {
                  transform: 'translate3d(0, -2px, 0)',
                },
              },
              'tag-select': {
                '0%': {
                  transform: 'scale(1)',
                  boxShadow: '0 0 0 0 rgba(0, 255, 136, 0.4)'
                },
                '50%': {
                  transform: 'scale(1.05)',
                  boxShadow: '0 0 0 8px rgba(0, 255, 136, 0.1)'
                },
                '100%': {
                  transform: 'scale(1)',
                  boxShadow: '0 0 0 0 rgba(0, 255, 136, 0)'
                }
              },
              'tag-glow': {
                '0%': {
                  boxShadow: '0 0 5px rgba(0, 255, 136, 0.2), 0 0 10px rgba(0, 255, 136, 0.1)'
                },
                '100%': {
                  boxShadow: '0 0 10px rgba(0, 255, 136, 0.4), 0 0 20px rgba(0, 255, 136, 0.2)'
                }
              },
              'shimmer': {
                '0%': {
                  backgroundPosition: '-200% 0'
                },
                '100%': {
                  backgroundPosition: '200% 0'
                }
              }
            }
          }
        }
      }
    </script>
  <script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "recharts": "https://esm.sh/recharts@^3.0.2",
    "react-icons/ri": "https://esm.sh/react-icons@^5.2.1/ri",
    "react-icons/": "https://esm.sh/react-icons@^5.5.0/",
    "@supabase/supabase-js": "https://esm.sh/@supabase/supabase-js@^2.44.4"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-[#0d0d0d] text-gray-200 font-sans">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>