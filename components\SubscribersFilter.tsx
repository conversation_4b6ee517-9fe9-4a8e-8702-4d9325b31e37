
import React, { useState, useEffect, useCallback } from 'react';
import { parseFormattedNumber, useDebounce } from '../lib/utils';
import { CloseIcon } from './Icons';

interface SubscribersFilterProps {
  onFilterChange: (minSubscribers: number, maxSubscribers: number | null) => void;
  listId: string; // To scope localStorage
  initialMin?: number;
  initialMax?: number | null;
  disabled?: boolean;
}

const getInitialValue = (key: string, fallback: any): string => {
    try {
        const item = window.localStorage.getItem(key);
        // localStorage stores strings, so we might get "null" or an empty string.
        if (item !== null) {
            return JSON.parse(item);
        }
        return String(fallback || '');
    } catch (error) {
        console.warn("Error reading from localStorage", error);
        return String(fallback || '');
    }
};


export const SubscribersFilter: React.FC<SubscribersFilterProps> = ({
  onFilterChange,
  listId,
  initialMin = 0,
  initialMax = null,
  disabled = false,
}) => {
  const minStorageKey = `sub_filter_min_${listId}`;
  const maxStorageKey = `sub_filter_max_${listId}`;
  
  const [minInput, setMinInput] = useState<string>(() => getInitialValue(minStorageKey, initialMin));
  const [maxInput, setMaxInput] = useState<string>(() => getInitialValue(maxStorageKey, initialMax));
  const [error, setError] = useState<string | null>(null);

  const debouncedMin = useDebounce(minInput, 300);
  const debouncedMax = useDebounce(maxInput, 300);

  // Effect to update localStorage and call onFilterChange
  useEffect(() => {
    if (disabled) return;

    const min = parseFormattedNumber(debouncedMin);
    const max = parseFormattedNumber(debouncedMax);
    const finalMin = min ?? 0;
    const finalMax = max;

    try {
        window.localStorage.setItem(minStorageKey, JSON.stringify(debouncedMin));
        window.localStorage.setItem(maxStorageKey, JSON.stringify(debouncedMax));
    } catch (error) {
        console.warn("Error writing to localStorage", error);
    }

    if (finalMax !== null && finalMin > finalMax) {
      setError('Min subscribers cannot be greater than max.');
      return; 
    }
    
    setError(null);
    onFilterChange(finalMin, finalMax);

  }, [debouncedMin, debouncedMax, onFilterChange, disabled, minStorageKey, maxStorageKey]);

  const handleReset = useCallback(() => {
      setMinInput('');
      setMaxInput('');
      setError(null);
  }, []);

  // Keyboard shortcut from prompt
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'r') {
        e.preventDefault();
        handleReset();
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleReset]);


  const hasValue = minInput.length > 0 || maxInput.length > 0;

  const inputClasses = `w-20 bg-dark-card text-white text-center rounded-md p-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-accent transition-shadow disabled:bg-gray-700 disabled:cursor-not-allowed`;
  const containerClasses = `flex items-center gap-2 bg-[#1a1a1a] border p-1 rounded-lg ${error ? 'border-red-500/50' : 'border-dark-border'}`;

  return (
    <div className="flex flex-col items-start">
        <div className={containerClasses}>
            <span className="text-sm font-semibold text-gray-400 pl-2">Subscribers</span>
            <input
                type="text"
                value={minInput}
                onChange={(e) => setMinInput(e.target.value)}
                placeholder="0"
                className={inputClasses}
                aria-label="Minimum subscribers"
                disabled={disabled}
            />
            <span className="text-gray-400 font-medium text-sm">TO</span>
             <input
                type="text"
                value={maxInput}
                onChange={(e) => setMaxInput(e.target.value)}
                placeholder="500M+"
                className={inputClasses}
                aria-label="Maximum subscribers"
                disabled={disabled}
            />
            {hasValue && !disabled && (
                 <button onClick={handleReset} className="p-1 rounded-full text-gray-400 hover:bg-gray-700 hover:text-white transition-colors" aria-label="Clear subscribers filter">
                    <CloseIcon className="w-4 h-4"/>
                </button>
            )}
        </div>
        {error && <p className="text-red-400 text-xs mt-1.5">{error}</p>}
    </div>
  );
};
