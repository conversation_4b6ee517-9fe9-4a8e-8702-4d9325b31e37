#!/usr/bin/env node

/**
 * Quick Quota Validation Test
 * Tests the specific quota validation flow that's failing in the YouTube service
 */

const { createClient } = require('@supabase/supabase-js');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`);
}

async function testQuotaValidation() {
    log('🧪 QUOTA VALIDATION TEST', colors.bright);
    log('=' .repeat(40), colors.blue);
    
    try {
        // Setup Supabase connection
        const supabaseUrl = process.env.VITE_SUPABASE_URL;
        const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
        
        if (!supabaseUrl || !supabaseKey) {
            log('❌ Environment variables not set', colors.red);
            return false;
        }
        
        const supabase = createClient(supabaseUrl, supabaseKey);
        log('✅ Connected to Supabase', colors.green);
        
        // Get an API key to test with
        const { data: apiKeys, error: keysError } = await supabase
            .from('api_keys')
            .select('*')
            .limit(1);
        
        if (keysError) {
            log(`❌ Cannot get API keys: ${keysError.message}`, colors.red);
            return false;
        }
        
        if (!apiKeys || apiKeys.length === 0) {
            log('⚠️ No API keys found, creating test key...', colors.yellow);
            
            const { data: newKey, error: createError } = await supabase
                .from('api_keys')
                .insert({
                    name: 'Test Quota Key',
                    key: 'test_key_for_quota_validation',
                    usage: 500,
                    quota_limit: 10000,
                    last_reset_at: new Date().toISOString()
                })
                .select()
                .single();
            
            if (createError) {
                log(`❌ Cannot create test key: ${createError.message}`, colors.red);
                return false;
            }
            
            apiKeys.push(newKey);
            log(`✅ Created test key: ${newKey.id}`, colors.green);
        }
        
        const testKey = apiKeys[0];
        log(`🔧 Testing with key: ${testKey.id}`, colors.cyan);
        
        // Test 1: Direct function call
        log('\n📊 Test 1: Direct get_quota_status call', colors.blue);
        try {
            const { data: quotaData, error: quotaError } = await supabase
                .rpc('get_quota_status', { api_key_id: testKey.id });
            
            if (quotaError) {
                log(`❌ Function call failed: ${quotaError.message}`, colors.red);
                log(`   Error code: ${quotaError.code}`, colors.yellow);
                log(`   Error details: ${quotaError.details}`, colors.yellow);
                return false;
            }
            
            if (!quotaData || quotaData.length === 0) {
                log('❌ No data returned', colors.red);
                return false;
            }
            
            const quota = quotaData[0];
            log('✅ Function call successful!', colors.green);
            log(`   Structure: ${JSON.stringify(quota, null, 2)}`, colors.cyan);
            
        } catch (error) {
            log(`❌ Test 1 failed: ${error.message}`, colors.red);
            return false;
        }
        
        // Test 2: Simulate the quota service call
        log('\n🔄 Test 2: Simulating quotaService.getQuotaStatus()', colors.blue);
        try {
            const { data, error } = await supabase
                .rpc('get_quota_status', { api_key_id: testKey.id });
            
            if (error) throw new Error(`Failed to get quota status: ${error.message}`);
            if (!data || data.length === 0) throw new Error('API key not found');
            
            const quotaStatus = data[0];
            log('✅ Quota service simulation successful!', colors.green);
            log(`   Current usage: ${quotaStatus.current_usage}`, colors.cyan);
            log(`   Quota limit: ${quotaStatus.quota_limit}`, colors.cyan);
            log(`   Remaining: ${quotaStatus.remaining_quota}`, colors.cyan);
            log(`   Percentage: ${quotaStatus.usage_percentage}%`, colors.cyan);
            log(`   Needs reset: ${quotaStatus.needs_reset}`, colors.cyan);
            
        } catch (error) {
            log(`❌ Test 2 failed: ${error.message}`, colors.red);
            return false;
        }
        
        // Test 3: Simulate the validation flow
        log('\n⚡ Test 3: Simulating validateQuotaUsage()', colors.blue);
        try {
            const operationCost = 100; // Search operation cost
            
            // Get quota status
            const { data, error } = await supabase
                .rpc('get_quota_status', { api_key_id: testKey.id });
            
            if (error) throw new Error(`Failed to get quota status: ${error.message}`);
            if (!data || data.length === 0) throw new Error('API key not found');
            
            const quotaStatus = data[0];
            
            // Validate operation
            const canProceed = operationCost <= quotaStatus.remaining_quota;
            const reason = !canProceed ? 
                `Operation requires ${operationCost} quota but only ${quotaStatus.remaining_quota} remaining` : 
                undefined;
            
            log('✅ Validation flow successful!', colors.green);
            log(`   Can proceed: ${canProceed}`, colors.cyan);
            log(`   Reason: ${reason || 'N/A'}`, colors.cyan);
            
        } catch (error) {
            log(`❌ Test 3 failed: ${error.message}`, colors.red);
            return false;
        }
        
        // Test 4: Test needs_quota_reset function
        log('\n🔄 Test 4: Testing needs_quota_reset function', colors.blue);
        try {
            const { data: needsReset, error: resetError } = await supabase
                .rpc('needs_quota_reset', { api_key_id: testKey.id });
            
            if (resetError) {
                log(`❌ needs_quota_reset failed: ${resetError.message}`, colors.red);
            } else {
                log(`✅ needs_quota_reset successful: ${needsReset}`, colors.green);
            }
            
        } catch (error) {
            log(`⚠️ Test 4 warning: ${error.message}`, colors.yellow);
        }
        
        // Cleanup test key if we created it
        if (testKey.name === 'Test Quota Key') {
            log('\n🧹 Cleaning up test key...', colors.blue);
            await supabase.from('api_keys').delete().eq('id', testKey.id);
            log('✅ Test key cleaned up', colors.green);
        }
        
        log('\n🎉 ALL TESTS PASSED!', colors.green);
        log('The quota validation system is working correctly', colors.green);
        return true;
        
    } catch (error) {
        log(`❌ Test suite failed: ${error.message}`, colors.red);
        return false;
    }
}

async function main() {
    const success = await testQuotaValidation();
    
    if (success) {
        log('\n✅ QUOTA SYSTEM IS WORKING', colors.green);
        log('Your application should now work without quota validation errors', colors.green);
    } else {
        log('\n❌ QUOTA SYSTEM NEEDS FIXING', colors.red);
        log('Please run the fix script:', colors.yellow);
        log('   1. Execute database/fix-quota-function.sql in Supabase SQL Editor', colors.cyan);
        log('   2. Run this test again to verify the fix', colors.cyan);
    }
}

if (require.main === module) {
    main().catch(error => {
        log(`💥 Test crashed: ${error.message}`, colors.red);
        process.exit(1);
    });
}

module.exports = { testQuotaValidation };
