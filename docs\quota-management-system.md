# 🔄 API Key Quota Management System

## Overview

The VirSnapp application now includes a comprehensive automatic quota reset system for YouTube API keys. This system provides daily quota management, usage tracking, automatic resets, and detailed monitoring capabilities.

## 🎯 Key Features

### 1. **Automatic Daily Quota Resets**
- **24-hour reset cycle**: Quotas automatically reset every 24 hours
- **Configurable reset time**: Default midnight UTC, customizable
- **Background scheduler**: Runs continuously to monitor and reset quotas
- **Graceful handling**: Automatic reset on first usage after 24 hours

### 2. **Enhanced Database Schema**
- **`quota_limit`**: Daily quota limit (default: 10,000 requests)
- **`last_reset_at`**: Timestamp of last quota reset
- **`created_at`**: API key creation timestamp
- **Data integrity**: Constraints ensure valid quota values

### 3. **Comprehensive Monitoring**
- **Real-time quota status**: Current usage, remaining quota, percentage used
- **Reset logs**: Complete audit trail of all quota resets
- **Usage statistics**: System-wide quota analytics
- **Visual indicators**: Color-coded warnings for high usage

### 4. **Smart Validation**
- **Pre-operation checks**: Validate quota before API calls
- **Automatic resets**: Trigger reset when needed during operations
- **Usage tracking**: Increment counters with validation
- **Error handling**: Graceful degradation when quotas exceeded

## 🗄️ Database Schema Changes

### Enhanced API Keys Table
```sql
ALTER TABLE api_keys 
ADD COLUMN quota_limit INTEGER DEFAULT 10000,
ADD COLUMN last_reset_at TIMESTAMPTZ DEFAULT NOW(),
ADD COLUMN created_at TIMESTAMPTZ DEFAULT NOW();
```

### New Quota Reset Logs Table
```sql
CREATE TABLE quota_reset_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    api_key_id UUID REFERENCES api_keys(id),
    reset_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    previous_usage INTEGER,
    batch_reset BOOLEAN DEFAULT false,
    keys_reset INTEGER,
    total_keys INTEGER
);
```

### Database Functions
- **`reset_api_key_quota(UUID)`**: Reset individual API key quota
- **`reset_all_api_key_quotas()`**: Reset all quotas (daily batch)
- **`get_quota_status(UUID)`**: Get comprehensive quota information
- **`needs_quota_reset(UUID)`**: Check if reset is needed

## 🔧 Implementation Components

### 1. **Quota Service** (`services/quotaService.ts`)
Core service handling all quota operations:

```typescript
// Get quota status
const status = await quotaService.getQuotaStatus(apiKeyId);

// Validate operation
const validation = await quotaService.validateQuotaUsage(apiKeyId, operationCost);

// Increment usage
const result = await quotaService.incrementUsage(apiKeyId, usageCost);

// Reset quota
await quotaService.resetApiKeyQuota(apiKeyId);
```

### 2. **Quota Scheduler** (`services/quotaScheduler.ts`)
Background scheduler for automatic resets:

```typescript
// Initialize scheduler
const scheduler = initializeQuotaScheduler({
    resetTimeUTC: "00:00",
    enableAutoReset: true,
    checkIntervalMinutes: 60
});

// Manual reset
await scheduler.manualReset();

// Get status
const status = scheduler.getStatus();
```

### 3. **Enhanced API Key Service**
Updated with quota integration:

```typescript
// Increment usage with validation
const result = await apiKeyService.incrementUsage(keyId, operationCost);

// Get key with quota status
const keyWithQuota = await apiKeyService.getApiKeyWithQuotaStatus(keyId);

// Validate operation
const validation = await apiKeyService.validateOperation(keyId, cost);
```

## 🎨 User Interface Components

### 1. **ApiKeyQuotaCard** (`components/ApiKeyQuotaCard.tsx`)
Visual quota status display:
- **Current usage** and **remaining quota**
- **Progress bar** with color-coded warnings
- **Reset countdown** timer
- **Manual reset button** (when available)
- **Warning messages** for high usage

### 2. **QuotaManagementDashboard** (`components/QuotaManagementDashboard.tsx`)
Comprehensive management interface:
- **System-wide statistics**
- **Scheduler status** and controls
- **Recent reset activity** logs
- **Manual reset** capabilities

### 3. **Enhanced Settings Page**
Integrated quota management:
- **Tab navigation** between API keys and quota management
- **Quota cards** for each API key
- **Real-time status** updates

## ⚙️ Configuration Options

### Scheduler Configuration
```typescript
interface SchedulerConfig {
    resetTimeUTC: string;        // "HH:MM" format (e.g., "00:00")
    enableAutoReset: boolean;    // Enable automatic resets
    checkIntervalMinutes: number; // How often to check (default: 60)
}
```

### Environment Presets
- **Production**: Midnight UTC, hourly checks, auto-reset enabled
- **Development**: Midnight UTC, 30-minute checks, auto-reset enabled  
- **Testing**: Manual control, 5-minute checks, auto-reset disabled

## 📊 Quota Constants

```typescript
export const QUOTA_CONSTANTS = {
    DEFAULT_DAILY_QUOTA: 10000,
    RESET_INTERVAL_HOURS: 24,
    HIGH_USAGE_THRESHOLD: 0.8,    // 80%
    WARNING_THRESHOLD: 0.9,       // 90%
    OPERATION_COSTS: {
        SEARCH: 100,
        CHANNELS: 1,
        VIDEOS: 1,
        PLAYLIST: 1,
        CHANNEL_DETAILS: 1
    }
};
```

## 🚀 Setup Instructions

### 1. **Database Migration**
Run the migration script to add quota management:
```sql
-- Execute database/api-key-quota-migration.sql
-- This adds new columns, functions, and constraints
```

### 2. **Application Integration**
The quota system initializes automatically when the app starts:
```typescript
// Automatic initialization in App.tsx
quotaService.initializeQuotaSystem();
initializeQuotaScheduler();
```

### 3. **Usage in API Calls**
Integrate quota validation in YouTube API calls:
```typescript
// Before making API call
const validation = await apiKeyService.validateOperation(keyId, operationCost);
if (!validation.canProceed) {
    throw new Error(validation.reason);
}

// After successful API call
await apiKeyService.incrementUsage(keyId, operationCost);
```

## 🔍 Monitoring and Logging

### Quota Reset Logs
All quota resets are logged with:
- **Timestamp** of reset
- **Previous usage** before reset
- **Batch vs individual** reset type
- **Number of keys** affected

### System Statistics
Monitor quota health with:
- **Total API keys** and usage
- **Keys needing reset**
- **Average usage** per key
- **High usage warnings** (>80% quota)

### Visual Indicators
- **Green**: Normal usage (<60%)
- **Orange**: Moderate usage (60-80%)
- **Yellow**: High usage (80-90%)
- **Red**: Critical usage (>90%)

## 🛡️ Error Handling

### Quota Exceeded
When quota is exceeded:
1. **Validation fails** before API call
2. **Clear error message** provided
3. **Automatic reset** if 24 hours passed
4. **Graceful degradation** of functionality

### Reset Failures
If automatic reset fails:
1. **Error logging** for debugging
2. **Manual reset** option available
3. **Retry mechanisms** in place
4. **Fallback procedures** activated

## 🔄 Daily Reset Process

### Automatic Reset Flow
1. **Scheduler checks** every hour (configurable)
2. **Identifies keys** needing reset (>24 hours old)
3. **Batch resets** all eligible keys
4. **Logs results** for monitoring
5. **Updates timestamps** for next cycle

### Manual Reset Options
- **Individual key reset**: Reset specific API key
- **Batch reset**: Reset all keys immediately
- **Scheduled reset**: Trigger next automatic reset

## 📈 Performance Considerations

### Database Optimization
- **Indexes** on `last_reset_at` and usage columns
- **Efficient queries** for quota status
- **Batch operations** for resets
- **Connection pooling** for high load

### Memory Management
- **Lightweight scheduler** with minimal overhead
- **Efficient caching** of quota status
- **Cleanup** of old reset logs
- **Resource monitoring** and limits

## 🔮 Future Enhancements

### Planned Features
1. **Custom quota limits** per API key
2. **Usage analytics** and reporting
3. **Quota alerts** and notifications
4. **API rate limiting** integration
5. **Multi-timezone** support
6. **Quota sharing** between keys
7. **Historical usage** trends
8. **Export capabilities** for logs

### Integration Opportunities
- **Webhook notifications** for quota events
- **External monitoring** system integration
- **Slack/Discord** alerts for high usage
- **Dashboard widgets** for real-time monitoring

The quota management system provides a robust, scalable solution for managing YouTube API quotas with automatic resets, comprehensive monitoring, and user-friendly interfaces.
