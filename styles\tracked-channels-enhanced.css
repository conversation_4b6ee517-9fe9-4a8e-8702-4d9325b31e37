/* Enhanced Tracked Channels Page Styles */

/* Line clamp utility for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth animations for enhanced interactions */
.hover-scale {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Enhanced card shadows */
.card-shadow-hover {
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.3),
    0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* Focus ring for accessibility */
.focus-ring:focus {
  outline: 2px solid #10b981;
  outline-offset: 2px;
}

/* Gradient backgrounds for enhanced visual appeal */
.gradient-card {
  background: linear-gradient(135deg, #1a1a1a 0%, #151515 100%);
}

/* Button hover effects */
.btn-primary {
  background: #10b981;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-secondary {
  background: transparent;
  border: 1px solid #374151;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #374151;
  border-color: #6b7280;
}

/* Icon animations */
.icon-hover {
  transition: transform 0.2s ease;
}

.icon-hover:hover {
  transform: scale(1.1);
}

/* Loading skeleton animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-shimmer {
  background: linear-gradient(90deg, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Responsive grid utilities */
@media (max-width: 767px) {
  .mobile-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .mobile-padding {
    padding: 1rem;
  }
  
  .mobile-text-sm {
    font-size: 0.875rem;
  }
}

@media (min-width: 768px) and (max-width: 1199px) {
  .tablet-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1200px) {
  .desktop-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Enhanced dark theme variables */
:root {
  --bg-primary: #0f0f0f;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #151515;
  --border-primary: #333;
  --border-secondary: #2a2a2a;
  --text-primary: #ffffff;
  --text-secondary: #9ca3af;
  --text-tertiary: #6b7280;
  --accent-primary: #10b981;
  --accent-hover: #059669;
  --accent-light: rgba(16, 185, 129, 0.1);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* Enhanced focus states for accessibility */
button:focus-visible,
a:focus-visible {
  outline: 2px solid #10b981;
  outline-offset: 2px;
}

/* Improved button states */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Enhanced card interactions */
.card-interactive {
  transition: all 0.2s ease;
  cursor: pointer;
}

.card-interactive:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.card-interactive:active {
  transform: scale(0.98);
}

/* Status indicators */
.status-active {
  background: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.status-inactive {
  background: #6b7280;
}

/* Enhanced typography */
.text-gradient {
  background: linear-gradient(135deg, #ffffff 0%, #9ca3af 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Micro-interactions */
.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Fade in animation for page load */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
