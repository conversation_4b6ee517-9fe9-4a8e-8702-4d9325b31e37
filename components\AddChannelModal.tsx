
import React, { useState } from 'react';

interface AddChannelModalProps {
    onClose: () => void;
    onAddChannel: (name: string) => Promise<void>;
}

export const AddChannelModal: React.FC<AddChannelModalProps> = ({ onClose, onAddChannel }) => {
    const [newChannelName, setNewChannelName] = useState('');
    const [isAdding, setIsAdding] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!newChannelName.trim() || isAdding) return;
        setIsAdding(true);
        await onAddChannel(newChannelName);
        // component will be unmounted on success, no need to setIsAdding(false)
    };
    
    return (
        <div 
            className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4"
            aria-modal="true"
            role="dialog"
        >
            <div 
                className="fixed inset-0"
                onClick={onClose}
                aria-hidden="true"
            ></div>
            <div className="relative bg-dark-card border border-dark-border rounded-xl w-full max-w-md p-6">
                 <h2 className="text-xl font-bold text-white mb-4">Create new channel list</h2>
                 <p className="text-gray-400 mb-6">Enter the name of the YouTube channel you want to start tracking.</p>
                 <form onSubmit={handleSubmit}>
                    <div className="flex flex-col space-y-4">
                         <input
                            id="new-channel"
                            type="text"
                            value={newChannelName}
                            onChange={e => setNewChannelName(e.target.value)}
                            placeholder="e.g., Linus Tech Tips"
                            className="bg-gray-700 border border-gray-600 rounded-md px-3 py-2.5 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent"
                            disabled={isAdding}
                            autoFocus
                        />
                        <div className="flex justify-end space-x-3 pt-2">
                             <button type="button" onClick={onClose} disabled={isAdding} className="px-4 py-2 rounded-md bg-gray-600 text-white hover:bg-gray-500 transition-colors disabled:opacity-50">
                                Cancel
                             </button>
                             <button type="submit" disabled={isAdding || !newChannelName.trim()} className="px-4 py-2 rounded-md bg-accent text-darkbg hover:bg-accent-hover disabled:bg-gray-500 disabled:cursor-not-allowed transition-colors flex items-center justify-center w-28">
                                {isAdding ? <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-darkbg"></div> : 'Add Channel'}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
};
