#!/usr/bin/env node

/**
 * Security Validation Script
 * Validates that all security measures are properly implemented
 * Run this before deploying to production
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function checkFileExists(filePath, description) {
  const exists = fs.existsSync(filePath);
  if (exists) {
    log(`✅ ${description}`, colors.green);
  } else {
    log(`❌ ${description} - File missing: ${filePath}`, colors.red);
  }
  return exists;
}

function checkFileContains(filePath, searchString, description) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const contains = content.includes(searchString);
    if (contains) {
      log(`✅ ${description}`, colors.green);
    } else {
      log(`❌ ${description} - Not found in: ${filePath}`, colors.red);
    }
    return contains;
  } catch (error) {
    log(`❌ ${description} - Error reading file: ${filePath}`, colors.red);
    return false;
  }
}

function checkEnvironmentTemplate() {
  log('\n📋 Checking Environment Configuration...', colors.blue);
  
  let score = 0;
  const total = 4;
  
  if (checkFileExists('.env.example', 'Environment template exists')) score++;
  if (checkFileContains('.env.example', 'VITE_SUPABASE_URL', 'Environment template has Supabase URL')) score++;
  if (checkFileContains('.env.example', 'VITE_SUPABASE_ANON_KEY', 'Environment template has Supabase key')) score++;
  if (checkFileContains('.gitignore', '.env.local', 'Environment files are gitignored')) score++;
  
  return score / total;
}

function checkSecurityFiles() {
  log('\n🔒 Checking Security Implementation Files...', colors.blue);
  
  let score = 0;
  const total = 6;
  
  if (checkFileExists('lib/inputValidation.ts', 'Input validation utilities')) score++;
  if (checkFileExists('lib/apiKeySecurity.ts', 'API key security utilities')) score++;
  if (checkFileExists('lib/securityMonitoring.ts', 'Security monitoring utilities')) score++;
  if (checkFileExists('lib/env.ts', 'Environment validation utilities')) score++;
  if (checkFileExists('database/security-policies.sql', 'Database security policies')) score++;
  if (checkFileExists('SECURITY.md', 'Security documentation')) score++;
  
  return score / total;
}

function checkSecurityImplementation() {
  log('\n🛡️ Checking Security Implementation...', colors.blue);
  
  let score = 0;
  const total = 8;
  
  // Check if hardcoded credentials are removed from source code
  const supabaseContent = fs.readFileSync('lib/supabase.ts', 'utf8');
  const hasHardcodedUrl = supabaseContent.includes('https://lofjviejdjqlrapaoyee.supabase.co');
  const hasHardcodedKey = supabaseContent.includes('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9');

  if (!hasHardcodedUrl) {
    log('✅ Hardcoded Supabase URL removed from source code', colors.green);
    score++;
  } else {
    log('❌ Hardcoded Supabase URL still present in source code', colors.red);
  }

  if (!hasHardcodedKey) {
    log('✅ Hardcoded Supabase key removed from source code', colors.green);
    score++;
  } else {
    log('❌ Hardcoded Supabase key still present in source code', colors.red);
  }

  // Check if environment variables are used
  if (checkFileContains('lib/supabase.ts', 'VITE_SUPABASE_URL', 'Environment variables used for Supabase URL')) score++;
  if (checkFileContains('lib/supabase.ts', 'VITE_SUPABASE_ANON_KEY', 'Environment variables used for Supabase key')) score++;
  
  // Check if validation is implemented
  if (checkFileContains('services/youtubeService.ts', 'validateYouTubeChannelQuery', 'YouTube service has input validation')) score++;
  if (checkFileContains('services/savedContentService.ts', 'validateName', 'Saved content service has input validation')) score++;
  if (checkFileContains('services/apiKeyService.ts', 'encryptApiKey', 'API key service has encryption')) score++;
  
  // Check if security monitoring is initialized
  if (checkFileContains('App.tsx', 'initializeSecurityMonitoring', 'Security monitoring is initialized')) score++;
  
  return score / total;
}

function checkDatabaseSecurity() {
  log('\n🗄️ Checking Database Security...', colors.blue);
  
  let score = 0;
  const total = 5;
  
  if (checkFileContains('database/security-policies.sql', 'ENABLE ROW LEVEL SECURITY', 'RLS policies defined')) score++;
  if (checkFileContains('database/security-policies.sql', 'CREATE POLICY', 'Security policies created')) score++;
  if (checkFileContains('database/security-policies.sql', 'ADD CONSTRAINT', 'Data constraints added')) score++;
  if (checkFileContains('database/security-policies.sql', 'validate_youtube_api_key', 'API key validation function')) score++;
  if (checkFileContains('database/security-policies.sql', 'sanitize_text', 'Text sanitization function')) score++;
  
  return score / total;
}

function checkTestSuite() {
  log('\n🧪 Checking Test Suite...', colors.blue);
  
  let score = 0;
  const total = 3;
  
  if (checkFileExists('tests/security.test.ts', 'Security test suite exists')) score++;
  if (checkFileContains('tests/security.test.ts', 'runSecurityTests', 'Security tests implemented')) score++;
  if (checkFileContains('tests/security.test.ts', 'runPenetrationTests', 'Penetration tests implemented')) score++;
  
  return score / total;
}

function checkDocumentation() {
  log('\n📚 Checking Documentation...', colors.blue);
  
  let score = 0;
  const total = 4;
  
  if (checkFileExists('SECURITY.md', 'Security documentation exists')) score++;
  if (checkFileExists('database/README.md', 'Database documentation exists')) score++;
  if (checkFileContains('README.md', 'Security Features', 'README mentions security features')) score++;
  if (checkFileContains('README.md', 'Production Deployment', 'README has deployment guide')) score++;
  
  return score / total;
}

function generateSecurityReport() {
  log('\n' + '='.repeat(60), colors.bold);
  log('🔒 VIRSNAPP SECURITY VALIDATION REPORT', colors.bold);
  log('='.repeat(60), colors.bold);
  
  const scores = {
    environment: checkEnvironmentTemplate(),
    files: checkSecurityFiles(),
    implementation: checkSecurityImplementation(),
    database: checkDatabaseSecurity(),
    tests: checkTestSuite(),
    documentation: checkDocumentation()
  };
  
  const overallScore = Object.values(scores).reduce((a, b) => a + b, 0) / Object.keys(scores).length;
  
  log('\n📊 SECURITY SCORE BREAKDOWN:', colors.blue);
  log(`Environment Configuration: ${(scores.environment * 100).toFixed(1)}%`);
  log(`Security Files: ${(scores.files * 100).toFixed(1)}%`);
  log(`Implementation: ${(scores.implementation * 100).toFixed(1)}%`);
  log(`Database Security: ${(scores.database * 100).toFixed(1)}%`);
  log(`Test Suite: ${(scores.tests * 100).toFixed(1)}%`);
  log(`Documentation: ${(scores.documentation * 100).toFixed(1)}%`);
  
  log('\n' + '='.repeat(60), colors.bold);
  
  const scoreColor = overallScore >= 0.9 ? colors.green : overallScore >= 0.7 ? colors.yellow : colors.red;
  log(`🎯 OVERALL SECURITY SCORE: ${(overallScore * 100).toFixed(1)}%`, scoreColor);
  
  if (overallScore >= 0.9) {
    log('🎉 Excellent! Your application has strong security measures.', colors.green);
    log('✅ Ready for production deployment.', colors.green);
  } else if (overallScore >= 0.7) {
    log('⚠️  Good security implementation, but some improvements needed.', colors.yellow);
    log('🔧 Review failed checks before production deployment.', colors.yellow);
  } else {
    log('🚨 Security implementation needs significant improvement.', colors.red);
    log('❌ DO NOT deploy to production until issues are resolved.', colors.red);
  }
  
  log('\n📋 NEXT STEPS:', colors.blue);
  if (overallScore < 1.0) {
    log('1. Fix all failed security checks above');
    log('2. Run this script again to verify fixes');
    log('3. Test security measures manually');
    log('4. Review SECURITY.md for deployment checklist');
  } else {
    log('1. Run security tests: npm run test:security');
    log('2. Apply database policies from database/security-policies.sql');
    log('3. Set production environment variables');
    log('4. Follow deployment checklist in SECURITY.md');
  }
  
  log('\n' + '='.repeat(60), colors.bold);
  
  return overallScore;
}

// Main execution
const isMainModule = import.meta.url === `file://${process.argv[1]}` ||
                     import.meta.url.endsWith(process.argv[1]) ||
                     process.argv[1].includes('validate-security.js');

if (isMainModule) {
  // Change to project root directory
  process.chdir(path.join(__dirname, '..'));
  const score = generateSecurityReport();
  process.exit(score >= 0.9 ? 0 : 1);
}

export { generateSecurityReport };
