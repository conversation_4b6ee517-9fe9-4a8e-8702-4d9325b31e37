

import React, { useState, useEffect, useCallback } from 'react';
import { Sidebar } from './components/Sidebar';
import { ChannelListPage } from './components/ChannelListPage';
import { TrackedChannelsPage } from './components/TrackedChannelsPage';
import { youtubeService } from './services/youtubeService';
import { savedContentService } from './services/savedContentService';
import type { Channel, Folder, Tag, SearchResultChannel, SavedVideoPostData } from './types';
import { LoadingSpinner } from './components/LoadingSpinner';
import { SavedVideosPage } from './components/SavedVideosPage';
import { ManageFoldersModal } from './components/ManageFoldersModal';
import { ManageTagsModal } from './components/ManageTagsModal';
import { SaveVideoModal } from './components/SaveVideoModal';
import { CreateChannelListPage } from './components/CreateChannelListPage';
import { SettingsPage } from './components/SettingsPage';
import { RiMenuLine } from 'react-icons/ri';
import { isSupabaseConfigured } from './lib/supabase';
import { SupabaseSetupNotice } from './components/SupabaseSetupNotice';
import { SavedVideosListPage } from './components/SavedVideosListPage';
import { quotaService } from './services/quotaService';
import { initializeQuotaScheduler } from './services/quotaScheduler';
import { initializeSecurityMonitoring } from './lib/securityMonitoring';

type Page = 'tracked' | 'saved' | 'settings';
type Modal = null | 'saveVideo' | 'manageFolders' | 'manageTags';


const App: React.FC = () => {
    // --- Configuration Check ---
    if (!isSupabaseConfigured) {
        return <SupabaseSetupNotice />;
    }

    // Page and Modal State
    const [currentPage, setCurrentPage] = useState<Page>('tracked');
    const [modal, setModal] = useState<Modal>(null);
    const [isCreatingList, setIsCreatingList] = useState(false);
    const [listIdToAddTo, setListIdToAddTo] = useState<string | null>(null);
    const [prefilledUrl, setPrefilledUrl] = useState<string | null>(null);

    // Data State
    const [channels, setChannels] = useState<Channel[]>([]); // This state now holds Channel Lists
    const [folders, setFolders] = useState<Folder[]>([]);
    const [tags, setTags] = useState<Tag[]>([]);
    const [selectedChannelId, setSelectedChannelId] = useState<string | null>(null);
    const [selectedSavedFolderId, setSelectedSavedFolderId] = useState<string | null>(null);

    // UI State
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);
    
    // --- Data Fetching ---
    const fetchFoldersAndCounts = async () => {
        const [fetchedFolders, videoCounts] = await Promise.all([
            savedContentService.getFolders(),
            savedContentService.getAllSavedVideoCounts(),
        ]);
        const foldersWithCounts = fetchedFolders.map(folder => ({
            ...folder,
            video_count: videoCounts.get(folder.id) || 0,
        }));
        setFolders(foldersWithCounts);
    };

    const fetchData = useCallback(async () => {
        try {
            setLoading(true);
            const [fetchedChannels, fetchedTags] = await Promise.all([
                youtubeService.getChannels(),
                savedContentService.getTags()
            ]);
            
            setChannels(fetchedChannels);
            setTags(fetchedTags);
            await fetchFoldersAndCounts();

        } catch (err) {
            setError('Failed to fetch initial data.');
            console.error(err);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();

        // Initialize security monitoring and quota management
        if (isSupabaseConfigured) {
            initializeSecurityMonitoring();

            // Initialize quota management system
            quotaService.initializeQuotaSystem().catch(error => {
                console.error('Failed to initialize quota system:', error);
            });

            // Initialize quota scheduler for automatic daily resets
            initializeQuotaScheduler({
                resetTimeUTC: "00:00", // Midnight UTC
                enableAutoReset: true,
                checkIntervalMinutes: 60 // Check every hour
            });
        }
    }, [fetchData]);

    // --- Channel List Handlers ---
    const handleCreateChannelList = async (listName: string, channelsToAdd: SearchResultChannel[]): Promise<void> => {
        try {
            const newList = await youtubeService.createChannelList(listName, channelsToAdd);
            setChannels(prev => [...prev, newList]);
            setIsCreatingList(false);
            handleSelectChannel(newList.id);
        } catch (err) {
            throw err;
        }
    };
    
    const handleAddChannelsToList = async (listId: string, channelsToAdd: SearchResultChannel[]): Promise<void> => {
        try {
            const updatedList = await youtubeService.addChannelsToList(listId, channelsToAdd);
            setChannels(prev => prev.map(c => c.id === listId ? updatedList : c));
            setListIdToAddTo(null);
            setSelectedChannelId(listId);
        } catch (err) {
            console.error('Failed to add channels to list:', err);
            throw err;
        }
    };

    const handleDeleteChannel = async (channelId: string): Promise<void> => {
        try {
            await youtubeService.deleteChannel(channelId);
            setChannels(prev => prev.filter(c => c.id !== channelId));
            if (selectedChannelId === channelId) {
                setSelectedChannelId(null);
            }
        } catch (err) {
             throw err;
        }
    };
    
    const handleRenameChannel = async (channelId: string, newName: string): Promise<void> => {
        try {
            const renamedChannel = await youtubeService.renameChannel(channelId, newName);
            setChannels(prev => prev.map(c => c.id === channelId ? renamedChannel : c));
        } catch (err) {
            throw err;
        }
    };

    const handleDuplicateChannel = async (channelId: string): Promise<void> => {
        try {
            const duplicatedChannel = await youtubeService.duplicateChannel(channelId);
            setChannels(prev => {
                const newChannels = [...prev];
                const originalIndex = newChannels.findIndex(c => c.id === channelId);
                if (originalIndex !== -1) {
                    newChannels.splice(originalIndex + 1, 0, duplicatedChannel);
                } else {
                    newChannels.push(duplicatedChannel);
                }
                return newChannels;
            });
        } catch (err) {
            throw err;
        }
    };

    const handlePinChannel = async (channelId: string, isPinned: boolean): Promise<void> => {
        try {
            const updatedChannel = await youtubeService.pinChannel(channelId, isPinned);
            setChannels(prev => prev.map(c => c.id === channelId ? updatedChannel : c));
        } catch (err) {
            throw err;
        }
    };

    const handleRemoveChannelsFromList = async (listId: string, channelYoutubeIdsToRemove: string[]) => {
        if (channelYoutubeIdsToRemove.length === 0) return;
        try {
            const updatedList = await youtubeService.removeChannelsFromList(listId, channelYoutubeIdsToRemove);
            setChannels(prev => prev.map(c => c.id === listId ? updatedList : c));
        } catch (err) {
            console.error('Failed to remove channels from list:', err);
            throw err;
        }
    };

    // --- Saved Content Handlers ---
    const handleAddFolder = async (name: string) => {
        const newFolder = await savedContentService.addFolder(name);
        setFolders(prev => [...prev, {...newFolder, video_count: 0}]);
    };
    const handleRenameFolder = async (id: string, newName: string) => {
        const updatedFolder = await savedContentService.renameFolder(id, newName);
        setFolders(prev => prev.map(f => f.id === id ? {...f, ...updatedFolder} : f));
    };
    const handleDeleteFolder = async (id: string) => {
        await savedContentService.deleteFolder(id);
        setFolders(prev => prev.filter(f => f.id !== id));
    };
    const handleAddTag = async (name: string) => {
        const newTag = await savedContentService.addTag(name);
        setTags(prev => [...prev, newTag]);
    };
    const handleRenameTag = async (id: string, newName: string) => {
        const updatedTag = await savedContentService.renameTag(id, newName);
        setTags(prev => prev.map(t => t.id === id ? updatedTag : t));
    };
    const handleDeleteTag = async (id: string) => {
        await savedContentService.deleteTag(id);
        setTags(prev => prev.filter(t => t.id !== id));
    };

    const handleSaveVideo = async (data: {url: string, folderId: string, tagIds: string[], notes: string}) => {
        const { url, folderId, tagIds, notes } = data;
        try {
            const videoDetails = await youtubeService.getVideoDetailsByUrl(url);
            const postData: SavedVideoPostData = {
                folder_id: folderId,
                video_url: url,
                video_title: videoDetails.title,
                video_thumbnail_url: videoDetails.thumbnailUrl,
                channel_name: videoDetails.channelName,
                notes: notes,
            };
            await savedContentService.saveVideo(postData, tagIds);
            await fetchFoldersAndCounts(); // Refresh counts
            setModal(null); // Close modal on success
        } catch (err) {
            console.error("Failed to save video:", err);
            throw err; // Re-throw to be caught by the modal
        }
    };
    
    // --- Navigation Handlers ---
    const handleSelectChannel = (id: string | null) => {
      setSelectedChannelId(id);
      if (id !== null) {
          setCurrentPage('tracked');
          setIsCreatingList(false);
          setListIdToAddTo(null);
      }
      setIsSidebarOpen(false);
    }

    const handleNavigate = (page: Page) => {
        setCurrentPage(page);
        setSelectedChannelId(null);
        setSelectedSavedFolderId(null);
        setIsCreatingList(false);
        setListIdToAddTo(null);
        setIsSidebarOpen(false);
    }
    
    // --- Render Logic ---
    const renderContent = () => {
        if (loading) {
            return <div className="flex-1 flex items-center justify-center"><LoadingSpinner /></div>;
        }
        if (error) {
            return <div className="flex-1 flex items-center justify-center text-red-500">{error}</div>;
        }
        
        const listToAddTo = listIdToAddTo ? channels.find(c => c.id === listIdToAddTo) : null;
        if (listToAddTo) {
            return <CreateChannelListPage
                channels={channels.filter(c => c.id !== listToAddTo.id)}
                onBack={() => {
                    setListIdToAddTo(null);
                    setSelectedChannelId(listToAddTo.id);
                }}
                onCreate={async (_listName, channelsToAdd) => {
                    await handleAddChannelsToList(listToAddTo.id, channelsToAdd);
                }}
                onNavigateToSettings={() => handleNavigate('settings')}
                pageTitle={`Add channels to "${listToAddTo.name}"`}
                submitText="Add"
                hideNameInput={true}
            />;
        }


        if (isCreatingList) {
            return <CreateChannelListPage 
                        channels={channels}
                        onBack={() => setIsCreatingList(false)}
                        onCreate={handleCreateChannelList}
                        onNavigateToSettings={() => handleNavigate('settings')}
                   />;
        }

        if (selectedChannelId) {
            const selectedList = channels.find(c => c.id === selectedChannelId);
            if (!selectedList) {
                setSelectedChannelId(null); // Fallback if list is deleted
                return null;
            }
            return <ChannelListPage
                        key={selectedChannelId}
                        list={selectedList}
                        onBack={() => handleSelectChannel(null)}
                        onAddChannelsClick={() => {
                            setSelectedChannelId(null);
                            setListIdToAddTo(selectedChannelId);
                        }}
                        onRemoveChannelsFromList={handleRemoveChannelsFromList}
                        onSaveVideoClick={(videoUrl: string) => {
                            setPrefilledUrl(videoUrl);
                            setModal('saveVideo');
                        }}
                   />;
        }

        if(currentPage === 'saved' && selectedSavedFolderId) {
            const selectedFolder = folders.find(f => f.id === selectedSavedFolderId);
            if (!selectedFolder) {
                setSelectedSavedFolderId(null); // Fallback
                return null;
            }
            return <SavedVideosListPage folder={selectedFolder} onBack={() => setSelectedSavedFolderId(null)} />
        }
        
        switch (currentPage) {
            case 'tracked':
                return <TrackedChannelsPage
                    channels={channels}
                    onSelectChannel={handleSelectChannel}
                    onAddChannelClick={() => setIsCreatingList(true)}
                    onDeleteChannel={handleDeleteChannel}
                    onRenameChannel={handleRenameChannel}
                    onDuplicateChannel={handleDuplicateChannel}
                    onPinChannel={handlePinChannel}
                />;
            case 'saved':
                return <SavedVideosPage 
                    folders={folders}
                    onOpenModal={(modalType) => {
                        setPrefilledUrl(null);
                        setModal(modalType);
                    }}
                    onRenameFolder={handleRenameFolder}
                    onDeleteFolder={handleDeleteFolder}
                    onSelectFolder={setSelectedSavedFolderId}
                />
            case 'settings':
                 return <SettingsPage />;
            default:
                return null;
        }
    }

    return (
        <div className="flex h-screen bg-[#0d0d0d] overflow-hidden">
            <Sidebar 
                currentPage={currentPage}
                onNavigate={handleNavigate}
                isOpen={isSidebarOpen}
                setIsOpen={setIsSidebarOpen}
            />
            <main className="flex-1 flex flex-col overflow-y-auto transition-all duration-300 relative">
                <button 
                  onClick={() => setIsSidebarOpen(!isSidebarOpen)} 
                  className="lg:hidden p-2 text-white bg-dark-card/50 backdrop-blur-sm fixed top-4 left-4 z-20 rounded-md"
                  aria-label="Toggle navigation"
                >
                    <RiMenuLine className="h-6 w-6" />
                </button>
                {renderContent()}
            </main>
            
            {modal === 'manageFolders' && (
                <ManageFoldersModal
                    folders={folders}
                    onClose={() => setModal(null)}
                    onAdd={handleAddFolder}
                    onRename={handleRenameFolder}
                    onDelete={handleDeleteFolder}
                />
            )}
             {modal === 'manageTags' && (
                <ManageTagsModal
                    tags={tags}
                    onClose={() => setModal(null)}
                    onAdd={handleAddTag}
                    onRename={handleRenameTag}
                    onDelete={handleDeleteTag}
                />
            )}
            {modal === 'saveVideo' && (
                <SaveVideoModal
                    folders={folders}
                    tags={tags}
                    onClose={() => {
                        setModal(null);
                        setPrefilledUrl(null);
                    }}
                    onSave={handleSaveVideo}
                    onManageFolders={() => { setModal(null); setTimeout(() => setModal('manageFolders'), 10); }}
                    onManageTags={() => { setModal(null); setTimeout(() => setModal('manageTags'), 10); }}
                    prefilledUrl={prefilledUrl}
                />
            )}
        </div>
    );
};

export default App;