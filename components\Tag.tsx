import React from 'react';

export type TagVariant = 'default' | 'selected' | 'interactive' | 'compact' | 'large';
export type TagSize = 'xs' | 'sm' | 'md' | 'lg';

interface TagProps {
    children: React.ReactNode;
    variant?: TagVariant;
    size?: TagSize;
    onClick?: () => void;
    className?: string;
    disabled?: boolean;
    'aria-label'?: string;
}

const tagVariants: Record<TagVariant, string> = {
    default: 'bg-gradient-to-r from-accent/12 to-accent/8 text-accent/90 border border-accent/15 hover:from-accent/18 hover:to-accent/12 hover:text-accent',
    selected: 'bg-gradient-to-r from-accent via-accent to-accent-hover text-darkbg shadow-xl shadow-accent/30 border-2 border-accent ring-2 ring-accent/20 ring-offset-2 ring-offset-darkbg',
    interactive: 'bg-gradient-to-r from-[#2a2a2a] via-[#2d2d2d] to-[#2a2a2a] text-gray-300 border-2 border-gray-600/50 hover:from-[#333333] hover:via-[#363636] hover:to-[#333333] hover:text-white hover:border-gray-500/70 hover:shadow-lg hover:shadow-gray-400/10 active:scale-95',
    compact: 'bg-accent/10 text-accent border border-accent/20',
    large: 'bg-gradient-to-r from-accent/15 to-accent/10 text-accent border border-accent/20 shadow-sm hover:shadow-accent/20 hover:from-accent/20 hover:to-accent/15'
};

const tagSizes: Record<TagSize, string> = {
    xs: 'px-2 py-0.5 text-xs font-medium',
    sm: 'px-3 py-1.5 text-xs font-medium',
    md: 'px-4 py-2.5 text-sm font-semibold tracking-wide',
    lg: 'px-5 py-3 text-sm font-semibold tracking-wide'
};

export const Tag: React.FC<TagProps> = ({ 
    children, 
    variant = 'default', 
    size = 'sm', 
    onClick, 
    className = '', 
    disabled = false,
    'aria-label': ariaLabel
}) => {
    const baseClasses = 'inline-flex items-center rounded-full transition-all duration-300 ease-out cursor-default select-none';
    const interactiveClasses = onClick && !disabled
        ? 'hover:scale-110 focus:scale-110 focus:outline-none cursor-pointer transform-gpu will-change-transform'
        : '';
    const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';
    
    const variantClasses = tagVariants[variant];
    const sizeClasses = tagSizes[size];
    
    const Component = onClick ? 'button' : 'span';
    
    return (
        <Component
            onClick={onClick && !disabled ? onClick : undefined}
            disabled={disabled}
            aria-label={ariaLabel}
            className={`
                ${baseClasses}
                ${variantClasses}
                ${sizeClasses}
                ${interactiveClasses}
                ${disabledClasses}
                ${className}
            `.trim().replace(/\s+/g, ' ')}
        >
            <span className="relative z-10">
                {children}
            </span>
            {variant === 'selected' && onClick && (
                <div className="absolute inset-0 bg-white/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
            )}
        </Component>
    );
};

// Specialized tag components for common use cases
export const SelectableTag: React.FC<{
    children: React.ReactNode;
    isSelected: boolean;
    onClick: () => void;
    size?: TagSize;
    className?: string;
}> = ({ children, isSelected, onClick, size = 'md', className = '' }) => (
    <Tag
        variant={isSelected ? 'selected' : 'interactive'}
        size={size}
        onClick={onClick}
        className={`group relative overflow-hidden ${className}`}
        aria-label={`${isSelected ? 'Deselect' : 'Select'} tag: ${children}`}
    >
        <span className="relative z-10 flex items-center gap-1.5">
            {isSelected && (
                <svg className="w-3 h-3 text-current" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
            )}
            {children}
        </span>
        {isSelected && (
            <div className="absolute inset-0 bg-gradient-to-r from-white/5 via-white/10 to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        )}
    </Tag>
);

export const DisplayTag: React.FC<{
    children: React.ReactNode;
    size?: TagSize;
    variant?: 'default' | 'large';
    className?: string;
}> = ({ children, size = 'sm', variant = 'default', className = '' }) => (
    <Tag
        variant={variant}
        size={size}
        className={className}
    >
        {children}
    </Tag>
);
