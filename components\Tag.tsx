import React from 'react';

export type TagVariant = 'default' | 'selected' | 'interactive' | 'compact' | 'large';
export type TagSize = 'xs' | 'sm' | 'md' | 'lg';

interface TagProps {
    children: React.ReactNode;
    variant?: TagVariant;
    size?: TagSize;
    onClick?: () => void;
    className?: string;
    disabled?: boolean;
    'aria-label'?: string;
}

const tagVariants: Record<TagVariant, string> = {
    default: 'bg-gradient-to-r from-accent/12 to-accent/8 text-accent/90 border border-accent/15 hover:from-accent/18 hover:to-accent/12 hover:text-accent',
    selected: 'bg-gradient-to-r from-accent to-accent-hover text-darkbg shadow-lg shadow-accent/25 border border-accent/20',
    interactive: 'bg-gradient-to-r from-gray-700/80 to-gray-600/80 text-gray-200 border border-gray-600/40 hover:from-gray-600/90 hover:to-gray-500/90 hover:text-white hover:border-gray-500/60 hover:shadow-md hover:shadow-gray-500/20',
    compact: 'bg-accent/10 text-accent border border-accent/20',
    large: 'bg-gradient-to-r from-accent/15 to-accent/10 text-accent border border-accent/20 shadow-sm hover:shadow-accent/20 hover:from-accent/20 hover:to-accent/15'
};

const tagSizes: Record<TagSize, string> = {
    xs: 'px-2 py-0.5 text-xs',
    sm: 'px-2.5 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-sm'
};

export const Tag: React.FC<TagProps> = ({ 
    children, 
    variant = 'default', 
    size = 'sm', 
    onClick, 
    className = '', 
    disabled = false,
    'aria-label': ariaLabel
}) => {
    const baseClasses = 'inline-flex items-center rounded-full font-medium transition-all duration-200 cursor-default';
    const interactiveClasses = onClick && !disabled 
        ? 'hover:scale-105 focus:scale-105 focus:outline-none focus:ring-2 focus:ring-accent/50 focus:ring-offset-2 focus:ring-offset-darkbg cursor-pointer' 
        : '';
    const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';
    
    const variantClasses = tagVariants[variant];
    const sizeClasses = tagSizes[size];
    
    const Component = onClick ? 'button' : 'span';
    
    return (
        <Component
            onClick={onClick && !disabled ? onClick : undefined}
            disabled={disabled}
            aria-label={ariaLabel}
            className={`
                ${baseClasses}
                ${variantClasses}
                ${sizeClasses}
                ${interactiveClasses}
                ${disabledClasses}
                ${className}
            `.trim().replace(/\s+/g, ' ')}
        >
            <span className="relative z-10">
                {children}
            </span>
            {variant === 'selected' && onClick && (
                <div className="absolute inset-0 bg-white/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
            )}
        </Component>
    );
};

// Specialized tag components for common use cases
export const SelectableTag: React.FC<{
    children: React.ReactNode;
    isSelected: boolean;
    onClick: () => void;
    size?: TagSize;
    className?: string;
}> = ({ children, isSelected, onClick, size = 'md', className = '' }) => (
    <Tag
        variant={isSelected ? 'selected' : 'interactive'}
        size={size}
        onClick={onClick}
        className={`group ${className}`}
        aria-label={`${isSelected ? 'Deselect' : 'Select'} tag: ${children}`}
    >
        {children}
    </Tag>
);

export const DisplayTag: React.FC<{
    children: React.ReactNode;
    size?: TagSize;
    variant?: 'default' | 'large';
    className?: string;
}> = ({ children, size = 'sm', variant = 'default', className = '' }) => (
    <Tag
        variant={variant}
        size={size}
        className={className}
    >
        {children}
    </Tag>
);
