/**
 * YouTube API Optimization Utilities
 * Implements efficient API usage strategies to minimize quota consumption
 */

import type { VideoDetails, ChannelDetails } from '../types';
import { API_USAGE_LIMITS } from './apiKeySecurity';

export interface SyncStrategy {
  lastSyncDate?: string;
  maxResults: number;
  syncMode: 'full' | 'incremental' | 'recent';
  daysBack?: number;
}

export interface QuotaEstimate {
  searchCost: number;
  channelsCost: number;
  videosCost: number;
  totalCost: number;
  canProceed: boolean;
  recommendation: string;
}

/**
 * Quota Manager for intelligent API usage
 */
export class QuotaManager {
  private static instance: QuotaManager;
  private quotaUsage: Map<string, number> = new Map();
  private lastReset: Date = new Date();
  
  static getInstance(): QuotaManager {
    if (!QuotaManager.instance) {
      QuotaManager.instance = new QuotaManager();
    }
    return QuotaManager.instance;
  }
  
  /**
   * Estimates the quota cost for a bulk operation
   */
  async estimateOperationCost(
    operation: 'search' | 'channels' | 'videos' | 'playlist',
    count: number
  ): Promise<number> {
    const costs = {
      search: API_USAGE_LIMITS.SEARCH_COST,
      channels: API_USAGE_LIMITS.CHANNELS_COST,
      videos: API_USAGE_LIMITS.VIDEOS_COST,
      playlist: API_USAGE_LIMITS.PLAYLIST_COST
    };
    
    // API allows batching up to 50 items per request
    const batchSize = operation === 'search' ? 1 : 50;
    const requestCount = Math.ceil(count / batchSize);
    
    return costs[operation] * requestCount;
  }
  
  /**
   * Estimates total cost for syncing multiple channels
   */
  async estimateSyncCost(
    channelCount: number,
    strategy: SyncStrategy
  ): Promise<QuotaEstimate> {
    const searchCost = await this.estimateOperationCost('search', 1); // Initial search
    const channelsCost = await this.estimateOperationCost('channels', channelCount);
    
    // Estimate videos based on sync strategy
    let videosPerChannel = 5; // Default recent videos
    if (strategy.syncMode === 'full') {
      videosPerChannel = 50; // More comprehensive sync
    } else if (strategy.syncMode === 'incremental') {
      videosPerChannel = 10; // Moderate sync
    }
    
    const videosCost = await this.estimateOperationCost('videos', channelCount * videosPerChannel);
    const totalCost = searchCost + channelsCost + videosCost;
    
    const canProceed = totalCost <= (API_USAGE_LIMITS.DAILY_QUOTA * 0.8); // Use 80% threshold
    
    let recommendation = '';
    if (!canProceed) {
      recommendation = 'Reduce channel count or use incremental sync mode';
    } else if (totalCost > (API_USAGE_LIMITS.DAILY_QUOTA * 0.5)) {
      recommendation = 'Consider using incremental sync to preserve quota';
    } else {
      recommendation = 'Operation is within safe quota limits';
    }
    
    return {
      searchCost,
      channelsCost,
      videosCost,
      totalCost,
      canProceed,
      recommendation
    };
  }
  
  /**
   * Checks if an operation can be performed within quota limits
   */
  async canPerformOperation(cost: number, currentUsage: number): Promise<boolean> {
    return (currentUsage + cost) <= (API_USAGE_LIMITS.DAILY_QUOTA * 0.9); // 90% threshold
  }
  
  /**
   * Schedules an operation with quota validation
   */
  async scheduleOperation<T>(
    operation: () => Promise<T>,
    cost: number,
    currentUsage: number
  ): Promise<T> {
    if (await this.canPerformOperation(cost, currentUsage)) {
      return await operation();
    } else {
      throw new Error(
        `Insufficient API quota. Operation cost: ${cost}, Current usage: ${currentUsage}, Daily limit: ${API_USAGE_LIMITS.DAILY_QUOTA}`
      );
    }
  }
}

/**
 * Optimized sync strategies for different use cases
 */
export const SyncStrategies = {
  /**
   * Recent sync - only fetch videos from last 7 days
   */
  recent: (daysBack: number = 7): SyncStrategy => ({
    syncMode: 'recent',
    maxResults: 10,
    daysBack,
    lastSyncDate: new Date(Date.now() - daysBack * 24 * 60 * 60 * 1000).toISOString()
  }),
  
  /**
   * Incremental sync - fetch videos since last sync
   */
  incremental: (lastSyncDate: string): SyncStrategy => ({
    syncMode: 'incremental',
    maxResults: 25,
    lastSyncDate
  }),
  
  /**
   * Full sync - comprehensive but quota-intensive
   */
  full: (): SyncStrategy => ({
    syncMode: 'full',
    maxResults: 50
  }),
  
  /**
   * Efficient sync - balanced approach
   */
  efficient: (): SyncStrategy => ({
    syncMode: 'recent',
    maxResults: 15,
    daysBack: 14,
    lastSyncDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString()
  })
};

/**
 * Optimized video fetching with intelligent batching
 */
export const optimizedVideoFetch = async (
  channelIds: string[],
  strategy: SyncStrategy,
  apiCall: (url: string, apiKey: any, cost: number) => Promise<any>
): Promise<VideoDetails[]> => {
  const quotaManager = QuotaManager.getInstance();
  const videos: VideoDetails[] = [];
  
  // Process channels in batches to manage quota
  const batchSize = 5; // Process 5 channels at a time
  
  for (let i = 0; i < channelIds.length; i += batchSize) {
    const batch = channelIds.slice(i, i + batchSize);
    
    // Estimate cost for this batch
    const batchCost = await quotaManager.estimateOperationCost('videos', batch.length * strategy.maxResults);
    
    console.log(`Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(channelIds.length/batchSize)}, estimated cost: ${batchCost}`);
    
    // Process batch with quota validation
    const batchVideos = await processBatch(batch, strategy, apiCall, batchCost);
    videos.push(...batchVideos);
    
    // Add small delay between batches to be respectful to API
    if (i + batchSize < channelIds.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  return videos;
};

/**
 * Process a batch of channels efficiently
 */
const processBatch = async (
  channelIds: string[],
  strategy: SyncStrategy,
  apiCall: (url: string, apiKey: any, cost: number) => Promise<any>,
  estimatedCost: number
): Promise<VideoDetails[]> => {
  const videos: VideoDetails[] = [];
  
  for (const channelId of channelIds) {
    try {
      const channelVideos = await fetchChannelVideosOptimized(channelId, strategy, apiCall);
      videos.push(...channelVideos);
    } catch (error) {
      console.error(`Failed to fetch videos for channel ${channelId}:`, error);
      // Continue with other channels instead of failing completely
    }
  }
  
  return videos;
};

/**
 * Optimized channel video fetching
 */
const fetchChannelVideosOptimized = async (
  channelId: string,
  strategy: SyncStrategy,
  apiCall: (url: string, apiKey: any, cost: number) => Promise<any>
): Promise<VideoDetails[]> => {
  const baseUrl = 'https://www.googleapis.com/youtube/v3/search';
  const params = new URLSearchParams({
    part: 'snippet',
    channelId: channelId,
    type: 'video',
    order: 'date',
    maxResults: Math.min(strategy.maxResults, 25).toString() // API limit is 50, but we use 25 for efficiency
  });
  
  // Add date filtering for incremental/recent syncs
  if (strategy.lastSyncDate) {
    params.append('publishedAfter', strategy.lastSyncDate);
  }
  
  const url = `${baseUrl}?${params.toString()}`;
  
  try {
    const response = await apiCall(url, null, API_USAGE_LIMITS.SEARCH_COST);
    
    if (response.items && response.items.length > 0) {
      return response.items.map(mapYouTubeItemToVideoDetails);
    }
    
    return [];
  } catch (error) {
    console.error(`API call failed for channel ${channelId}:`, error);
    return [];
  }
};

/**
 * Maps YouTube API response to VideoDetails
 */
const mapYouTubeItemToVideoDetails = (item: any): VideoDetails => {
  return {
    id: item.id.videoId,
    title: item.snippet.title,
    description: item.snippet.description,
    thumbnailUrl: item.snippet.thumbnails?.medium?.url || item.snippet.thumbnails?.default?.url || '',
    publishedAt: item.snippet.publishedAt,
    channelId: item.snippet.channelId,
    channelTitle: item.snippet.channelTitle,
    duration: '', // Would need additional API call to get duration
    viewCount: 0,  // Would need additional API call to get view count
    likeCount: 0,  // Would need additional API call to get like count
    commentCount: 0 // Would need additional API call to get comment count
  };
};

/**
 * Smart sync recommendation based on usage patterns
 */
export const getRecommendedSyncStrategy = (
  channelCount: number,
  lastSyncDate?: string,
  currentQuotaUsage: number = 0
): SyncStrategy => {
  const remainingQuota = API_USAGE_LIMITS.DAILY_QUOTA - currentQuotaUsage;
  
  // If quota is low, use minimal sync
  if (remainingQuota < 1000) {
    return SyncStrategies.recent(3); // Only last 3 days
  }
  
  // If many channels, use efficient strategy
  if (channelCount > 20) {
    return SyncStrategies.efficient();
  }
  
  // If we have a recent sync date, use incremental
  if (lastSyncDate) {
    const daysSinceSync = (Date.now() - new Date(lastSyncDate).getTime()) / (24 * 60 * 60 * 1000);
    
    if (daysSinceSync <= 1) {
      return SyncStrategies.recent(1); // Very recent sync
    } else if (daysSinceSync <= 7) {
      return SyncStrategies.incremental(lastSyncDate);
    }
  }
  
  // Default to recent strategy
  return SyncStrategies.recent(7);
};

/**
 * Quota usage analytics
 */
export const analyzeQuotaUsage = (
  operations: Array<{ type: string; cost: number; timestamp: Date }>
): {
  totalUsage: number;
  averageCostPerOperation: number;
  mostExpensiveOperations: Array<{ type: string; cost: number }>;
  recommendations: string[];
} => {
  const totalUsage = operations.reduce((sum, op) => sum + op.cost, 0);
  const averageCostPerOperation = totalUsage / operations.length;
  
  const operationCosts = operations.reduce((acc, op) => {
    acc[op.type] = (acc[op.type] || 0) + op.cost;
    return acc;
  }, {} as Record<string, number>);
  
  const mostExpensiveOperations = Object.entries(operationCosts)
    .map(([type, cost]) => ({ type, cost }))
    .sort((a, b) => b.cost - a.cost)
    .slice(0, 3);
  
  const recommendations: string[] = [];
  
  if (averageCostPerOperation > 200) {
    recommendations.push('Consider using more efficient sync strategies');
  }
  
  if (operationCosts.search > operationCosts.videos) {
    recommendations.push('Optimize search operations - they are consuming more quota than video fetching');
  }
  
  if (totalUsage > API_USAGE_LIMITS.DAILY_QUOTA * 0.8) {
    recommendations.push('Approaching daily quota limit - implement more aggressive caching');
  }
  
  return {
    totalUsage,
    averageCostPerOperation,
    mostExpensiveOperations,
    recommendations
  };
};
