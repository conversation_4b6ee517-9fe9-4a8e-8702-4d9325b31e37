/**
 * YouTube API Diagnostics Utility
 * Helps diagnose and troubleshoot YouTube API connection issues
 */

import { apiKeyService } from '../services/apiKeyService';
import { API_USAGE_LIMITS } from './apiKeySecurity';

export interface DiagnosticResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
}

export interface DiagnosticReport {
  overall: 'healthy' | 'warning' | 'critical';
  results: DiagnosticResult[];
  recommendations: string[];
}

/**
 * Performs comprehensive YouTube API diagnostics
 */
export const performYouTubeApiDiagnostics = async (): Promise<DiagnosticReport> => {
  const results: DiagnosticResult[] = [];
  const recommendations: string[] = [];

  // Test 1: Check if API keys exist
  try {
    const apiKeys = await apiKeyService.getApiKeys();
    if (apiKeys.length === 0) {
      results.push({
        test: 'API Key Availability',
        status: 'fail',
        message: 'No YouTube API keys found',
        details: { count: 0 }
      });
      recommendations.push('Add a YouTube API key in Settings');
    } else {
      results.push({
        test: 'API Key Availability',
        status: 'pass',
        message: `Found ${apiKeys.length} API key(s)`,
        details: { count: apiKeys.length, keys: apiKeys.map(k => ({ name: k.name, usage: k.usage })) }
      });
    }
  } catch (error) {
    results.push({
      test: 'API Key Availability',
      status: 'fail',
      message: `Failed to check API keys: ${error}`,
      details: { error: error instanceof Error ? error.message : String(error) }
    });
    recommendations.push('Check database connection and API key service');
  }

  // Test 2: Check active API key
  try {
    const activeKey = await apiKeyService.getActiveApiKey();
    results.push({
      test: 'Active API Key',
      status: 'pass',
      message: `Active key: ${activeKey.name} (Usage: ${activeKey.usage})`,
      details: { name: activeKey.name, usage: activeKey.usage, id: activeKey.id }
    });

    // Test 3: Check API key format
    if (activeKey.key.startsWith('AIza') && activeKey.key.length === 39) {
      results.push({
        test: 'API Key Format',
        status: 'pass',
        message: 'API key format is valid',
        details: { format: 'valid', length: activeKey.key.length }
      });
    } else {
      results.push({
        test: 'API Key Format',
        status: 'warning',
        message: 'API key format may be invalid',
        details: { format: 'questionable', length: activeKey.key.length, prefix: activeKey.key.substring(0, 4) }
      });
      recommendations.push('Verify your YouTube API key format (should start with "AIza" and be 39 characters long)');
    }

    // Test 4: Check quota usage
    const quotaPercentage = (activeKey.usage / API_USAGE_LIMITS.DAILY_QUOTA) * 100;
    if (quotaPercentage >= 90) {
      results.push({
        test: 'API Quota Usage',
        status: 'fail',
        message: `Quota usage is critical: ${quotaPercentage.toFixed(1)}%`,
        details: { usage: activeKey.usage, limit: API_USAGE_LIMITS.DAILY_QUOTA, percentage: quotaPercentage }
      });
      recommendations.push('API quota is nearly exhausted. Add another API key or wait until tomorrow.');
    } else if (quotaPercentage >= 70) {
      results.push({
        test: 'API Quota Usage',
        status: 'warning',
        message: `Quota usage is high: ${quotaPercentage.toFixed(1)}%`,
        details: { usage: activeKey.usage, limit: API_USAGE_LIMITS.DAILY_QUOTA, percentage: quotaPercentage }
      });
      recommendations.push('Consider adding another API key to avoid quota exhaustion.');
    } else {
      results.push({
        test: 'API Quota Usage',
        status: 'pass',
        message: `Quota usage is healthy: ${quotaPercentage.toFixed(1)}%`,
        details: { usage: activeKey.usage, limit: API_USAGE_LIMITS.DAILY_QUOTA, percentage: quotaPercentage }
      });
    }

  } catch (error) {
    results.push({
      test: 'Active API Key',
      status: 'fail',
      message: `Failed to get active API key: ${error}`,
      details: { error: error instanceof Error ? error.message : String(error) }
    });
    recommendations.push('Check API key configuration in Settings');
  }

  // Test 5: Test YouTube API connectivity
  try {
    const testUrl = 'https://www.googleapis.com/youtube/v3/search?part=snippet&q=test&type=channel&maxResults=1&key=test';
    const response = await fetch(testUrl.replace('key=test', 'key=invalid'));
    
    if (response.status === 400 || response.status === 403) {
      results.push({
        test: 'YouTube API Connectivity',
        status: 'pass',
        message: 'YouTube API is accessible (received expected error response)',
        details: { status: response.status, accessible: true }
      });
    } else {
      results.push({
        test: 'YouTube API Connectivity',
        status: 'warning',
        message: `Unexpected response from YouTube API: ${response.status}`,
        details: { status: response.status, accessible: true }
      });
    }
  } catch (error) {
    results.push({
      test: 'YouTube API Connectivity',
      status: 'fail',
      message: `Cannot reach YouTube API: ${error}`,
      details: { error: error instanceof Error ? error.message : String(error), accessible: false }
    });
    recommendations.push('Check internet connection and firewall settings');
  }

  // Test 6: Test actual API call with real key (if available)
  try {
    const activeKey = await apiKeyService.getActiveApiKey();
    const testApiUrl = `https://www.googleapis.com/youtube/v3/channels?part=snippet&id=UC_x5XG1OV2P6uZZ5FSM9Ttw&key=${activeKey.key}`;
    
    const response = await fetch(testApiUrl);
    const data = await response.json();
    
    if (response.ok && data.items && data.items.length > 0) {
      results.push({
        test: 'API Key Validation',
        status: 'pass',
        message: 'API key is working correctly',
        details: { response: 'success', itemsReturned: data.items.length }
      });
    } else if (response.status === 403) {
      const errorMessage = data.error?.message || 'Access denied';
      results.push({
        test: 'API Key Validation',
        status: 'fail',
        message: `API key validation failed: ${errorMessage}`,
        details: { status: response.status, error: errorMessage }
      });
      
      if (errorMessage.includes('quotaExceeded')) {
        recommendations.push('API quota exceeded. Wait until tomorrow or add another API key.');
      } else if (errorMessage.includes('keyInvalid')) {
        recommendations.push('API key is invalid. Please check your API key in Settings.');
      } else {
        recommendations.push('API key access denied. Check API key permissions and YouTube Data API v3 is enabled.');
      }
    } else {
      results.push({
        test: 'API Key Validation',
        status: 'fail',
        message: `API call failed with status ${response.status}`,
        details: { status: response.status, error: data.error }
      });
      recommendations.push('API key test failed. Check API key validity and permissions.');
    }
  } catch (error) {
    results.push({
      test: 'API Key Validation',
      status: 'fail',
      message: `Failed to test API key: ${error}`,
      details: { error: error instanceof Error ? error.message : String(error) }
    });
  }

  // Determine overall status
  const failCount = results.filter(r => r.status === 'fail').length;
  const warningCount = results.filter(r => r.status === 'warning').length;
  
  let overall: 'healthy' | 'warning' | 'critical';
  if (failCount > 0) {
    overall = 'critical';
  } else if (warningCount > 0) {
    overall = 'warning';
  } else {
    overall = 'healthy';
  }

  return {
    overall,
    results,
    recommendations
  };
};

/**
 * Quick diagnostic check for common issues
 */
export const quickDiagnostic = async (): Promise<string> => {
  try {
    const activeKey = await apiKeyService.getActiveApiKey();
    const quotaPercentage = (activeKey.usage / API_USAGE_LIMITS.DAILY_QUOTA) * 100;
    
    if (quotaPercentage >= 90) {
      return 'API quota nearly exhausted. Add another API key or wait until tomorrow.';
    }
    
    return 'API configuration appears healthy.';
  } catch (error) {
    if (error instanceof Error && error.message.includes('No API key found')) {
      return 'No YouTube API key configured. Please add an API key in Settings.';
    }
    return `API configuration issue: ${error}`;
  }
};
