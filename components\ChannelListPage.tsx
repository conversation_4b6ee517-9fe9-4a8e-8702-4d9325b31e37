


import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { RiArrowUpSLine, RiArrowDownSLine } from 'react-icons/ri';
import type { Channel, VideoDetails, ChannelDetails, ChannelDisplayData } from '../types';
import { youtubeService } from '../services/youtubeService';
import { LoadingSpinner } from './LoadingSpinner';
import { BackArrowIcon, PlusIcon, TrashIcon, SearchIcon, RefreshIcon, MinusIcon, ExportIcon } from './Icons';
import { VideoCard } from './VideoCard';
import { ViewsFilter } from './ViewsFilter';
import { SubscribersFilter } from './SubscribersFilter';
import { formatNumber, formatChannelAge, parseISO8601DurationToMinutes } from '../lib/utils';
import { VideoDurationMinutesFilter } from './VideoDurationMinutesFilter';
import { ChannelNumericFilter } from './ChannelNumericFilter';
import { YouTubeApiDiagnostics } from './YouTubeApiDiagnostics';

// --- Types & Constants ---
type GridLayout = '2-grid' | '4-grid' | '6-grid';
const GRID_LAYOUTS: GridLayout[] = ['2-grid', '4-grid', '6-grid'];
const GRID_LAYOUT_CLASSES: Record<GridLayout, string> = {
    '2-grid': 'grid-cols-1 md:grid-cols-2 gap-6',
    '4-grid': 'grid-cols-2 md:grid-cols-4 gap-4',
    '6-grid': 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3',
};
const LAYOUT_NUMBER_MAP: Record<GridLayout, number> = { '2-grid': 2, '4-grid': 4, '6-grid': 6 };

// --- Custom Hook for Grid Layout ---
const useGridLayout = (listId: string) => {
    const getInitialLayout = (): GridLayout => {
        try {
            const savedLayout = localStorage.getItem(`video_grid_layout_${listId}`);
            return GRID_LAYOUTS.includes(savedLayout as GridLayout) ? (savedLayout as GridLayout) : '4-grid';
        } catch (e) {
            return '4-grid';
        }
    };

    const [layout, setLayout] = useState<GridLayout>(getInitialLayout);

    const updateLayout = useCallback((newLayout: GridLayout) => {
        setLayout(newLayout);
        try {
            localStorage.setItem(`video_grid_layout_${listId}`, newLayout);
        } catch (e) {
            console.warn("Could not save layout to localStorage", e);
        }
    }, [listId]);

    const changeLayout = (direction: 'increase' | 'decrease') => {
        const currentIndex = GRID_LAYOUTS.indexOf(layout);
        if (direction === 'increase' && currentIndex < GRID_LAYOUTS.length - 1) {
            updateLayout(GRID_LAYOUTS[currentIndex + 1]);
        }
        if (direction === 'decrease' && currentIndex > 0) {
            updateLayout(GRID_LAYOUTS[currentIndex - 1]);
        }
    };

    return { layout, changeLayout };
};


// --- Grid Controls Component ---
const GridControls: React.FC<{ layout: GridLayout; onChangeLayout: (dir: 'increase' | 'decrease') => void; disabled?: boolean; }> = ({ layout, onChangeLayout, disabled }) => {
    const currentNumber = LAYOUT_NUMBER_MAP[layout];
    const canDecrease = layout !== GRID_LAYOUTS[0];
    const canIncrease = layout !== GRID_LAYOUTS[GRID_LAYOUTS.length - 1];
    
    return (
        <div className="flex items-center space-x-1 bg-[#1a1a1a] border border-dark-border rounded-lg p-1">
            <button 
                onClick={() => onChangeLayout('decrease')} 
                disabled={!canDecrease || disabled}
                className="p-1.5 text-gray-400 rounded-md hover:bg-gray-700 hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="Decrease grid columns"
            >
                <MinusIcon className="w-4 h-4" />
            </button>
            <span className="px-2 text-sm font-semibold text-white w-5 text-center">{currentNumber}</span>
             <button 
                onClick={() => onChangeLayout('increase')} 
                disabled={!canIncrease || disabled}
                className="p-1.5 text-gray-400 rounded-md hover:bg-gray-700 hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="Increase grid columns"
            >
                <PlusIcon className="w-4 h-4" />
            </button>
        </div>
    );
};

// --- Main Page Component ---
interface ChannelListPageProps {
    list: Channel;
    onBack: () => void;
    onAddChannelsClick: () => void;
    onRemoveChannelsFromList: (listId: string, channelYoutubeIdsToRemove: string[]) => Promise<void>;
    onSaveVideoClick: (videoUrl: string) => void;
    onNavigateToSettings?: () => void;
}

const TabButton: React.FC<{ label: string; isActive: boolean; onClick: () => void; }> = ({ label, isActive, onClick }) => (
    <button
        onClick={onClick}
        className={`px-4 py-3 text-sm font-semibold transition-colors focus:outline-none ${
            isActive 
                ? 'text-accent border-b-2 border-accent'
                : 'text-gray-400 hover:text-white border-b-2 border-transparent'
        }`}
    >
        {label}
    </button>
);


export const ChannelListPage: React.FC<ChannelListPageProps> = ({ list, onBack, onAddChannelsClick, onRemoveChannelsFromList, onSaveVideoClick, onNavigateToSettings }) => {
    // --- Page State ---
    const [view, setView] = useState<'popularVideos' | 'manageChannels'>('popularVideos');
    const [error, setError] = useState<string | null>(null);
    const [refreshError, setRefreshError] = useState<string | null>(null);
    const [showDiagnostics, setShowDiagnostics] = useState<boolean>(false);
    const { layout, changeLayout } = useGridLayout(list.id);

    // --- Popular Videos State ---
    const [videos, setVideos] = useState<VideoDetails[]>([]);
    const [loadingVideos, setLoadingVideos] = useState<boolean>(true);
    const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
    const [viewFilter, setViewFilter] = useState<{ min: number; max: number | null }>({ min: 0, max: null });
    const [subscribersFilter, setSubscribersFilter] = useState<{ min: number; max: number | null }>({ min: 0, max: null });
    const [durationFilter, setDurationFilter] = useState<{ min: number; max: number | null }>({ min: 1, max: null });
    
    // --- Channel Management State ---
    const [channelDetails, setChannelDetails] = useState<ChannelDetails[]>([]);
    const [loadingChannels, setLoadingChannels] = useState<boolean>(true);
    const [selection, setSelection] = useState<Set<string>>(new Set());
    const [isDeleting, setIsDeleting] = useState(false);
    const [filterQuery, setFilterQuery] = useState('');
    const [sortConfig, setSortConfig] = useState<{ key: ManageSortKey, direction: 'asc' | 'desc' }>({ key: 'viewCount', direction: 'desc' });
    const [channelViewFilter, setChannelViewFilter] = useState<{ min: number; max: number | null }>({ min: 0, max: null });
    const [channelSubscribersFilter, setChannelSubscribersFilter] = useState<{ min: number; max: number | null }>({ min: 0, max: null });
    const [channelVideoCountFilter, setChannelVideoCountFilter] = useState<{ min: number; max: number | null }>({ min: 0, max: null });

    // Virtual scroll state
    type ManageSortKey = keyof Pick<ChannelDisplayData, 'viewCount' | 'subscriberCount' | 'videoCount' | 'name' | 'channelAge'>;
    const scrollContainerRef = useRef<HTMLDivElement>(null);
    const [scrollTop, setScrollTop] = useState(0);
    const ROW_HEIGHT = 72; // Fixed row height for virtual scrolling

    const channelIdsInList = useMemo(() => list.youtubeId?.split(',').filter(Boolean) || [], [list.youtubeId]);

    const fetchVideoData = useCallback(async () => {
        try {
            setLoadingVideos(true);
            const data = await youtubeService.getPopularVideosForListFromDb(list);
            setVideos(data);
        } catch (err) {
            setError('Failed to fetch video details from the database.');
            console.error(err);
        } finally {
            setLoadingVideos(false);
        }
    }, [list]);
    
    const fetchChannelDetails = useCallback(async () => {
        if (channelIdsInList.length === 0) {
            setChannelDetails([]);
            setLoadingChannels(false);
            return;
        }
        try {
            setLoadingChannels(true);
            setError(null); // Clear previous errors
            const data = await youtubeService.getChannelsInfoByIds(channelIdsInList);
            setChannelDetails(data);
        } catch (err) {
            console.error('Failed to fetch channel details:', err);

            // Provide more specific error messages
            let errorMessage = 'Failed to fetch channel details.';
            if (err instanceof Error) {
                if (err.message.includes('No YouTube API key')) {
                    errorMessage = 'No YouTube API key configured. Please add an API key in Settings to view channel details.';
                } else if (err.message.includes('quota exceeded')) {
                    errorMessage = 'YouTube API quota exceeded. Please try again tomorrow or add another API key in Settings.';
                } else if (err.message.includes('invalid') || err.message.includes('expired')) {
                    errorMessage = 'YouTube API key is invalid or expired. Please check your API key in Settings.';
                } else if (err.message.includes('Network error')) {
                    errorMessage = 'Network error: Unable to connect to YouTube API. Please check your internet connection.';
                } else {
                    errorMessage = `Failed to fetch channel details: ${err.message}`;
                }
            }

            setError(errorMessage);
        } finally {
            setLoadingChannels(false);
        }
    }, [channelIdsInList]);
    
    const handleRefreshVideos = useCallback(async () => {
        setIsRefreshing(true);
        setRefreshError(null);
        try {
            await youtubeService.syncPopularVideosForList(list);
            await fetchVideoData();
        } catch (err) {
            setRefreshError('Failed to refresh. You may have exceeded your API quota.');
            console.error(err);
        } finally {
            setIsRefreshing(false);
        }
    }, [list, fetchVideoData]);

    const handleViewFilterChange = useCallback((min: number, max: number | null) => {
        setViewFilter({ min, max });
    }, []);

    const handleSubscribersFilterChange = useCallback((min: number, max: number | null) => {
        setSubscribersFilter({ min, max });
    }, []);
    
    const handleDurationFilterChange = useCallback((min: number, max: number | null) => {
        setDurationFilter({ min, max });
    }, []);

    const handleChannelViewFilterChange = useCallback((min: number, max: number | null) => {
        setChannelViewFilter({ min, max });
    }, []);
    const handleChannelSubscribersFilterChange = useCallback((min: number, max: number | null) => {
        setChannelSubscribersFilter({ min, max });
    }, []);
    const handleChannelVideoCountFilterChange = useCallback((min: number, max: number | null) => {
        setChannelVideoCountFilter({ min, max });
    }, []);

    useEffect(() => {
        fetchVideoData();
        fetchChannelDetails();
    }, [fetchVideoData, fetchChannelDetails]);

    const filteredVideos = useMemo(() => {
        return videos.filter(video => {
            const meetsMinViews = video.viewCount >= viewFilter.min;
            const meetsMaxViews = viewFilter.max === null || video.viewCount <= viewFilter.max;
            
            const meetsMinSubs = video.subscriberCount >= subscribersFilter.min;
            const meetsMaxSubs = subscribersFilter.max === null || video.subscriberCount <= subscribersFilter.max;

            const durationInMinutes = parseISO8601DurationToMinutes(video.duration);
            const meetsMinDuration = durationInMinutes >= durationFilter.min;
            const meetsMaxDuration = durationFilter.max === null || durationInMinutes <= durationFilter.max;

            return meetsMinViews && meetsMaxViews && meetsMinSubs && meetsMaxSubs && meetsMinDuration && meetsMaxDuration;
        });
    }, [videos, viewFilter, subscribersFilter, durationFilter]);

    const sortedVideos = useMemo(() => {
        return [...filteredVideos].sort((a, b) => b.viewCount - a.viewCount);
    }, [filteredVideos]);

    const handleRemoveSelectedChannels = async () => {
        if (selection.size === 0) return;
        setIsDeleting(true);
        try {
            await onRemoveChannelsFromList(list.id, Array.from(selection));
            setSelection(new Set());
        } catch (err) {
            alert('Failed to remove channels: ' + (err as Error).message);
        } finally {
            setIsDeleting(false);
        }
    };
    
    // --- Manage Channels Derived Data ---
    const displayData = useMemo((): ChannelDisplayData[] => {
        return channelDetails.map(ch => ({
            ...ch,
            channelAge: formatChannelAge(ch.publishedAt),
        }));
    }, [channelDetails]);

    const sortedAndFilteredData = useMemo(() => {
        let data = [...displayData].filter(ch => {
            if (filterQuery && !ch.name.toLowerCase().includes(filterQuery.toLowerCase())) return false;
            
            if (ch.viewCount < channelViewFilter.min) return false;
            if (channelViewFilter.max !== null && ch.viewCount > channelViewFilter.max) return false;
            
            if (ch.subscriberCount < channelSubscribersFilter.min) return false;
            if (channelSubscribersFilter.max !== null && ch.subscriberCount > channelSubscribersFilter.max) return false;

            if (ch.videoCount < channelVideoCountFilter.min) return false;
            if (channelVideoCountFilter.max !== null && ch.videoCount > channelVideoCountFilter.max) return false;
            
            return true;
        });

        if (sortConfig.key) {
            data.sort((a, b) => {
                const aVal = a[sortConfig.key];
                const bVal = b[sortConfig.key];
                const direction = sortConfig.direction === 'asc' ? 1 : -1;
                if (typeof aVal === 'string' && typeof bVal === 'string') return aVal.localeCompare(bVal) * direction;
                if (typeof aVal === 'number' && typeof bVal === 'number') return (aVal - bVal) * direction;
                return 0;
            });
        }
        return data;
    }, [displayData, filterQuery, sortConfig, channelViewFilter, channelSubscribersFilter, channelVideoCountFilter]);

    const totalData = useMemo(() => {
        const channelsToSum = selection.size > 0 
            ? sortedAndFilteredData.filter(ch => selection.has(ch.youtubeId)) 
            : sortedAndFilteredData;
        
        return channelsToSum.reduce((acc, ch) => {
            acc.views += ch.viewCount;
            acc.subs += ch.subscriberCount;
            acc.uploads += ch.videoCount;
            return acc;
        }, { views: 0, subs: 0, uploads: 0, count: channelsToSum.length });
    }, [selection, sortedAndFilteredData]);

    const handleSort = (key: ManageSortKey) => {
        setSortConfig(prev => ({
            key,
            direction: prev.key === key && prev.direction === 'desc' ? 'asc' : 'desc'
        }));
    };
    
    const handleSelectAll = () => {
        if (selection.size === sortedAndFilteredData.length) {
            setSelection(new Set());
        } else {
            setSelection(new Set(sortedAndFilteredData.map(ch => ch.youtubeId)));
        }
    };
    
    const toggleSelection = (youtubeId: string) => {
        setSelection(prev => {
            const newSet = new Set(prev);
            if (newSet.has(youtubeId)) {
                newSet.delete(youtubeId);
            } else {
                newSet.add(youtubeId);
            }
            return newSet;
        });
    };
    
    const handleExportCsv = () => {
        if (sortedAndFilteredData.length === 0) {
            alert("No data to export.");
            return;
        }
        
        const headers = ['Name', 'Subscribers', 'Views', 'Uploads', 'Channel Age', 'YouTube ID', 'Published Date'];
        const rows = sortedAndFilteredData.map(ch => [
            `"${ch.name.replace(/"/g, '""')}"`,
            ch.subscriberCount,
            ch.viewCount,
            ch.videoCount,
            ch.channelAge,
            ch.youtubeId,
            ch.publishedAt,
        ]);

        const csvContent = "data:text/csv;charset=utf-8," 
            + [headers.join(','), ...rows.map(r => r.join(','))].join('\n');

        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", `${list.name}_channels_export.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    // Virtual scroll calculations
    const containerHeight = scrollContainerRef.current?.clientHeight || 0;
    const startIndex = Math.floor(scrollTop / ROW_HEIGHT);
    const endIndex = Math.min(sortedAndFilteredData.length, startIndex + Math.ceil(containerHeight / ROW_HEIGHT) + 2); // Add buffer
    const visibleData = sortedAndFilteredData.slice(startIndex, endIndex);

    // --- RENDER FUNCTIONS ---
    
    const renderVideosContent = () => {
        if (loadingVideos) return <div className="flex-1 flex items-center justify-center"><LoadingSpinner /></div>;
        if (error && !loadingVideos && videos.length === 0) return <div className="flex-1 flex items-center justify-center text-red-500">{error}</div>;
        if (channelIdsInList.length === 0) return (
             <div className="text-center py-16 text-gray-500">
                <p>This list has no channels.</p>
                <button onClick={onAddChannelsClick} className="mt-4 text-sm bg-accent text-darkbg font-semibold px-4 py-2 rounded-md hover:bg-accent-hover transition-colors">
                    Add Channels
                </button>
            </div>
        );
        if (videos.length === 0 && !loadingVideos) return (
             <div className="text-center py-16 text-gray-500">
                <p>No videos found for the channels in this list.</p>
                <p className="mt-2 text-sm">This could be because data is still syncing, or the channels have no public videos.</p>
            </div>
        );
         if (sortedVideos.length === 0 && (viewFilter.min > 0 || viewFilter.max !== null || subscribersFilter.min > 0 || subscribersFilter.max !== null || durationFilter.min > 1 || durationFilter.max !== null)) return (
             <div className="text-center py-16 text-gray-500">
                <p>No videos match the current filters.</p>
                <p className="mt-2 text-sm">Try adjusting the filter ranges.</p>
            </div>
        );
        
        return (
            <div className={`grid ${GRID_LAYOUT_CLASSES[layout]} transition-all duration-300`}>
                {sortedVideos.map(video => (
                    <VideoCard 
                        key={video.id} 
                        video={video} 
                        layout={layout}
                        onSaveClick={() => onSaveVideoClick(`https://www.youtube.com/watch?v=${video.id}`)}
                    />
                ))}
            </div>
        );
    };

    const renderChannelsContent = () => {
        if (loadingChannels) return <div className="flex-1 flex items-center justify-center pt-10"><LoadingSpinner /></div>;
        if (error) return (
            <div className="flex-1 flex items-center justify-center">
                <div className="text-center max-w-md">
                    <div className="text-red-400 mb-4 p-4 bg-red-900/20 border border-red-700 rounded-lg">
                        <p className="font-medium mb-2">Channel Details Error</p>
                        <p className="text-sm text-red-300">{error}</p>
                    </div>
                    <div className="flex gap-3 justify-center">
                        <button
                            onClick={() => setShowDiagnostics(true)}
                            className="bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 transition-colors"
                        >
                            Run Diagnostics
                        </button>
                        {onNavigateToSettings && (
                            <button
                                onClick={onNavigateToSettings}
                                className="bg-accent text-darkbg px-4 py-2 rounded-md font-medium hover:bg-accent-hover transition-colors"
                            >
                                Go to Settings
                            </button>
                        )}
                        <button
                            onClick={() => {
                                setError(null);
                                fetchChannelDetails();
                            }}
                            className="bg-gray-600 text-white px-4 py-2 rounded-md font-medium hover:bg-gray-700 transition-colors"
                        >
                            Retry
                        </button>
                    </div>
                </div>
            </div>
        );
        if (channelDetails.length === 0) return (
            <div className="text-center py-16 text-gray-500">
                <p>This list has no channels.</p>
                <button onClick={onAddChannelsClick} className="mt-4 text-sm bg-accent text-darkbg font-semibold px-4 py-2 rounded-md hover:bg-accent-hover transition-colors">
                    Add Channels
                </button>
            </div>
        );

        const SortableHeader: React.FC<{ sortKey: ManageSortKey, label: string, className?: string }> = ({ sortKey, label, className }) => (
            <th onClick={() => handleSort(sortKey)} className={`p-3 text-sm font-semibold text-gray-300 tracking-wider cursor-pointer hover:bg-[#2a2a2a] transition-colors ${className}`}>
                <div className="flex items-center gap-1">
                    {label}
                    {sortConfig.key === sortKey && (sortConfig.direction === 'desc' ? <RiArrowDownSLine className="w-4 h-4 text-accent"/> : <RiArrowUpSLine className="w-4 h-4 text-accent"/>)}
                </div>
            </th>
        );
        
        return (
            <div className="flex flex-col flex-1 min-h-0 bg-[#0d0d0d]">
                <div className="flex flex-col gap-4 p-4 border-b border-dark-border">
                    <div className="flex flex-wrap gap-4 items-center">
                        <div className="relative flex-grow" style={{flexBasis: '300px'}}>
                            <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-500" />
                            <input
                                type="text"
                                placeholder="Filter by name..."
                                value={filterQuery}
                                onChange={e => setFilterQuery(e.target.value)}
                                className="w-full bg-[#1a1a1a] border border-dark-border rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-accent"
                            />
                        </div>
                        <div className="flex items-center gap-2">
                             <button
                                onClick={handleRemoveSelectedChannels}
                                disabled={selection.size === 0 || isDeleting}
                                className="flex items-center gap-2 px-3 py-2 text-sm font-semibold rounded-lg bg-red-500/10 text-red-400 hover:bg-red-500/20 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                <TrashIcon className="w-4 h-4" />
                                {isDeleting ? 'Deleting...' : `Delete (${selection.size})`}
                            </button>
                            <button
                                onClick={handleExportCsv}
                                disabled={sortedAndFilteredData.length === 0}
                                className="flex items-center gap-2 px-3 py-2 text-sm font-semibold rounded-lg bg-accent/10 text-accent hover:bg-accent/20 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                <ExportIcon className="w-4 h-4" />
                                <span>Export</span>
                            </button>
                        </div>
                    </div>
                     <div className="flex flex-wrap items-center gap-4">
                        <ChannelNumericFilter label="Subs" listId={list.id} filterKey="channel-subs" onFilterChange={handleChannelSubscribersFilterChange} />
                        <ChannelNumericFilter label="Views" listId={list.id} filterKey="channel-views" onFilterChange={handleChannelViewFilterChange} />
                        <ChannelNumericFilter label="Uploads" listId={list.id} filterKey="channel-uploads" onFilterChange={handleChannelVideoCountFilterChange} />
                     </div>
                </div>

                <div className="flex-1 overflow-y-auto" ref={scrollContainerRef} onScroll={e => setScrollTop(e.currentTarget.scrollTop)}>
                    <div className="relative" style={{ height: `${sortedAndFilteredData.length * ROW_HEIGHT}px` }}>
                        <table className="w-full text-left border-separate border-spacing-0" style={{ position: 'absolute', top: `${startIndex * ROW_HEIGHT}px`, width: '100%' }}>
                            <thead className="sticky top-0 bg-[#0d0d0d] z-10">
                                <tr>
                                    <th className="p-3 w-10"><input type="checkbox" className="h-4 w-4 bg-dark-card border-gray-600 rounded text-accent focus:ring-accent" checked={selection.size > 0 && selection.size === sortedAndFilteredData.length} onChange={handleSelectAll} /></th>
                                    <SortableHeader sortKey="name" label={`Channel (${sortedAndFilteredData.length})`} className="w-1/3"/>
                                    <SortableHeader sortKey="subscriberCount" label="Subscribers"/>
                                    <SortableHeader sortKey="viewCount" label="Total views"/>
                                    <SortableHeader sortKey="videoCount" label="Uploads"/>
                                    <SortableHeader sortKey="channelAge" label="Channel age"/>
                                </tr>
                            </thead>
                            <tbody>
                                {visibleData.length === 0 && (
                                    <tr>
                                        <td colSpan={6} className="text-center text-gray-500 py-16">
                                            No channels match the current filters.
                                        </td>
                                    </tr>
                                )}
                                {visibleData.map((channel) => (
                                    <tr key={channel.youtubeId} className={`transition-colors ${selection.has(channel.youtubeId) ? 'bg-accent/5' : 'odd:bg-black/20 even:bg-dark-card/30 hover:bg-dark-card'}`} style={{height: `${ROW_HEIGHT}px`}}>
                                        <td className="p-3 border-b border-dark-border"><input type="checkbox" className="h-4 w-4 bg-dark-card border-gray-600 rounded text-accent focus:ring-accent" checked={selection.has(channel.youtubeId)} onChange={() => toggleSelection(channel.youtubeId)} /></td>
                                        <td className="p-3 border-b border-dark-border">
                                            <div className="flex items-center gap-3">
                                                <img src={channel.avatar} alt={channel.name} className="w-10 h-10 rounded-full flex-shrink-0" />
                                                <div className='min-w-0'>
                                                    <p className="font-semibold text-white truncate">{channel.name}</p>
                                                    <p className="text-sm text-gray-400 truncate">{channel.handle || ' '}</p>
                                                </div>
                                            </div>
                                        </td>
                                        <td className="p-3 border-b border-dark-border">{formatNumber(channel.subscriberCount)}</td>
                                        <td className="p-3 border-b border-dark-border">{formatNumber(channel.viewCount)}</td>
                                        <td className="p-3 border-b border-dark-border">{formatNumber(channel.videoCount)}</td>
                                        <td className="p-3 border-b border-dark-border">{channel.channelAge}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>

                 <div className="flex-shrink-0 border-t border-dark-border bg-[#0d0d0d]/80 backdrop-blur-sm p-3">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center max-w-4xl mx-auto">
                        <div>
                            <p className="text-sm text-gray-400">{selection.size > 0 ? 'Selected Channels' : 'Filtered Channels'}</p>
                            <p className="text-xl font-bold text-white">{formatNumber(totalData.count)}</p>
                        </div>
                        <div>
                            <p className="text-sm text-gray-400">Total Subscribers</p>
                            <p className="text-xl font-bold text-white">{formatNumber(totalData.subs)}</p>
                        </div>
                        <div>
                            <p className="text-sm text-gray-400">Total Views</p>
                            <p className="text-xl font-bold text-white">{formatNumber(totalData.views)}</p>
                        </div>
                        <div>
                            <p className="text-sm text-gray-400">Total Uploads</p>
                            <p className="text-xl font-bold text-white">{formatNumber(totalData.uploads)}</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    };
    
    return (
        <>
        <div className="flex-1 p-4 sm:p-6 lg:p-8 flex flex-col min-h-0">
            <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                <div className="flex items-center space-x-4 min-w-0">
                    <button onClick={onBack} className="p-2 rounded-full hover:bg-dark-card transition-colors flex-shrink-0" aria-label="Back to channel lists">
                        <BackArrowIcon className="w-6 h-6" />
                    </button>
                    <h2 className="text-3xl font-bold text-white truncate" title={list.name}>{list.name}</h2>
                </div>
                 <button 
                    onClick={onAddChannelsClick} 
                    className="flex-shrink-0 flex items-center gap-2 px-4 py-2 text-sm font-semibold rounded-lg bg-accent/20 text-accent hover:bg-accent/30 transition-colors"
                >
                    <PlusIcon className="w-4 h-4" />
                    Add Channels
                </button>
            </header>

            <div className="border-b border-dark-border">
                <div className="flex">
                    <TabButton label="Popular Videos" isActive={view === 'popularVideos'} onClick={() => setView('popularVideos')} />
                    <TabButton label={`Manage Channels`} isActive={view === 'manageChannels'} onClick={() => setView('manageChannels')} />
                </div>
            </div>

            {view === 'popularVideos' && (
                <div className="flex flex-col flex-1 min-h-0 pt-6">
                    <div className="flex flex-col sm:flex-row justify-between items-start gap-4 mb-4">
                        <div className="flex flex-wrap items-start gap-4">
                            <ViewsFilter onFilterChange={handleViewFilterChange} listId={list.id} />
                            <SubscribersFilter onFilterChange={handleSubscribersFilterChange} listId={list.id} />
                            <VideoDurationMinutesFilter onFilterChange={handleDurationFilterChange} listId={list.id} />
                        </div>
                        <div className="flex items-center gap-4 self-end sm:self-auto">
                           <GridControls layout={layout} onChangeLayout={changeLayout} disabled={isRefreshing || videos.length === 0} />
                           <div className="flex flex-col items-end">
                                <button
                                    onClick={handleRefreshVideos}
                                    disabled={isRefreshing || channelIdsInList.length === 0}
                                    className="flex items-center gap-2 px-3 py-1.5 text-sm font-semibold rounded-lg bg-dark-card border border-dark-border text-gray-300 hover:border-gray-600 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                >
                                    {isRefreshing ? <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div> : <RefreshIcon className="w-4 h-4" />}
                                    {isRefreshing ? 'Refreshing...' : 'Refresh Videos'}
                                </button>
                                {refreshError && <p className="text-red-400 text-xs mt-2">{refreshError}</p>}
                           </div>
                        </div>
                    </div>
                     {videos.length > 0 && (viewFilter.min > 0 || viewFilter.max !== null || subscribersFilter.min > 0 || subscribersFilter.max !== null || durationFilter.min > 1 || durationFilter.max !== null) && 
                        <p className="text-sm text-gray-400 mb-4 -mt-2">
                            Showing {sortedVideos.length} of {videos.length} videos.
                        </p>
                    }
                    <div className="flex-1 overflow-y-auto -mr-4 pr-4">
                        {renderVideosContent()}
                    </div>
                </div>
            )}

            {view === 'manageChannels' && (
                <div className="flex-1 mt-2 flex flex-col min-h-0">
                    {renderChannelsContent()}
                </div>
            )}
        </div>

        {/* YouTube API Diagnostics Modal */}
        {showDiagnostics && (
            <YouTubeApiDiagnostics
                onClose={() => setShowDiagnostics(false)}
                onNavigateToSettings={() => {
                    setShowDiagnostics(false);
                    onNavigateToSettings?.();
                }}
            />
        )}
        </>
    );
};