

export interface Channel {
  id: string;
  name: string; // Represents the name of the channel list
  youtubeId?: string; // Now a comma-separated string of channel IDs for a list
  createdAt: string;
  updatedAt:string;
  avatar?: string; // Can be a placeholder for a list
  isPinned?: boolean;
}

export interface SearchResultChannel {
  youtubeId: string;
  name:string;
  avatar: string;
  handle?: string;
  subscriberCount?: number;
}

export interface Statistic {
  subscriberCount: number;
  viewCount: number;
  videoCount: number;
  recordedAt: string;
}

export interface ChannelStats extends Channel {
  statistics: Statistic[];
}

export interface Folder {
  id: string;
  name: string;
  video_count?: number;
}

export interface Tag {
  id: string;
  name: string;
}

export interface ApiKey {
  id: string;
  name: string;
  key: string;
  usage: number;
}

// New type for detailed channel info in the list view
export interface ChannelDetails {
  youtubeId: string;
  name: string;
  avatar: string;
  subscriberCount: number;
  viewCount: number;
  videoCount: number;
  publishedAt: string;
  handle?: string;
}

// New type for the advanced 'Manage Channels' table, extending ChannelDetails
export interface ChannelDisplayData extends ChannelDetails {
  channelAge: string;
}


export interface VideoDetails {
  id: string; // videoId
  title: string;
  thumbnailUrl: string;
  viewCount: number;
  publishedAt: string;
  duration: string; // ISO 8601
  channelId: string;
  channelTitle: string;
  channelAvatar: string;
  subscriberCount: number;
}

export interface SavedVideo {
    id: string;
    created_at: string;
    folder_id: string;
    video_url: string;
    video_title?: string;
    video_thumbnail_url?: string;
    channel_name?: string;
    notes?: string;
}

export interface SavedVideoWithTags extends SavedVideo {
    tags: Tag[];
}

export interface SavedVideoPostData {
    folder_id: string;
    video_url: string;
    video_title?: string;
    video_thumbnail_url?: string;
    channel_name?: string;
    notes?: string;
}