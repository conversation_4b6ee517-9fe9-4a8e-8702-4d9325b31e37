# VirSnapp - YouTube Channel Tracking Application

A secure React application for tracking YouTube channels, managing saved videos, and monitoring channel statistics.

## 🔒 Security Features

This application implements comprehensive security measures:

- **Environment Variable Protection**: Credentials stored securely in environment variables
- **API Key Encryption**: YouTube API keys encrypted before database storage
- **Input Validation**: All user inputs validated and sanitized
- **Database Security**: Row Level Security (RLS) policies and constraints
- **Security Monitoring**: Real-time security event logging and health checks

For detailed security information, see [SECURITY.md](SECURITY.md).

## 🚀 Quick Start

**Prerequisites:** Node.js 16+ and a Supabase account

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Setup
Copy the environment template and configure your credentials:
```bash
cp .env.example .env.local
```

Edit `.env.local` with your actual values:
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. Database Setup
Apply security policies to your Supabase database:
1. Go to your Supabase project dashboard
2. Navigate to "SQL Editor"
3. Run the contents of `database/security-policies.sql`

### 4. Run the Application
```bash
npm run dev
```

## 📋 Features

- **Channel Management**: Create and manage lists of YouTube channels
- **Video Tracking**: Save and organize videos with tags and notes
- **Statistics**: View channel growth and performance metrics
- **Search**: Find channels by URL, handle, or search terms
- **Folders & Tags**: Organize saved content with custom categories
- **API Management**: Secure YouTube API key management with usage monitoring

## 🛡️ Security Implementation

### Environment Variables
- `VITE_SUPABASE_URL`: Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY`: Your Supabase anonymous key

### Database Security
- Row Level Security (RLS) enabled on all tables
- Input sanitization triggers
- Data integrity constraints
- Format validation functions

### API Security
- Client-side API key encryption
- Usage quota monitoring
- Rate limiting validation
- Security event logging

## 🧪 Testing

### Security Tests
Run the security test suite to verify all security measures:
```bash
# In browser console or test environment
import { runAllSecurityTests } from './tests/security.test.ts';
runAllSecurityTests();
```

### Manual Testing
1. Test input validation with malicious inputs
2. Verify API key encryption/decryption
3. Check database constraints
4. Monitor security event logs

## 📁 Project Structure

```
virsnapp-29-v2/
├── components/          # React components
├── services/           # API and data services
├── lib/               # Utility libraries
│   ├── supabase.ts    # Database configuration
│   ├── env.ts         # Environment validation
│   ├── inputValidation.ts  # Input validation utilities
│   ├── apiKeySecurity.ts   # API key security utilities
│   └── securityMonitoring.ts  # Security monitoring
├── database/          # Database security policies
├── tests/            # Security test suite
├── types.ts          # TypeScript type definitions
└── SECURITY.md       # Detailed security documentation
```

## 🚨 Production Deployment

Before deploying to production:

1. **Security Checklist**: Follow the checklist in [SECURITY.md](SECURITY.md)
2. **Environment Variables**: Set production environment variables
3. **Database Policies**: Apply RLS policies from `database/security-policies.sql`
4. **API Keys**: Remove test keys and configure production YouTube API keys
5. **Monitoring**: Set up security monitoring and alerting

## 🆘 Support

- **Security Issues**: See [SECURITY.md](SECURITY.md) for emergency procedures
- **Database Setup**: See [database/README.md](database/README.md) for detailed instructions
- **API Configuration**: Check the Settings page in the application

## 📄 License

This project is for educational and personal use. Please ensure compliance with YouTube's Terms of Service when using their API.
