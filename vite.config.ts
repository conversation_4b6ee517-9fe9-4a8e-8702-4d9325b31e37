import path from 'path';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig(() => {
    return {
      plugins: [react()],
      define: {
        // Remove unused Gemini API key references
        // Environment variables with VITE_ prefix are automatically available
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      },
      // Ensure environment variables are loaded properly
      envPrefix: 'VITE_',
    };
});
