import React, { useState, useEffect } from 'react';
import { quotaService, type QuotaResetLog } from '../services/quotaService';
import { getQuotaScheduler, initializeQuotaScheduler, type SchedulerStatus } from '../services/quotaScheduler';

interface QuotaStatistics {
    totalKeys: number;
    keysNeedingReset: number;
    averageUsage: number;
    highUsageKeys: number;
    totalUsage: number;
    totalQuota: number;
}

export const QuotaManagementDashboard: React.FC = () => {
    const [statistics, setStatistics] = useState<QuotaStatistics | null>(null);
    const [resetLogs, setResetLogs] = useState<QuotaResetLog[]>([]);
    const [schedulerStatus, setSchedulerStatus] = useState<SchedulerStatus | null>(null);
    const [loading, setLoading] = useState(true);
    const [resetting, setResetting] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        loadDashboardData();
        initializeScheduler();
        
        // Refresh data every 5 minutes
        const interval = setInterval(loadDashboardData, 5 * 60 * 1000);
        return () => clearInterval(interval);
    }, []);

    const loadDashboardData = async () => {
        try {
            setLoading(true);
            setError(null);
            
            const [stats, logs] = await Promise.all([
                quotaService.getQuotaStatistics(),
                quotaService.getQuotaResetLogs(20)
            ]);
            
            setStatistics(stats);
            setResetLogs(logs);
            
            // Update scheduler status
            const scheduler = getQuotaScheduler();
            if (scheduler) {
                setSchedulerStatus(scheduler.getStatus());
            }
        } catch (err) {
            setError((err as Error).message);
        } finally {
            setLoading(false);
        }
    };

    const initializeScheduler = () => {
        try {
            const scheduler = initializeQuotaScheduler({
                resetTimeUTC: "00:00",
                enableAutoReset: true,
                checkIntervalMinutes: 60
            });
            setSchedulerStatus(scheduler.getStatus());
        } catch (err) {
            console.error('Failed to initialize scheduler:', err);
        }
    };

    const handleManualReset = async () => {
        try {
            setResetting(true);
            const scheduler = getQuotaScheduler();
            if (scheduler) {
                await scheduler.manualReset();
                await loadDashboardData(); // Refresh data
            }
        } catch (err) {
            setError((err as Error).message);
        } finally {
            setResetting(false);
        }
    };

    const formatDate = (dateString: string): string => {
        return new Date(dateString).toLocaleString();
    };

    const getUsageColor = (percentage: number): string => {
        if (percentage >= 90) return 'text-red-400';
        if (percentage >= 80) return 'text-yellow-400';
        if (percentage >= 60) return 'text-orange-400';
        return 'text-green-400';
    };

    if (loading && !statistics) {
        return (
            <div className="bg-dark-card border border-dark-border rounded-xl p-6">
                <div className="animate-pulse space-y-4">
                    <div className="h-6 bg-gray-700 rounded w-1/3"></div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {[1, 2, 3].map(i => (
                            <div key={i} className="h-20 bg-gray-700 rounded"></div>
                        ))}
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold text-white">Quota Management</h2>
                    <p className="text-gray-400 mt-1">Monitor and manage API key quotas</p>
                </div>
                <button
                    onClick={handleManualReset}
                    disabled={resetting}
                    className="px-4 py-2 bg-accent text-darkbg font-semibold rounded-lg hover:bg-accent-hover disabled:bg-gray-600 disabled:text-gray-400 transition-colors"
                >
                    {resetting ? 'Resetting...' : 'Reset All Quotas'}
                </button>
            </div>

            {error && (
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
                    <div className="text-red-400">{error}</div>
                    <button 
                        onClick={loadDashboardData}
                        className="mt-2 text-sm text-accent hover:text-white transition-colors"
                    >
                        Retry
                    </button>
                </div>
            )}

            {/* Statistics Cards */}
            {statistics && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="bg-gradient-to-br from-dark-card/80 to-dark-card/60 border border-dark-border/50 rounded-xl p-5">
                        <div className="flex items-center gap-3 mb-2">
                            <div className="w-3 h-3 rounded-full bg-blue-400"></div>
                            <h3 className="font-semibold text-white">Total API Keys</h3>
                        </div>
                        <div className="text-2xl font-bold text-white">{statistics.totalKeys}</div>
                        <div className="text-xs text-gray-400 mt-1">
                            {statistics.keysNeedingReset} need reset
                        </div>
                    </div>

                    <div className="bg-gradient-to-br from-dark-card/80 to-dark-card/60 border border-dark-border/50 rounded-xl p-5">
                        <div className="flex items-center gap-3 mb-2">
                            <div className="w-3 h-3 rounded-full bg-green-400"></div>
                            <h3 className="font-semibold text-white">Total Usage</h3>
                        </div>
                        <div className="text-2xl font-bold text-white">
                            {statistics.totalUsage.toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                            of {statistics.totalQuota.toLocaleString()} total quota
                        </div>
                    </div>

                    <div className="bg-gradient-to-br from-dark-card/80 to-dark-card/60 border border-dark-border/50 rounded-xl p-5">
                        <div className="flex items-center gap-3 mb-2">
                            <div className="w-3 h-3 rounded-full bg-orange-400"></div>
                            <h3 className="font-semibold text-white">Average Usage</h3>
                        </div>
                        <div className={`text-2xl font-bold ${getUsageColor((statistics.averageUsage / 10000) * 100)}`}>
                            {statistics.averageUsage.toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                            per API key
                        </div>
                    </div>

                    <div className="bg-gradient-to-br from-dark-card/80 to-dark-card/60 border border-dark-border/50 rounded-xl p-5">
                        <div className="flex items-center gap-3 mb-2">
                            <div className="w-3 h-3 rounded-full bg-red-400"></div>
                            <h3 className="font-semibold text-white">High Usage Keys</h3>
                        </div>
                        <div className="text-2xl font-bold text-red-400">{statistics.highUsageKeys}</div>
                        <div className="text-xs text-gray-400 mt-1">
                            using &gt;80% quota
                        </div>
                    </div>
                </div>
            )}

            {/* Scheduler Status */}
            {schedulerStatus && (
                <div className="bg-gradient-to-br from-dark-card/80 to-dark-card/60 border border-dark-border/50 rounded-xl p-5">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="font-semibold text-white">Automatic Reset Scheduler</h3>
                        <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                            schedulerStatus.isRunning 
                                ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                                : 'bg-red-500/20 text-red-400 border border-red-500/30'
                        }`}>
                            {schedulerStatus.isRunning ? 'Running' : 'Stopped'}
                        </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <div className="text-xs text-gray-400 uppercase tracking-wide mb-1">Next Reset</div>
                            <div className="text-white font-medium">
                                {formatDate(schedulerStatus.nextResetTime.toISOString())}
                            </div>
                        </div>
                        {schedulerStatus.lastResetTime && (
                            <div>
                                <div className="text-xs text-gray-400 uppercase tracking-wide mb-1">Last Reset</div>
                                <div className="text-white font-medium">
                                    {formatDate(schedulerStatus.lastResetTime.toISOString())}
                                </div>
                            </div>
                        )}
                    </div>

                    {schedulerStatus.lastResetResult && (
                        <div className="mt-4 pt-4 border-t border-gray-700/50">
                            <div className="text-xs text-gray-400 uppercase tracking-wide mb-2">Last Reset Result</div>
                            <div className={`text-sm ${schedulerStatus.lastResetResult.success ? 'text-green-400' : 'text-red-400'}`}>
                                {schedulerStatus.lastResetResult.success 
                                    ? `✅ Reset ${schedulerStatus.lastResetResult.keysReset}/${schedulerStatus.lastResetResult.totalKeys} keys`
                                    : `❌ Failed: ${schedulerStatus.lastResetResult.error}`
                                }
                            </div>
                        </div>
                    )}
                </div>
            )}

            {/* Recent Reset Logs */}
            <div className="bg-gradient-to-br from-dark-card/80 to-dark-card/60 border border-dark-border/50 rounded-xl p-5">
                <h3 className="font-semibold text-white mb-4">Recent Reset Activity</h3>
                
                {resetLogs.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                        <div className="w-12 h-12 mx-auto mb-3 opacity-50">
                            <svg fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <p className="text-sm">No reset activity yet</p>
                    </div>
                ) : (
                    <div className="space-y-3 max-h-64 overflow-y-auto">
                        {resetLogs.map((log) => (
                            <div key={log.id} className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg">
                                <div className="flex items-center gap-3">
                                    <div className={`w-2 h-2 rounded-full ${log.batch_reset ? 'bg-blue-400' : 'bg-green-400'}`}></div>
                                    <div>
                                        <div className="text-white text-sm font-medium">
                                            {log.batch_reset 
                                                ? `Batch Reset: ${log.keys_reset}/${log.total_keys} keys`
                                                : `Individual Reset${log.previous_usage ? ` (${log.previous_usage} → 0)` : ''}`
                                            }
                                        </div>
                                        <div className="text-xs text-gray-400">
                                            {formatDate(log.reset_at)}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};
