/**
 * Quota Reset Scheduler
 * Handles automatic daily quota resets and background job management
 */

import { quotaService } from './quotaService';

export interface SchedulerConfig {
    resetTimeUTC: string; // Format: "HH:MM" (e.g., "00:00" for midnight UTC)
    enableAutoReset: boolean;
    checkIntervalMinutes: number; // How often to check if reset is needed
}

export interface SchedulerStatus {
    isRunning: boolean;
    nextResetTime: Date;
    lastResetTime?: Date;
    lastResetResult?: {
        success: boolean;
        keysReset: number;
        totalKeys: number;
        error?: string;
    };
}

class QuotaScheduler {
    private intervalId: NodeJS.Timeout | null = null;
    private config: SchedulerConfig;
    private status: SchedulerStatus;

    constructor(config: Partial<SchedulerConfig> = {}) {
        this.config = {
            resetTimeUTC: "00:00", // Midnight UTC by default
            enableAutoReset: true,
            checkIntervalMinutes: 60, // Check every hour
            ...config
        };

        this.status = {
            isRunning: false,
            nextResetTime: this.calculateNextResetTime()
        };
    }

    /**
     * Start the quota reset scheduler
     */
    start(): void {
        if (this.intervalId) {
            console.warn('⚠️ Quota scheduler is already running');
            return;
        }

        console.log('🚀 Starting quota reset scheduler...');
        console.log(`⏰ Reset time: ${this.config.resetTimeUTC} UTC`);
        console.log(`🔄 Check interval: ${this.config.checkIntervalMinutes} minutes`);

        this.status.isRunning = true;
        
        // Check immediately on start
        this.checkAndReset();
        
        // Set up recurring checks
        this.intervalId = setInterval(() => {
            this.checkAndReset();
        }, this.config.checkIntervalMinutes * 60 * 1000);

        console.log('✅ Quota scheduler started successfully');
    }

    /**
     * Stop the quota reset scheduler
     */
    stop(): void {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }

        this.status.isRunning = false;
        console.log('🛑 Quota scheduler stopped');
    }

    /**
     * Get current scheduler status
     */
    getStatus(): SchedulerStatus {
        return {
            ...this.status,
            nextResetTime: this.calculateNextResetTime()
        };
    }

    /**
     * Update scheduler configuration
     */
    updateConfig(newConfig: Partial<SchedulerConfig>): void {
        const wasRunning = this.status.isRunning;
        
        if (wasRunning) {
            this.stop();
        }

        this.config = { ...this.config, ...newConfig };
        this.status.nextResetTime = this.calculateNextResetTime();

        if (wasRunning && this.config.enableAutoReset) {
            this.start();
        }

        console.log('⚙️ Scheduler configuration updated:', this.config);
    }

    /**
     * Manually trigger quota reset
     */
    async manualReset(): Promise<{
        success: boolean;
        keysReset: number;
        totalKeys: number;
        error?: string;
    }> {
        console.log('🔄 Manual quota reset triggered...');
        
        try {
            const result = await quotaService.scheduleDailyReset();
            
            if (result.success && result.result) {
                const resetResult = {
                    success: true,
                    keysReset: result.result.reset_count,
                    totalKeys: result.result.total_keys
                };

                this.status.lastResetTime = new Date();
                this.status.lastResetResult = resetResult;
                this.status.nextResetTime = this.calculateNextResetTime();

                console.log(`✅ Manual reset completed: ${resetResult.keysReset}/${resetResult.totalKeys} keys reset`);
                return resetResult;
            } else {
                const errorResult = {
                    success: false,
                    keysReset: 0,
                    totalKeys: 0,
                    error: result.error || 'Unknown error'
                };

                this.status.lastResetResult = errorResult;
                console.error('❌ Manual reset failed:', errorResult.error);
                return errorResult;
            }
        } catch (error) {
            const errorResult = {
                success: false,
                keysReset: 0,
                totalKeys: 0,
                error: (error as Error).message
            };

            this.status.lastResetResult = errorResult;
            console.error('❌ Manual reset failed:', error);
            return errorResult;
        }
    }

    /**
     * Check if reset is needed and perform it
     */
    private async checkAndReset(): Promise<void> {
        if (!this.config.enableAutoReset) {
            return;
        }

        const now = new Date();
        const shouldReset = now >= this.status.nextResetTime;

        if (!shouldReset) {
            return;
        }

        console.log('⏰ Scheduled quota reset time reached, performing reset...');
        
        try {
            const result = await this.manualReset();
            
            if (result.success) {
                console.log(`✅ Scheduled reset completed: ${result.keysReset}/${result.totalKeys} keys reset`);
            } else {
                console.error('❌ Scheduled reset failed:', result.error);
            }
        } catch (error) {
            console.error('❌ Scheduled reset error:', error);
        }
    }

    /**
     * Calculate the next reset time based on configuration
     */
    private calculateNextResetTime(): Date {
        const now = new Date();
        const [hours, minutes] = this.config.resetTimeUTC.split(':').map(Number);
        
        const nextReset = new Date();
        nextReset.setUTCHours(hours, minutes, 0, 0);
        
        // If the reset time has already passed today, schedule for tomorrow
        if (nextReset <= now) {
            nextReset.setUTCDate(nextReset.getUTCDate() + 1);
        }
        
        return nextReset;
    }

    /**
     * Get time until next reset in human-readable format
     */
    getTimeUntilNextReset(): string {
        const now = new Date();
        const nextReset = this.calculateNextResetTime();
        const diffMs = nextReset.getTime() - now.getTime();
        
        const hours = Math.floor(diffMs / (1000 * 60 * 60));
        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        
        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else {
            return `${minutes}m`;
        }
    }
}

// Global scheduler instance
let globalScheduler: QuotaScheduler | null = null;

/**
 * Initialize the global quota scheduler
 */
export const initializeQuotaScheduler = (config?: Partial<SchedulerConfig>): QuotaScheduler => {
    if (globalScheduler) {
        console.warn('⚠️ Quota scheduler already initialized');
        return globalScheduler;
    }

    globalScheduler = new QuotaScheduler(config);
    
    // Auto-start if enabled
    if (globalScheduler['config'].enableAutoReset) {
        globalScheduler.start();
    }

    return globalScheduler;
};

/**
 * Get the global quota scheduler instance
 */
export const getQuotaScheduler = (): QuotaScheduler | null => {
    return globalScheduler;
};

/**
 * Shutdown the global quota scheduler
 */
export const shutdownQuotaScheduler = (): void => {
    if (globalScheduler) {
        globalScheduler.stop();
        globalScheduler = null;
        console.log('🛑 Global quota scheduler shutdown');
    }
};

/**
 * Default scheduler configurations for different environments
 */
export const SCHEDULER_PRESETS = {
    PRODUCTION: {
        resetTimeUTC: "00:00", // Midnight UTC
        enableAutoReset: true,
        checkIntervalMinutes: 60 // Check every hour
    },
    DEVELOPMENT: {
        resetTimeUTC: "00:00", // Midnight UTC
        enableAutoReset: true,
        checkIntervalMinutes: 30 // Check every 30 minutes for faster testing
    },
    TESTING: {
        resetTimeUTC: "00:00", // Midnight UTC
        enableAutoReset: false, // Manual control in tests
        checkIntervalMinutes: 5 // Quick checks for testing
    }
} as const;

export { QuotaScheduler };
