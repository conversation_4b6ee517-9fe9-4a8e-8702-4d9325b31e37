


import React from 'react';
import {
    RiAddLine,
    RiCloseLine,
    RiSearchLine,
    RiArrowLeftSLine,
    RiDeleteBinLine,
    RiMore2Fill,
    RiPushpinLine,
    RiPencilLine,
    RiFileCopyLine,
    RiYoutubeFill,
    RiMapPinUserLine,
    RiBookmarkLine,
    RiSettings3Line,
    RiGroupLine,
    RiEyeLine,
    RiFilmLine,
    RiEyeOffLine,
    RiCheckLine,
    RiStickyNoteLine,
    RiRefreshLine,
    RiLinksLine,
    RiSubtractLine,
    RiDownload2Line,
} from 'react-icons/ri';

export const PlusIcon: React.FC<{className?: string}> = ({className}) => <RiAddLine className={className} />;
export const MinusIcon: React.FC<{className?: string}> = ({className}) => <RiSubtractLine className={className} />;
export const CloseIcon: React.FC<{className?: string}> = ({className}) => <RiCloseLine className={className} />;
export const SearchIcon: React.FC<{className?: string}> = ({className}) => <RiSearchLine className={className} />;
export const BackArrowIcon: React.FC<{className?: string}> = ({className}) => <RiArrowLeftSLine className={className} />;
export const TrashIcon: React.FC<{className?: string}> = ({className}) => <RiDeleteBinLine className={className} />;
export const MoreHorizontalIcon: React.FC<{ className?: string }> = ({ className }) => <RiMore2Fill className={className} />;
export const PinIcon: React.FC<{ className?: string }> = ({ className }) => <RiPushpinLine className={className} />;
export const RenameIcon: React.FC<{ className?: string }> = ({ className }) => <RiPencilLine className={className} />;
export const DuplicateIcon: React.FC<{ className?: string }> = ({ className }) => <RiFileCopyLine className={className} />;
export const YoutubeIcon: React.FC<{className?: string}> = ({className}) => <RiYoutubeFill className={className} />;
export const TrackedChannelsIcon: React.FC<{className?: string}> = ({className}) => <RiMapPinUserLine className={className} />;
export const SavedVideosIcon: React.FC<{className?: string}> = ({className}) => <RiBookmarkLine className={className} />;
export const SettingsIcon: React.FC<{className?: string}> = ({className}) => <RiSettings3Line className={className} />;
export const SubscribersIcon: React.FC<{className?: string}> = ({className}) => <RiGroupLine className={className} />;
export const ViewsIcon: React.FC<{className?: string}> = ({className}) => <RiEyeLine className={className} />;
export const VideoIcon: React.FC<{className?: string}> = ({className}) => <RiFilmLine className={className} />;
export const EyeIcon: React.FC<{className?: string}> = ({className}) => <RiEyeLine className={className} />;
export const EyeOffIcon: React.FC<{className?: string}> = ({className}) => <RiEyeOffLine className={className} />;
export const CheckIcon: React.FC<{className?: string}> = ({className}) => <RiCheckLine className={className} />;
export const NotesIcon: React.FC<{className?: string}> = ({className}) => <RiStickyNoteLine className={className} />;
export const RefreshIcon: React.FC<{className?: string}> = ({className}) => <RiRefreshLine className={className} />;
export const LinkIcon: React.FC<{className?: string}> = ({className}) => <RiLinksLine className={className} />;
export const ExportIcon: React.FC<{className?: string}> = ({className}) => <RiDownload2Line className={className} />;

// --- Custom Logo ---
// This is kept as a custom SVG because it's a unique brand logo, not a standard icon.
export const VelioIcon: React.FC<{className?: string}> = ({className}) => (
    <svg className={className} viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M18 3H6C4.34315 3 3 4.34315 3 6V18C3 19.6569 4.34315 21 6 21H18C19.6569 21 21 19.6569 21 18V6C21 4.34315 19.6569 3 18 3ZM10.5 8.65991C10.5 7.8524 11.3956 7.37563 12.0901 7.82009L16.0279 10.5201C16.66 10.9259 16.66 11.8741 16.0279 12.2799L12.0901 14.9799C11.3956 15.4244 10.5 14.9476 10.5 14.1401V8.65991Z" />
    </svg>
);