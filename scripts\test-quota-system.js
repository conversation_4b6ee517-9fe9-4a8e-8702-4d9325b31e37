#!/usr/bin/env node

/**
 * Quota Management System Test Script
 * Tests the quota management functionality and database setup
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`);
}

function checkCritical(condition, successMessage, errorMessage) {
    if (condition) {
        log(`✅ ${successMessage}`, colors.green);
        return true;
    } else {
        log(`❌ ${errorMessage}`, colors.red);
        return false;
    }
}

function checkWarning(condition, successMessage, warningMessage) {
    if (condition) {
        log(`✅ ${successMessage}`, colors.green);
        return true;
    } else {
        log(`⚠️ ${warningMessage}`, colors.yellow);
        return false;
    }
}

async function testDatabaseConnection() {
    log('\n🔌 DATABASE CONNECTION TEST', colors.bright);
    log('=' .repeat(50), colors.blue);
    
    try {
        // Check if environment variables are set
        const supabaseUrl = process.env.VITE_SUPABASE_URL;
        const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
        
        if (!supabaseUrl || !supabaseKey) {
            log('❌ Supabase environment variables not set', colors.red);
            log('   Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY', colors.yellow);
            return false;
        }
        
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        // Test basic connection
        const { data, error } = await supabase.from('api_keys').select('count').limit(1);
        
        if (error) {
            log(`❌ Database connection failed: ${error.message}`, colors.red);
            return false;
        }
        
        log('✅ Database connection successful', colors.green);
        return supabase;
    } catch (error) {
        log(`❌ Database connection error: ${error.message}`, colors.red);
        return false;
    }
}

async function testQuotaSchema(supabase) {
    log('\n🗄️ QUOTA SCHEMA TEST', colors.bright);
    log('=' .repeat(50), colors.blue);
    
    let score = 0;
    const total = 6;
    
    try {
        // Test if quota columns exist
        const { data: columns, error: columnsError } = await supabase
            .rpc('get_table_columns', { table_name: 'api_keys' })
            .catch(() => ({ data: null, error: 'Function not available' }));
        
        // Test quota_limit column
        const { data: quotaTest, error: quotaError } = await supabase
            .from('api_keys')
            .select('quota_limit')
            .limit(1);
        
        if (!quotaError) {
            log('✅ quota_limit column exists', colors.green);
            score++;
        } else {
            log('❌ quota_limit column missing', colors.red);
        }
        
        // Test last_reset_at column
        const { data: resetTest, error: resetError } = await supabase
            .from('api_keys')
            .select('last_reset_at')
            .limit(1);
        
        if (!resetError) {
            log('✅ last_reset_at column exists', colors.green);
            score++;
        } else {
            log('❌ last_reset_at column missing', colors.red);
        }
        
        // Test created_at column
        const { data: createdTest, error: createdError } = await supabase
            .from('api_keys')
            .select('created_at')
            .limit(1);
        
        if (!createdError) {
            log('✅ created_at column exists', colors.green);
            score++;
        } else {
            log('❌ created_at column missing', colors.red);
        }
        
        // Test quota_reset_logs table
        const { data: logsTest, error: logsError } = await supabase
            .from('quota_reset_logs')
            .select('id')
            .limit(1);
        
        if (!logsError) {
            log('✅ quota_reset_logs table exists', colors.green);
            score++;
        } else {
            log('❌ quota_reset_logs table missing', colors.red);
        }
        
        // Test quota functions
        const functions = [
            'get_quota_status',
            'reset_api_key_quota',
            'reset_all_api_key_quotas',
            'needs_quota_reset'
        ];
        
        let functionsExist = 0;
        for (const func of functions) {
            try {
                // Try to call function with dummy data to test existence
                await supabase.rpc(func, func === 'get_quota_status' || func === 'needs_quota_reset' ? 
                    { api_key_id: '00000000-0000-0000-0000-000000000000' } : {});
                functionsExist++;
            } catch (error) {
                // Function exists if we get a different error than "function not found"
                if (!error.message.includes('function') && !error.message.includes('does not exist')) {
                    functionsExist++;
                }
            }
        }
        
        if (functionsExist >= 3) {
            log('✅ Quota management functions exist', colors.green);
            score++;
        } else {
            log(`❌ Quota management functions missing (${functionsExist}/4)`, colors.red);
        }
        
        // Test triggers
        const { data: triggerTest, error: triggerError } = await supabase
            .rpc('check_trigger_exists', { trigger_name: 'auto_reset_quota_trigger' })
            .catch(() => ({ data: false, error: null }));
        
        if (triggerTest || !triggerError) {
            log('✅ Auto-reset trigger configured', colors.green);
            score++;
        } else {
            log('⚠️ Auto-reset trigger may not be configured', colors.yellow);
        }
        
    } catch (error) {
        log(`❌ Schema test error: ${error.message}`, colors.red);
    }
    
    log(`\n📊 Schema Score: ${score}/${total} (${Math.round(score/total*100)}%)`, 
        score === total ? colors.green : score >= total * 0.7 ? colors.yellow : colors.red);
    
    return score >= total * 0.7; // 70% pass rate
}

async function testQuotaOperations(supabase) {
    log('\n⚙️ QUOTA OPERATIONS TEST', colors.bright);
    log('=' .repeat(50), colors.blue);
    
    try {
        // Test getting quota statistics
        const { data: stats, error: statsError } = await supabase
            .from('api_keys')
            .select('id, usage, quota_limit, last_reset_at')
            .limit(5);
        
        if (!statsError && stats) {
            log(`✅ Retrieved ${stats.length} API keys for testing`, colors.green);
            
            if (stats.length > 0) {
                const testKey = stats[0];
                log(`   Testing with key: ${testKey.id}`, colors.cyan);
                
                // Test quota status function
                try {
                    const { data: quotaStatus, error: quotaError } = await supabase
                        .rpc('get_quota_status', { api_key_id: testKey.id });
                    
                    if (!quotaError && quotaStatus && quotaStatus.length > 0) {
                        const status = quotaStatus[0];
                        log('✅ Quota status function working', colors.green);
                        log(`   Current usage: ${status.current_usage}/${status.quota_limit}`, colors.cyan);
                        log(`   Usage percentage: ${status.usage_percentage}%`, colors.cyan);
                        log(`   Needs reset: ${status.needs_reset}`, colors.cyan);
                    } else {
                        log('❌ Quota status function failed', colors.red);
                    }
                } catch (error) {
                    log(`❌ Quota status test error: ${error.message}`, colors.red);
                }
                
                // Test needs reset function
                try {
                    const { data: needsReset, error: needsError } = await supabase
                        .rpc('needs_quota_reset', { api_key_id: testKey.id });
                    
                    if (!needsError) {
                        log(`✅ Needs reset function working: ${needsReset}`, colors.green);
                    } else {
                        log('❌ Needs reset function failed', colors.red);
                    }
                } catch (error) {
                    log(`❌ Needs reset test error: ${error.message}`, colors.red);
                }
            }
        } else {
            log('❌ Could not retrieve API keys for testing', colors.red);
        }
        
        // Test quota reset logs
        const { data: logs, error: logsError } = await supabase
            .from('quota_reset_logs')
            .select('*')
            .order('reset_at', { ascending: false })
            .limit(5);
        
        if (!logsError) {
            log(`✅ Quota reset logs accessible (${logs?.length || 0} recent entries)`, colors.green);
        } else {
            log('❌ Quota reset logs not accessible', colors.red);
        }
        
    } catch (error) {
        log(`❌ Operations test error: ${error.message}`, colors.red);
        return false;
    }
    
    return true;
}

async function testQuotaFiles() {
    log('\n📁 QUOTA FILES TEST', colors.bright);
    log('=' .repeat(50), colors.blue);
    
    const requiredFiles = [
        'services/quotaService.ts',
        'services/quotaScheduler.ts',
        'components/ApiKeyQuotaCard.tsx',
        'components/QuotaManagementDashboard.tsx',
        'database/api-key-quota-migration.sql',
        'docs/quota-management-system.md'
    ];
    
    let score = 0;
    
    for (const file of requiredFiles) {
        const exists = fs.existsSync(file);
        if (exists) {
            log(`✅ ${file}`, colors.green);
            score++;
        } else {
            log(`❌ ${file}`, colors.red);
        }
    }
    
    log(`\n📊 Files Score: ${score}/${requiredFiles.length} (${Math.round(score/requiredFiles.length*100)}%)`, 
        score === requiredFiles.length ? colors.green : colors.yellow);
    
    return score >= requiredFiles.length * 0.8; // 80% pass rate
}

async function main() {
    log('🧪 QUOTA MANAGEMENT SYSTEM TEST SUITE', colors.bright);
    log('=' .repeat(60), colors.blue);
    log('Testing the automatic quota reset system implementation\n', colors.cyan);
    
    let totalScore = 0;
    let maxScore = 0;
    
    // Test 1: Files
    log('Running test suite...\n');
    const filesPass = await testQuotaFiles();
    totalScore += filesPass ? 1 : 0;
    maxScore += 1;
    
    // Test 2: Database Connection
    const supabase = await testDatabaseConnection();
    totalScore += supabase ? 1 : 0;
    maxScore += 1;
    
    if (supabase) {
        // Test 3: Schema
        const schemaPass = await testQuotaSchema(supabase);
        totalScore += schemaPass ? 1 : 0;
        maxScore += 1;
        
        // Test 4: Operations
        const operationsPass = await testQuotaOperations(supabase);
        totalScore += operationsPass ? 1 : 0;
        maxScore += 1;
    } else {
        log('\n⚠️ Skipping schema and operations tests due to database connection failure', colors.yellow);
        maxScore += 2;
    }
    
    // Final Results
    log('\n' + '=' .repeat(60), colors.blue);
    log('🏁 FINAL RESULTS', colors.bright);
    log('=' .repeat(60), colors.blue);
    
    const percentage = Math.round((totalScore / maxScore) * 100);
    const resultColor = percentage >= 80 ? colors.green : percentage >= 60 ? colors.yellow : colors.red;
    
    log(`\n📊 Overall Score: ${totalScore}/${maxScore} (${percentage}%)`, resultColor);
    
    if (percentage >= 80) {
        log('\n🎉 QUOTA SYSTEM READY!', colors.green);
        log('✅ The quota management system is properly configured and ready to use.', colors.green);
        log('\n📋 Next steps:', colors.cyan);
        log('   1. Run the database migration: database/api-key-quota-migration.sql', colors.cyan);
        log('   2. Restart your application to initialize the quota scheduler', colors.cyan);
        log('   3. Check the Settings page for quota management features', colors.cyan);
    } else if (percentage >= 60) {
        log('\n⚠️ QUOTA SYSTEM PARTIALLY READY', colors.yellow);
        log('Some components are missing or not configured properly.', colors.yellow);
        log('Please review the failed tests above and complete the setup.', colors.yellow);
    } else {
        log('\n❌ QUOTA SYSTEM NOT READY', colors.red);
        log('Critical components are missing. Please complete the implementation.', colors.red);
    }
    
    log('\n📚 For detailed information, see: docs/quota-management-system.md', colors.cyan);
}

// Run the test suite
if (require.main === module) {
    main().catch(error => {
        log(`\n💥 Test suite crashed: ${error.message}`, colors.red);
        process.exit(1);
    });
}

module.exports = { main };
