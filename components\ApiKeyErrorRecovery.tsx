import React, { useState } from 'react';
import { apiKeyService } from '../services/apiKeyService';

interface ApiKeyErrorRecoveryProps {
    error: string;
    onRecoveryComplete?: () => void;
    onNavigateToSettings?: () => void;
}

export const ApiKeyErrorRecovery: React.FC<ApiKeyErrorRecoveryProps> = ({
    error,
    onRecoveryComplete,
    onNavigateToSettings
}) => {
    const [isRecovering, setIsRecovering] = useState(false);
    const [recoveryResult, setRecoveryResult] = useState<{
        removedCount: number;
        validKeysRemaining: number;
        needsNewKey: boolean;
    } | null>(null);

    const handleAutoRecovery = async () => {
        setIsRecovering(true);
        try {
            const result = await apiKeyService.cleanupCorruptedKeys();
            setRecoveryResult(result);
            
            if (result.validKeysRemaining > 0) {
                // If we have valid keys, try to reload the page
                setTimeout(() => {
                    onRecoveryComplete?.();
                    window.location.reload();
                }, 2000);
            }
        } catch (recoveryError) {
            console.error('Recovery failed:', recoveryError);
        } finally {
            setIsRecovering(false);
        }
    };

    const isCorruptionError = error.includes('corrupted') || error.includes('decrypt') || error.includes('invalid format');

    return (
        <div className="min-h-screen bg-[#0f0f0f] flex items-center justify-center p-4">
            <div className="bg-[#1a1a1a] border border-[#333] rounded-xl p-8 max-w-md w-full">
                {/* Error Icon */}
                <div className="flex justify-center mb-6">
                    <div className="w-16 h-16 bg-[#ef4444]/10 rounded-full flex items-center justify-center">
                        <svg className="w-8 h-8 text-[#ef4444]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                </div>

                {/* Title */}
                <h2 className="text-xl font-bold text-white text-center mb-4">
                    API Key Issue Detected
                </h2>

                {/* Error Message */}
                <div className="bg-[#ef4444]/10 border border-[#ef4444]/20 rounded-lg p-4 mb-6">
                    <p className="text-[#ef4444] text-sm">
                        {error}
                    </p>
                </div>

                {/* Recovery Result */}
                {recoveryResult && (
                    <div className="bg-[#10b981]/10 border border-[#10b981]/20 rounded-lg p-4 mb-6">
                        <h3 className="text-[#10b981] font-semibold mb-2">Recovery Complete</h3>
                        <ul className="text-[#10b981] text-sm space-y-1">
                            <li>• Removed {recoveryResult.removedCount} corrupted key(s)</li>
                            <li>• {recoveryResult.validKeysRemaining} valid key(s) remaining</li>
                            {recoveryResult.needsNewKey && (
                                <li className="text-[#ff6b35]">• You need to add a new API key</li>
                            )}
                        </ul>
                        {recoveryResult.validKeysRemaining > 0 && (
                            <p className="text-[#10b981] text-sm mt-2">
                                ✅ Reloading application with valid keys...
                            </p>
                        )}
                    </div>
                )}

                {/* Action Buttons */}
                <div className="space-y-3">
                    {isCorruptionError && !recoveryResult && (
                        <button
                            onClick={handleAutoRecovery}
                            disabled={isRecovering}
                            className="w-full bg-[#10b981] text-white py-3 px-4 rounded-lg font-medium hover:bg-[#0ea474] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isRecovering ? (
                                <span className="flex items-center justify-center gap-2">
                                    <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"/>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                                    </svg>
                                    Cleaning up corrupted keys...
                                </span>
                            ) : (
                                'Auto-Fix Corrupted Keys'
                            )}
                        </button>
                    )}

                    <button
                        onClick={onNavigateToSettings}
                        className="w-full bg-[#333] text-white py-3 px-4 rounded-lg font-medium hover:bg-[#444] transition-colors duration-200"
                    >
                        Go to API Key Settings
                    </button>

                    <button
                        onClick={() => window.location.reload()}
                        className="w-full border border-[#444] text-[#ccc] py-3 px-4 rounded-lg font-medium hover:bg-[#333] hover:text-white transition-colors duration-200"
                    >
                        Retry
                    </button>
                </div>

                {/* Help Text */}
                <div className="mt-6 p-4 bg-[#333]/30 rounded-lg">
                    <h4 className="text-white font-medium mb-2">What happened?</h4>
                    <p className="text-[#ccc] text-sm leading-relaxed">
                        {isCorruptionError ? (
                            <>
                                One or more of your API keys appears to be corrupted or was encrypted with a different method. 
                                This can happen after app updates or data migrations. Use the auto-fix feature to clean up 
                                corrupted keys automatically.
                            </>
                        ) : (
                            <>
                                There was an issue with your API key configuration. Please check your API key settings 
                                and ensure you have at least one valid YouTube Data API v3 key configured.
                            </>
                        )}
                    </p>
                </div>
            </div>
        </div>
    );
};
