

import React from 'react';
import type { Folder } from '../types';
import { FolderCard } from './FolderCard';
import { PlusIcon } from './Icons';

type Modal = 'saveVideo' | 'manageFolders' | 'manageTags';

interface SavedVideosPageProps {
    folders: Folder[];
    onOpenModal: (modal: Modal) => void;
    onRenameFolder: (id: string, newName: string) => Promise<void>;
    onDeleteFolder: (id: string) => Promise<void>;
    onSelectFolder: (id: string) => void;
}

export const SavedVideosPage: React.FC<SavedVideosPageProps> = ({ 
    folders, 
    onOpenModal,
    onRenameFolder,
    onDeleteFolder,
    onSelectFolder,
}) => {
    return (
        <div className="p-4 sm:p-6 lg:p-8 space-y-8">
            <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <h2 className="text-3xl font-bold text-white">Saved videos</h2>
                <div className="flex items-center space-x-2 sm:space-x-4">
                    <button onClick={() => onOpenModal('manageFolders')} className="text-sm text-accent hover:text-white transition-colors">Manage folders</button>
                    <button onClick={() => onOpenModal('manageTags')} className="text-sm text-accent hover:text-white transition-colors">Manage tags</button>
                    <button 
                        onClick={() => onOpenModal('saveVideo')}
                        className="flex items-center space-x-2 px-4 py-2.5 rounded-lg bg-white text-darkbg font-semibold hover:bg-gray-200 transition-colors"
                    >
                        <PlusIcon className="w-5 h-5" />
                        <span>Save a video</span>
                    </button>
                </div>
            </header>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                {folders.map(folder => (
                    <FolderCard 
                        key={folder.id} 
                        folder={folder}
                        onSelect={() => onSelectFolder(folder.id)}
                        onDelete={onDeleteFolder}
                        onRename={onRenameFolder}
                    />
                ))}
                {folders.length === 0 && (
                    <div className="sm:col-span-2 md:col-span-3 lg:col-span-4 xl:col-span-5 text-center py-16 text-gray-500">
                        <p>No folders created yet.</p>
                        <p className="mt-2 text-sm">Click "Manage folders" to get started.</p>
                    </div>
                )}
            </div>
        </div>
    );
};