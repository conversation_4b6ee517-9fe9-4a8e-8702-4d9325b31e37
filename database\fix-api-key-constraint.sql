-- Fix API Key Constraint Issue
-- This script removes the problematic constraint and replaces it with a more flexible one

-- =====================================================
-- REMOVE PROBLEMATIC CONSTRAINT
-- =====================================================

-- Drop the existing constraint that's causing issues
-- This constraint was likely checking for YouTube API key format on encrypted keys
ALTER TABLE api_keys DROP CONSTRAINT IF EXISTS api_keys_valid_format;
ALTER TABLE api_keys DROP CONSTRAINT IF EXISTS api_keys_check;
ALTER TABLE api_keys DROP CONSTRAINT IF EXISTS check_api_key_format;

-- =====================================================
-- ADD FLEXIBLE CONSTRAINTS
-- =====================================================

-- Add constraint to ensure key is not null or empty
ALTER TABLE api_keys 
ADD CONSTRAINT api_keys_key_not_empty 
CHECK (key IS NOT NULL AND LENGTH(TRIM(key)) > 0);

-- Add constraint to ensure name is valid
ALTER TABLE api_keys 
ADD CONSTRAINT api_keys_name_valid 
CHECK (name IS NOT NULL AND LENGTH(TRIM(name)) > 0 AND LENGTH(TRIM(name)) <= 100);

-- Add constraint for quota limit (must be positive)
ALTER TABLE api_keys 
ADD CONSTRAINT api_keys_quota_limit_positive 
CHECK (quota_limit > 0 AND quota_limit <= 1000000);

-- Add constraint for usage (must be non-negative)
ALTER TABLE api_keys 
ADD CONSTRAINT api_keys_usage_non_negative 
CHECK (usage >= 0);

-- =====================================================
-- ENSURE REQUIRED COLUMNS EXIST
-- =====================================================

-- Add usage column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'api_keys' AND column_name = 'usage') THEN
        ALTER TABLE api_keys ADD COLUMN usage INTEGER DEFAULT 0 NOT NULL;
    END IF;
END $$;

-- Add quota_limit column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'api_keys' AND column_name = 'quota_limit') THEN
        ALTER TABLE api_keys ADD COLUMN quota_limit INTEGER DEFAULT 10000 NOT NULL;
    END IF;
END $$;

-- Add last_reset_at column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'api_keys' AND column_name = 'last_reset_at') THEN
        ALTER TABLE api_keys ADD COLUMN last_reset_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL;
    END IF;
END $$;

-- Add notes column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'api_keys' AND column_name = 'notes') THEN
        ALTER TABLE api_keys ADD COLUMN notes TEXT;
    END IF;
END $$;

-- Add created_at column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'api_keys' AND column_name = 'created_at') THEN
        ALTER TABLE api_keys ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL;
    END IF;
END $$;

-- =====================================================
-- UPDATE EXISTING DATA
-- =====================================================

-- Update any existing records that might have null values
UPDATE api_keys 
SET usage = 0 
WHERE usage IS NULL;

UPDATE api_keys 
SET quota_limit = 10000 
WHERE quota_limit IS NULL;

UPDATE api_keys 
SET last_reset_at = NOW() 
WHERE last_reset_at IS NULL;

UPDATE api_keys 
SET created_at = NOW() 
WHERE created_at IS NULL;

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Index on created_at for ordering
CREATE INDEX IF NOT EXISTS idx_api_keys_created_at ON api_keys(created_at);

-- Index on usage for quota monitoring
CREATE INDEX IF NOT EXISTS idx_api_keys_usage ON api_keys(usage);

-- Index on last_reset_at for quota reset scheduling
CREATE INDEX IF NOT EXISTS idx_api_keys_last_reset_at ON api_keys(last_reset_at);

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify the table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'api_keys' 
ORDER BY ordinal_position;

-- Verify constraints
SELECT constraint_name, constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'api_keys';

-- Count existing records
SELECT COUNT(*) as total_api_keys FROM api_keys;

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON TABLE api_keys IS 'Stores encrypted YouTube API keys with quota management';
COMMENT ON COLUMN api_keys.key IS 'Encrypted API key value - validation happens before encryption';
COMMENT ON COLUMN api_keys.usage IS 'Current daily usage count';
COMMENT ON COLUMN api_keys.quota_limit IS 'Daily quota limit (default: 10,000)';
COMMENT ON COLUMN api_keys.last_reset_at IS 'Timestamp of last quota reset';
COMMENT ON COLUMN api_keys.notes IS 'Optional notes about the API key';

-- =====================================================
-- SUCCESS MESSAGE
-- =====================================================

DO $$ 
BEGIN
    RAISE NOTICE 'API key constraint fix completed successfully!';
    RAISE NOTICE 'The problematic format constraint has been removed.';
    RAISE NOTICE 'New flexible constraints have been added.';
    RAISE NOTICE 'All required columns are now present.';
END $$;
