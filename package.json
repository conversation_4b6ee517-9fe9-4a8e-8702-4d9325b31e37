{"name": "virsnapp-29/06/2025--v2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "security:validate": "node scripts/validate-security.js", "security:check": "npm run security:validate && echo 'Security validation complete'", "prebuild": "npm run security:validate"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "^3.0.2", "react-icons/ri": "^5.2.1", "react-icons": "^5.5.0", "@supabase/supabase-js": "^2.44.4"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}