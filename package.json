{"name": "virsnapp-29/06/2025--v2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "security:validate": "node scripts/validate-security.js", "security:check": "npm run security:validate && echo 'Security validation complete'", "security:monitor": "node scripts/security-monitor.js", "security:monitor:once": "node scripts/security-monitor.js --once", "test:all": "node scripts/run-all-tests.js", "test:security": "npm run security:validate && npm run test:all", "test:manual": "echo 'See testing/MANUAL-TESTING-CHECKLIST.md for manual testing instructions'", "perf:analyze": "echo 'See optimization/PERFORMANCE-OPTIMIZATION.md for performance analysis'", "perf:monitor": "echo 'Performance monitoring integrated in application'", "deploy:check": "node scripts/pre-deployment-check.js", "deploy:build": "npm run deploy:check && npm run build", "deploy:preview": "npm run deploy:build && npm run preview", "deploy:test": "npm run test:security && npm run deploy:check", "deploy:full": "npm run deploy:test && npm run deploy:build && echo 'Ready for production deployment'", "prebuild": "npm run security:validate"}, "dependencies": {"@supabase/supabase-js": "^2.44.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "recharts": "^3.0.2"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}