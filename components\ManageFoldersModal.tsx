
import React, { useState, useRef, useEffect } from 'react';
import type { Folder } from '../types';
import { CloseIcon, MoreHorizontalIcon, PlusIcon, RenameIcon, TrashIcon, CheckIcon } from './Icons';
import { useClickOutside } from '../lib/utils';

interface ManageFoldersModalProps {
    folders: Folder[];
    onClose: () => void;
    onAdd: (name: string) => Promise<void>;
    onRename: (id: string, newName: string) => Promise<void>;
    onDelete: (id: string) => Promise<void>;
}

export const ManageFoldersModal: React.FC<ManageFoldersModalProps> = ({ folders, onClose, onAdd, onRename, onDelete }) => {
    const [newFolderName, setNewFolderName] = useState('');
    const [isAdding, setIsAdding] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if(error) {
            const timer = setTimeout(() => setError(null), 3000);
            return () => clearTimeout(timer);
        }
    }, [error]);

    const handleAdd = async () => {
        if (!newFolderName.trim() || isAdding) return;
        setIsAdding(true);
        setError(null);
        try {
            await onAdd(newFolderName.trim());
            setNewFolderName('');
        } catch (err) {
            setError((err as Error).message);
        } finally {
            setIsAdding(false);
        }
    };
    
    return (
         <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4" aria-modal="true" role="dialog">
            <div className="fixed inset-0" onClick={onClose} aria-hidden="true"></div>
            <div className="relative bg-dark-card border border-dark-border rounded-xl w-full max-w-md p-6 flex flex-col" style={{height: 'min(600px, 90vh)'}}>
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-bold text-white">Manage folders</h2>
                    <button onClick={onClose} className="p-1 rounded-full hover:bg-gray-700">
                        <CloseIcon className="w-6 h-6 text-gray-400" />
                    </button>
                </div>
                
                <div className="mb-6">
                    <p className="text-gray-400 text-sm mb-2">Create new folder</p>
                    <div className="flex items-center space-x-2">
                        <input
                            type="text"
                            value={newFolderName}
                            onChange={e => setNewFolderName(e.target.value)}
                            onKeyDown={e => e.key === 'Enter' && handleAdd()}
                            placeholder="Enter new folder name"
                            className="flex-grow bg-[#1a1a1a] border border-gray-600 rounded-lg px-4 py-2.5 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent"
                            disabled={isAdding}
                        />
                        <button onClick={handleAdd} disabled={isAdding || !newFolderName.trim()} className="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-accent/20 text-accent hover:bg-accent/30 disabled:bg-gray-600 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors">
                            {isAdding ? <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-accent"></div> : <PlusIcon className="w-6 h-6"/>}
                        </button>
                    </div>
                     {error && <p className="text-red-400 text-sm mt-2">{error}</p>}
                </div>

                <div className="flex-1 overflow-y-auto -mr-3 pr-3 space-y-2">
                    {folders.map(folder => 
                        <FolderListItem key={folder.id} folder={folder} onRename={onRename} onDelete={onDelete} />
                    )}
                </div>

                <div className="mt-6 flex justify-end">
                    <button onClick={onClose} className="px-5 py-2.5 rounded-lg bg-white text-darkbg font-semibold hover:bg-gray-200 transition-colors">
                       Done
                    </button>
                </div>
            </div>
        </div>
    );
};

const FolderListItem: React.FC<{folder: Folder} & Pick<ManageFoldersModalProps, 'onRename' | 'onDelete'>> = ({folder, onRename, onDelete}) => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);
    useClickOutside(menuRef, () => setIsMenuOpen(false));

    const [isEditing, setIsEditing] = useState(false);
    const [editedName, setEditedName] = useState(folder.name);
    const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!isMenuOpen) setIsConfirmingDelete(false);
        if (isMenuOpen && isEditing) setIsEditing(false);
    }, [isMenuOpen]);
    
    useEffect(() => {
        if(error) {
            const timer = setTimeout(() => setError(null), 3000);
            return () => clearTimeout(timer);
        }
    }, [error]);

    const handleAction = async (action: Promise<any>, successCallback?: () => void) => {
        setIsLoading(true);
        setError(null);
        try {
            await action;
            if (successCallback) successCallback();
        } catch (err) {
            setError((err as Error).message);
        } finally {
            setIsLoading(false);
        }
    };

    const handleRename = () => {
        if(editedName.trim() && editedName.trim() !== folder.name) {
            handleAction(onRename(folder.id, editedName.trim()), () => setIsEditing(false));
        } else {
            setIsEditing(false);
        }
    };

    const handleDelete = () => handleAction(onDelete(folder.id));
    
    if (isEditing) {
        return (
            <div className="flex items-center justify-between bg-[#1a1a1a]/50 p-2 rounded-lg border border-accent">
                <div className="flex-grow">
                    <input
                        type="text"
                        value={editedName}
                        onChange={e => setEditedName(e.target.value)}
                        onKeyDown={e => e.key === 'Enter' ? handleRename() : (e.key === 'Escape' && setIsEditing(false))}
                        className="w-full bg-dark-card border border-gray-600 rounded-md px-2 py-1 text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-accent"
                        autoFocus
                    />
                    {error && <p className="text-red-400 text-xs mt-1 pl-1">{error}</p>}
                </div>
                <div className="flex items-center ml-2">
                    <button onClick={() => setIsEditing(false)} className="p-1 text-gray-400 hover:text-white"><CloseIcon className="w-5 h-5"/></button>
                    <button onClick={handleRename} disabled={isLoading} className="p-1 text-accent hover:text-white"><CheckIcon className="w-5 h-5"/></button>
                </div>
            </div>
        )
    }
    
    return (
        <div className="flex items-center justify-between bg-[#1a1a1a]/50 p-3 rounded-lg group">
            <span className="text-white">{folder.name}</span>
            <div className="relative" ref={menuRef}>
                <button onClick={() => setIsMenuOpen(p => !p)} className="p-1 text-gray-400 hover:text-white opacity-0 group-hover:opacity-100 focus:opacity-100 transition-opacity">
                    <MoreHorizontalIcon className="w-5 h-5"/>
                </button>
                 {isMenuOpen && (
                    <div className="absolute top-full right-0 mt-2 w-40 bg-[#2a2a2a] border border-dark-border rounded-lg shadow-xl z-10 overflow-hidden">
                        <ul className="text-sm text-gray-200">
                             <li onClick={() => { setIsEditing(true); setIsMenuOpen(false); }} className="flex items-center px-4 py-2.5 cursor-pointer transition-colors duration-150 hover:bg-gray-700/50">
                                <RenameIcon className="w-4 h-4 mr-3" />
                                <span>Rename</span>
                            </li>
                            <li 
                                onClick={() => isConfirmingDelete ? handleDelete() : setIsConfirmingDelete(true)} 
                                className="flex items-center px-4 py-2.5 cursor-pointer transition-colors duration-150 text-red-400 hover:bg-red-500/10"
                            >
                                <TrashIcon className="w-4 h-4 mr-3" />
                                <span>{isConfirmingDelete ? 'Confirm' : 'Delete'}</span>
                            </li>
                        </ul>
                    </div>
                 )}
            </div>
        </div>
    )
}
