
import React from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>Axis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer } from 'recharts';
import type { Statistic } from '../types';

interface HistoryChartProps {
    data: Statistic[];
    dataKey: keyof Omit<Statistic, 'recordedAt'>;
    title: string;
}

const formatYAxisTick = (value: number) => {
    if (value >= 1000000000) return `${value / 1000000000}B`;
    if (value >= 1000000) return `${value / 1000000}M`;
    if (value >= 1000) return `${value / 1000}K`;
    return value.toString();
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
};

const CustomTooltip: React.FC<any> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-dark-card/80 backdrop-blur-sm p-3 rounded-lg border border-dark-border shadow-lg">
        <p className="label text-gray-300">{`${formatDate(label)}`}</p>
        <p className="intro text-white font-bold" style={{ color: payload[0].color }}>{`${payload[0].name}: ${formatYAxisTick(payload[0].value)}`}</p>
      </div>
    );
  }

  return null;
};


export const HistoryChart: React.FC<HistoryChartProps> = ({ data, dataKey, title }) => {
    const chartTitle = dataKey.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase());
    
    return (
        <div className="bg-dark-card border border-dark-border rounded-xl p-4 sm:p-6">
            <h3 className="text-lg font-semibold text-white mb-4">{title}</h3>
            <div style={{ width: '100%', height: 300 }}>
                <ResponsiveContainer>
                    <LineChart
                        data={data}
                        margin={{
                            top: 5,
                            right: 20,
                            left: 10,
                            bottom: 5,
                        }}
                    >
                        <CartesianGrid strokeDasharray="3 3" stroke="#3a3a3a" />
                        <XAxis 
                            dataKey="recordedAt" 
                            tickFormatter={formatDate}
                            stroke="#888888" 
                            tick={{ fill: '#888888', fontSize: 12 }}
                            minTickGap={20}
                        />
                        <YAxis 
                            tickFormatter={formatYAxisTick} 
                            stroke="#888888" 
                            tick={{ fill: '#888888', fontSize: 12 }}
                            width={50}
                            axisLine={false}
                            tickLine={false}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend wrapperStyle={{fontSize: "14px"}}/>
                        <Line type="monotone" dataKey={dataKey} name={chartTitle} stroke="#00ff88" strokeWidth={2} dot={false} />
                    </LineChart>
                </ResponsiveContainer>
            </div>
        </div>
    );
};
