import React, { useState, useEffect, useCallback } from 'react';
import { useDebounce } from '../lib/utils';
import { CloseIcon } from './Icons';

// Simple parser for minute inputs
const parseMinutes = (input: string): number | null => {
    if (!input.trim()) return null;
    // Allow only digits
    const cleaned = input.replace(/[^0-9]/g, '');
    if (cleaned === '') return null;
    const num = parseInt(cleaned, 10);
    return isNaN(num) ? null : num;
};

interface VideoDurationMinutesFilterProps {
  onFilterChange: (minMinutes: number, maxMinutes: number | null) => void;
  listId: string; // To scope localStorage
}

const getInitialValue = (key: string, fallback: any): string => {
    try {
        const item = window.localStorage.getItem(key);
        return item !== null ? JSON.parse(item) : String(fallback || '');
    } catch (error) {
        console.warn("Error reading from localStorage", error);
        return String(fallback || '');
    }
};

export const VideoDurationMinutesFilter: React.FC<VideoDurationMinutesFilterProps> = ({
  onFilterChange,
  listId
}) => {
  const minStorageKey = `duration_filter_min_${listId}`;
  const maxStorageKey = `duration_filter_max_${listId}`;

  const [minInput, setMinInput] = useState(() => getInitialValue(minStorageKey, '1'));
  const [maxInput, setMaxInput] = useState(() => getInitialValue(maxStorageKey, ''));
  const [error, setError] = useState<string | null>(null);

  const debouncedMin = useDebounce(minInput, 300);
  const debouncedMax = useDebounce(maxInput, 300);

  useEffect(() => {
    const min = parseMinutes(debouncedMin);
    const max = parseMinutes(debouncedMax);
    
    // Default min to 1 if empty or less than 1, as requested.
    const finalMin = min === null ? 1 : Math.max(1, min);
    const finalMax = max;

    try {
        window.localStorage.setItem(minStorageKey, JSON.stringify(debouncedMin));
        window.localStorage.setItem(maxStorageKey, JSON.stringify(debouncedMax));
    } catch (e) {
        console.warn("Error writing to localStorage", e);
    }
    
    if (finalMax !== null && finalMin > finalMax) {
      setError('Min duration cannot be greater than max.');
      return; 
    }
    
    setError(null);
    onFilterChange(finalMin, finalMax);

  }, [debouncedMin, debouncedMax, onFilterChange, minStorageKey, maxStorageKey]);

  const handleReset = useCallback(() => {
      setMinInput('1');
      setMaxInput('');
      setError(null);
  }, []);
  
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'r') {
        e.preventDefault();
        handleReset();
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleReset]);


  const hasValue = minInput !== '1' || maxInput.length > 0;

  return (
    <div className="flex flex-col items-start">
        <div className={`flex items-center gap-2 bg-[#1a1a1a] border p-1 rounded-lg ${error ? 'border-red-500/50' : 'border-dark-border'}`}>
            <span className="text-sm font-semibold text-gray-400 pl-2">Duration</span>
            <input
                type="text"
                value={minInput}
                onChange={(e) => setMinInput(e.target.value)}
                placeholder="1 min"
                className="w-20 bg-dark-card text-white text-center rounded-md p-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-accent transition-shadow"
                aria-label="Minimum duration in minutes"
            />
            <span className="text-gray-400 font-medium text-sm">TO</span>
             <input
                type="text"
                value={maxInput}
                onChange={(e) => setMaxInput(e.target.value)}
                placeholder="60+ min"
                className="w-20 bg-dark-card text-white text-center rounded-md p-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-accent transition-shadow"
                aria-label="Maximum duration in minutes"
            />
            {hasValue && (
                 <button onClick={handleReset} className="p-1 rounded-full text-gray-400 hover:bg-gray-700 hover:text-white transition-colors" aria-label="Clear duration filter">
                    <CloseIcon className="w-4 h-4"/>
                </button>
            )}
        </div>
        {error && <p className="text-red-400 text-xs mt-1.5">{error}</p>}
    </div>
  );
};
