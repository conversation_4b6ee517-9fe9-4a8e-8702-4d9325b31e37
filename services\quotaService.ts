/**
 * API Key Quota Management Service
 * Handles daily quota resets, usage tracking, and quota validation
 */

import { supabase } from '../lib/supabase';
import type { ApiKey } from '../types';

export interface QuotaStatus {
    current_usage: number;
    quota_limit: number;
    remaining_quota: number;
    usage_percentage: number;
    last_reset_at: string;
    next_reset_at: string;
    needs_reset: boolean;
}

export interface QuotaResetResult {
    reset_count: number;
    total_keys: number;
}

export interface QuotaResetLog {
    id: string;
    api_key_id?: string;
    reset_at: string;
    previous_usage?: number;
    batch_reset: boolean;
    keys_reset?: number;
    total_keys?: number;
    created_at: string;
}

export const quotaService = {
    /**
     * Get comprehensive quota status for an API key
     */
    getQuotaStatus: async (apiKeyId: string): Promise<QuotaStatus> => {
        const { data, error } = await supabase
            .rpc('get_quota_status', { api_key_id: apiKeyId });
        
        if (error) throw new Error(`Failed to get quota status: ${error.message}`);
        if (!data || data.length === 0) throw new Error('API key not found');
        
        return data[0];
    },

    /**
     * Check if an API key needs quota reset
     */
    needsQuotaReset: async (apiKeyId: string): Promise<boolean> => {
        const { data, error } = await supabase
            .rpc('needs_quota_reset', { api_key_id: apiKeyId });
        
        if (error) throw new Error(`Failed to check quota reset status: ${error.message}`);
        return data;
    },

    /**
     * Reset quota for a specific API key
     */
    resetApiKeyQuota: async (apiKeyId: string): Promise<void> => {
        const { error } = await supabase
            .rpc('reset_api_key_quota', { api_key_id: apiKeyId });
        
        if (error) throw new Error(`Failed to reset API key quota: ${error.message}`);
    },

    /**
     * Reset quotas for all API keys (daily reset)
     */
    resetAllQuotas: async (): Promise<QuotaResetResult> => {
        const { data, error } = await supabase
            .rpc('reset_all_api_key_quotas');
        
        if (error) throw new Error(`Failed to reset all quotas: ${error.message}`);
        if (!data || data.length === 0) throw new Error('No data returned from quota reset');
        
        return data[0];
    },

    /**
     * Validate if an API operation can be performed within quota limits
     */
    validateQuotaUsage: async (apiKeyId: string, operationCost: number): Promise<{
        canProceed: boolean;
        quotaStatus: QuotaStatus;
        reason?: string;
    }> => {
        try {
            const quotaStatus = await quotaService.getQuotaStatus(apiKeyId);
            
            // Check if quota reset is needed
            if (quotaStatus.needs_reset) {
                await quotaService.resetApiKeyQuota(apiKeyId);
                // Get updated status after reset
                const updatedStatus = await quotaService.getQuotaStatus(apiKeyId);
                return {
                    canProceed: operationCost <= updatedStatus.remaining_quota,
                    quotaStatus: updatedStatus,
                    reason: operationCost > updatedStatus.remaining_quota ? 
                        `Operation requires ${operationCost} quota but only ${updatedStatus.remaining_quota} remaining` : undefined
                };
            }
            
            const canProceed = operationCost <= quotaStatus.remaining_quota;
            return {
                canProceed,
                quotaStatus,
                reason: !canProceed ? 
                    `Operation requires ${operationCost} quota but only ${quotaStatus.remaining_quota} remaining` : undefined
            };
        } catch (error) {
            throw new Error(`Failed to validate quota usage: ${(error as Error).message}`);
        }
    },

    /**
     * Increment API key usage and validate quota
     */
    incrementUsage: async (apiKeyId: string, usageCost: number): Promise<{
        success: boolean;
        newUsage: number;
        quotaStatus: QuotaStatus;
        reason?: string;
    }> => {
        try {
            // First validate if the operation can proceed
            const validation = await quotaService.validateQuotaUsage(apiKeyId, usageCost);
            
            if (!validation.canProceed) {
                return {
                    success: false,
                    newUsage: validation.quotaStatus.current_usage,
                    quotaStatus: validation.quotaStatus,
                    reason: validation.reason
                };
            }
            
            // Increment usage
            const newUsage = validation.quotaStatus.current_usage + usageCost;
            const { error } = await supabase
                .from('api_keys')
                .update({ usage: newUsage })
                .eq('id', apiKeyId);
            
            if (error) throw new Error(`Failed to update usage: ${error.message}`);
            
            // Get updated quota status
            const updatedStatus = await quotaService.getQuotaStatus(apiKeyId);
            
            return {
                success: true,
                newUsage: updatedStatus.current_usage,
                quotaStatus: updatedStatus
            };
        } catch (error) {
            throw new Error(`Failed to increment usage: ${(error as Error).message}`);
        }
    },

    /**
     * Get quota reset logs for monitoring
     */
    getQuotaResetLogs: async (limit: number = 50, apiKeyId?: string): Promise<QuotaResetLog[]> => {
        let query = supabase
            .from('quota_reset_logs')
            .select('*')
            .order('reset_at', { ascending: false })
            .limit(limit);
        
        if (apiKeyId) {
            query = query.eq('api_key_id', apiKeyId);
        }
        
        const { data, error } = await query;
        
        if (error) throw new Error(`Failed to get quota reset logs: ${error.message}`);
        return data || [];
    },

    /**
     * Get quota statistics for all API keys
     */
    getQuotaStatistics: async (): Promise<{
        totalKeys: number;
        keysNeedingReset: number;
        averageUsage: number;
        highUsageKeys: number; // Keys using >80% of quota
        totalUsage: number;
        totalQuota: number;
    }> => {
        const { data: apiKeys, error } = await supabase
            .from('api_keys')
            .select('usage, quota_limit, last_reset_at');
        
        if (error) throw new Error(`Failed to get quota statistics: ${error.message}`);
        if (!apiKeys) return {
            totalKeys: 0,
            keysNeedingReset: 0,
            averageUsage: 0,
            highUsageKeys: 0,
            totalUsage: 0,
            totalQuota: 0
        };
        
        const now = new Date();
        const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        
        const totalKeys = apiKeys.length;
        const keysNeedingReset = apiKeys.filter(key => 
            new Date(key.last_reset_at) < twentyFourHoursAgo
        ).length;
        const totalUsage = apiKeys.reduce((sum, key) => sum + key.usage, 0);
        const totalQuota = apiKeys.reduce((sum, key) => sum + key.quota_limit, 0);
        const averageUsage = totalKeys > 0 ? totalUsage / totalKeys : 0;
        const highUsageKeys = apiKeys.filter(key => 
            (key.usage / key.quota_limit) > 0.8
        ).length;
        
        return {
            totalKeys,
            keysNeedingReset,
            averageUsage: Math.round(averageUsage),
            highUsageKeys,
            totalUsage,
            totalQuota
        };
    },

    /**
     * Schedule daily quota reset (to be called by a cron job or scheduler)
     */
    scheduleDailyReset: async (): Promise<{
        success: boolean;
        result?: QuotaResetResult;
        error?: string;
    }> => {
        try {
            console.log('🔄 Starting daily quota reset...');
            const result = await quotaService.resetAllQuotas();
            
            console.log(`✅ Daily quota reset completed: ${result.reset_count}/${result.total_keys} keys reset`);
            
            return {
                success: true,
                result
            };
        } catch (error) {
            const errorMessage = `Failed to perform daily quota reset: ${(error as Error).message}`;
            console.error('❌', errorMessage);
            
            return {
                success: false,
                error: errorMessage
            };
        }
    },

    /**
     * Initialize quota management system
     */
    initializeQuotaSystem: async (): Promise<void> => {
        try {
            console.log('🚀 Initializing quota management system...');
            
            // Check if any API keys need immediate reset
            const stats = await quotaService.getQuotaStatistics();
            
            if (stats.keysNeedingReset > 0) {
                console.log(`⚠️ Found ${stats.keysNeedingReset} API keys needing quota reset`);
                const result = await quotaService.resetAllQuotas();
                console.log(`✅ Reset ${result.reset_count} API keys during initialization`);
            }
            
            console.log('✅ Quota management system initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize quota management system:', error);
            throw error;
        }
    }
};

/**
 * Quota management constants
 */
export const QUOTA_CONSTANTS = {
    DEFAULT_DAILY_QUOTA: 10000,
    RESET_INTERVAL_HOURS: 24,
    HIGH_USAGE_THRESHOLD: 0.8, // 80%
    WARNING_THRESHOLD: 0.9, // 90%
    OPERATION_COSTS: {
        SEARCH: 100,
        CHANNELS: 1,
        VIDEOS: 1,
        PLAYLIST: 1,
        CHANNEL_DETAILS: 1
    }
} as const;
