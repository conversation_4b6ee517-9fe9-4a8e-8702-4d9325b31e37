/* Enhanced Tracked Channels Page Styles */

/* Line clamp utility for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced animations and transitions */
.hover-scale {
  transition: transform 0.25s ease-in-out, box-shadow 0.25s ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Enhanced card shadows */
.card-shadow-hover {
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.3),
    0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* Smooth animations for enhanced interactions */
@keyframes pulse-green {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-green {
  animation: pulse-green 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced button hover effects */
.btn-primary-enhanced {
  background: #10b981;
  transition: all 0.2s ease;
}

.btn-primary-enhanced:hover {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Context menu improvements */
.context-menu {
  backdrop-filter: blur(8px);
  background: rgba(42, 42, 42, 0.95);
}

/* Pin indicator animation */
@keyframes pin-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

.pin-bounce {
  animation: pin-bounce 0.6s ease-in-out;
}

/* Enhanced focus states for accessibility */
.focus-ring-enhanced:focus {
  outline: 2px solid #10b981;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}

/* Gradient backgrounds for enhanced visual appeal */
.gradient-card-enhanced {
  background: linear-gradient(135deg, #1a1a1a 0%, #151515 100%);
}

.gradient-pinned {
  background: linear-gradient(135deg, #1a1a1a 0%, rgba(16, 185, 129, 0.05) 100%);
}

/* Loading skeleton animation */
@keyframes shimmer-enhanced {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-shimmer-enhanced {
  background: linear-gradient(90deg, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%);
  background-size: 200px 100%;
  animation: shimmer-enhanced 1.5s infinite;
}

/* Enhanced responsive utilities */
@media (max-width: 767px) {
  .mobile-grid-enhanced {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .mobile-padding-enhanced {
    padding: 1rem;
  }
  
  .mobile-text-enhanced {
    font-size: 0.875rem;
  }
}

@media (min-width: 768px) and (max-width: 1199px) {
  .tablet-grid-enhanced {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1200px) {
  .desktop-grid-enhanced {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }
}

/* Enhanced dark theme variables */
:root {
  --bg-primary-enhanced: #0f0f0f;
  --bg-secondary-enhanced: #1a1a1a;
  --bg-tertiary-enhanced: #151515;
  --border-primary-enhanced: #333;
  --border-secondary-enhanced: #2a2a2a;
  --text-primary-enhanced: #ffffff;
  --text-secondary-enhanced: #9ca3af;
  --text-tertiary-enhanced: #6b7280;
  --accent-primary-enhanced: #10b981;
  --accent-hover-enhanced: #059669;
  --accent-light-enhanced: rgba(16, 185, 129, 0.1);
}

/* Enhanced scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary-enhanced);
}

::-webkit-scrollbar-thumb {
  background: var(--border-primary-enhanced);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary-enhanced);
}

/* Enhanced card interactions */
.card-interactive-enhanced {
  transition: all 0.25s ease;
  cursor: pointer;
}

.card-interactive-enhanced:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.card-interactive-enhanced:active {
  transform: scale(0.98);
}

/* Status indicators */
.status-active-enhanced {
  background: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.status-inactive-enhanced {
  background: #6b7280;
}

/* Enhanced typography */
.text-gradient-enhanced {
  background: linear-gradient(135deg, #ffffff 0%, #9ca3af 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Micro-interactions */
.bounce-in-enhanced {
  animation: bounceInEnhanced 0.6s ease-out;
}

@keyframes bounceInEnhanced {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Fade in animation for page load */
.fade-in-enhanced {
  animation: fadeInEnhanced 0.5s ease-out;
}

@keyframes fadeInEnhanced {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced button states */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Enhanced menu animations */
.menu-enter {
  animation: menuEnter 0.2s ease-out;
}

@keyframes menuEnter {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced hover states */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced focus indicators */
.focus-visible-enhanced:focus-visible {
  outline: 2px solid #10b981;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}
