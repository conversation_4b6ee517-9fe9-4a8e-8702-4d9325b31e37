/**
 * Environment variable validation and type definitions
 * This ensures type safety and validates required environment variables
 */

// ImportMetaEnv interface is defined in vite-env.d.ts

// Interface for ImportMeta is already defined globally in vite-env.d.ts

/**
 * Validates that all required environment variables are present
 * @throws Error if any required environment variable is missing
 */
export function validateEnvironmentVariables(): void {
  const requiredVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ] as const;

  const missingVars = requiredVars.filter(
    varName => !import.meta.env[varName]
  );

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}\n` +
      'Please check your .env.local file and ensure all required variables are set.'
    );
  }
}

/**
 * Get validated environment configuration
 */
export function getEnvironmentConfig() {
  validateEnvironmentVariables();
  
  return {
    supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
    supabaseAnonKey: import.meta.env.VITE_SUPABASE_ANON_KEY,
    nodeEnv: import.meta.env.NODE_ENV || 'development',
    isDevelopment: import.meta.env.NODE_ENV === 'development',
    isProduction: import.meta.env.NODE_ENV === 'production',
  } as const;
}
