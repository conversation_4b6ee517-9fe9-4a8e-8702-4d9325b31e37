


import React, { useState, useEffect, useCallback } from 'react';
import type { Folder, SavedVideoWithTags } from '../types';
import { savedContentService } from '../services/savedContentService';
import { LoadingSpinner } from './LoadingSpinner';
import { BackArrowIcon, PlusIcon, MinusIcon } from './Icons';
import { SavedVideoCard } from './SavedVideoCard';

// --- Types & Constants ---
type GridLayout = '2-grid' | '4-grid' | '6-grid';
const GRID_LAYOUTS: GridLayout[] = ['2-grid', '4-grid', '6-grid'];
const GRID_LAYOUT_CLASSES: Record<GridLayout, string> = {
    '2-grid': 'grid-cols-1 md:grid-cols-2 gap-6',
    '4-grid': 'grid-cols-2 md:grid-cols-4 gap-4',
    '6-grid': 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3',
};
const LAYOUT_NUMBER_MAP: Record<GridLayout, number> = { '2-grid': 2, '4-grid': 4, '6-grid': 6 };

// --- Custom Hook for Grid Layout ---
const useGridLayout = (folderId: string) => {
    const getInitialLayout = (): GridLayout => {
        try {
            const savedLayout = localStorage.getItem(`video_grid_layout_saved_${folderId}`);
            return GRID_LAYOUTS.includes(savedLayout as GridLayout) ? (savedLayout as GridLayout) : '4-grid';
        } catch (e) {
            return '4-grid';
        }
    };

    const [layout, setLayout] = useState<GridLayout>(getInitialLayout);

    const updateLayout = useCallback((newLayout: GridLayout) => {
        setLayout(newLayout);
        try {
            localStorage.setItem(`video_grid_layout_saved_${folderId}`, newLayout);
        } catch (e) {
            console.warn("Could not save layout to localStorage", e);
        }
    }, [folderId]);

    const changeLayout = (direction: 'increase' | 'decrease') => {
        const currentIndex = GRID_LAYOUTS.indexOf(layout);
        if (direction === 'increase' && currentIndex < GRID_LAYOUTS.length - 1) {
            updateLayout(GRID_LAYOUTS[currentIndex + 1]);
        }
        if (direction === 'decrease' && currentIndex > 0) {
            updateLayout(GRID_LAYOUTS[currentIndex - 1]);
        }
    };

    return { layout, changeLayout };
};

// --- Grid Controls Component ---
const GridControls: React.FC<{ layout: GridLayout; onChangeLayout: (dir: 'increase' | 'decrease') => void; disabled?: boolean; }> = ({ layout, onChangeLayout, disabled }) => {
    const currentNumber = LAYOUT_NUMBER_MAP[layout];
    const canDecrease = layout !== GRID_LAYOUTS[0];
    const canIncrease = layout !== GRID_LAYOUTS[GRID_LAYOUTS.length - 1];
    
    return (
        <div className="flex items-center space-x-1 bg-[#1a1a1a] border border-dark-border rounded-lg p-1">
            <button 
                onClick={() => onChangeLayout('decrease')} 
                disabled={!canDecrease || disabled}
                className="p-1.5 text-gray-400 rounded-md hover:bg-gray-700 hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="Decrease grid columns"
            >
                <MinusIcon className="w-4 h-4" />
            </button>
            <span className="px-2 text-sm font-semibold text-white w-5 text-center">{currentNumber}</span>
             <button 
                onClick={() => onChangeLayout('increase')} 
                disabled={!canIncrease || disabled}
                className="p-1.5 text-gray-400 rounded-md hover:bg-gray-700 hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="Increase grid columns"
            >
                <PlusIcon className="w-4 h-4" />
            </button>
        </div>
    );
};


interface SavedVideosListPageProps {
    folder: Folder;
    onBack: () => void;
}

export const SavedVideosListPage: React.FC<SavedVideosListPageProps> = ({ folder, onBack }) => {
    const [videos, setVideos] = useState<SavedVideoWithTags[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const { layout, changeLayout } = useGridLayout(folder.id);

    const fetchVideos = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const fetchedVideos = await savedContentService.getVideosByFolderId(folder.id);
            setVideos(fetchedVideos);
        } catch (err) {
            setError('Failed to load saved videos.');
            console.error(err);
        } finally {
            setLoading(false);
        }
    }, [folder.id]);

    useEffect(() => {
        fetchVideos();
    }, [fetchVideos]);

    const handleDeleteVideo = async (videoId: string) => {
        try {
            await savedContentService.deleteSavedVideo(videoId);
            setVideos(prev => prev.filter(v => v.id !== videoId));
        } catch (err) {
            alert('Failed to delete video: ' + (err as Error).message);
        }
    };
    
    const handleUpdateNotes = async (videoId: string, newNotes: string) => {
        try {
            const updatedVideo = await savedContentService.updateSavedVideoNotes(videoId, newNotes);
            setVideos(prev => prev.map(v => v.id === videoId ? {...v, notes: updatedVideo.notes} : v));
        } catch (err) {
             alert('Failed to update notes: ' + (err as Error).message);
        }
    };


    const renderContent = () => {
        if (loading) {
            return <div className="flex-1 flex items-center justify-center"><LoadingSpinner /></div>;
        }
        if (error) {
            return <div className="flex-1 flex items-center justify-center text-red-500">{error}</div>;
        }
        if (videos.length === 0) {
            return (
                <div className="text-center py-16 text-gray-500">
                    <p>No videos saved in this folder yet.</p>
                    <p className="mt-2 text-sm">Click "Save a video" on the main page to get started.</p>
                </div>
            );
        }
        return (
             <div className={`grid ${GRID_LAYOUT_CLASSES[layout]} transition-all duration-300`}>
                {videos.map(video => (
                    <SavedVideoCard 
                        key={video.id} 
                        video={video} 
                        layout={layout}
                        onDelete={handleDeleteVideo}
                        onUpdateNotes={handleUpdateNotes}
                    />
                ))}
            </div>
        );
    }

    return (
         <div className="flex-1 p-4 sm:p-6 lg:p-8 space-y-6 flex flex-col">
            <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                 <div className="flex items-center space-x-4">
                    <button onClick={onBack} className="p-2 rounded-full hover:bg-dark-card transition-colors" aria-label="Back to folders">
                        <BackArrowIcon className="w-6 h-6" />
                    </button>
                    <h2 className="text-3xl font-bold text-white">{folder.name}</h2>
                </div>
                <GridControls layout={layout} onChangeLayout={changeLayout} disabled={loading || videos.length === 0} />
            </header>
            <div className="flex-1 overflow-y-auto -mr-4 pr-4">
               {renderContent()}
            </div>
        </div>
    );
};
