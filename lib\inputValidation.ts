/**
 * Input Validation and Sanitization Utilities
 * Provides comprehensive validation for all user inputs
 */

// Regular expressions for validation
const YOUTUBE_VIDEO_URL_REGEX = /^https?:\/\/(?:www\.)?(?:youtube\.com\/(?:watch\?v=|embed\/|v\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})(?:\S+)?$/;
const YOUTUBE_CHANNEL_URL_REGEX = /^https?:\/\/(?:www\.)?youtube\.com\/(?:c\/|channel\/|user\/|@)([a-zA-Z0-9_.-]+)$/;
const YOUTUBE_HANDLE_REGEX = /^@[a-zA-Z0-9_.-]{1,30}$/;
const SAFE_NAME_REGEX = /^[a-zA-Z0-9\s\-_.,!?()[\]{}'"]+$/;
const SQL_INJECTION_PATTERNS = [
  /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
  /(--|\/\*|\*\/|;|'|"|`)/,
  /(\bOR\b|\bAND\b).*[=<>]/i
];

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  error?: string;
  sanitized?: string;
}

/**
 * Sanitizes a string by removing potentially dangerous characters
 */
export const sanitizeString = (input: string): string => {
  if (typeof input !== 'string') return '';
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove HTML brackets
    .replace(/[&]/g, '&amp;') // Escape ampersands
    .replace(/['"]/g, '') // Remove quotes
    .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
    .substring(0, 1000); // Limit length
};

/**
 * Validates and sanitizes a name (folder, tag, channel list name)
 */
export const validateName = (name: string): ValidationResult => {
  if (!name || typeof name !== 'string') {
    return { isValid: false, error: 'Name is required' };
  }
  
  const trimmed = name.trim();
  
  if (trimmed.length === 0) {
    return { isValid: false, error: 'Name cannot be empty' };
  }
  
  if (trimmed.length > 100) {
    return { isValid: false, error: 'Name must be 100 characters or less' };
  }
  
  if (!SAFE_NAME_REGEX.test(trimmed)) {
    return { isValid: false, error: 'Name contains invalid characters. Only letters, numbers, spaces, and basic punctuation are allowed' };
  }
  
  // Check for SQL injection patterns
  for (const pattern of SQL_INJECTION_PATTERNS) {
    if (pattern.test(trimmed)) {
      return { isValid: false, error: 'Name contains potentially dangerous content' };
    }
  }
  
  return { isValid: true, sanitized: sanitizeString(trimmed) };
};

/**
 * Validates YouTube video URL
 */
export const validateYouTubeVideoUrl = (url: string): ValidationResult => {
  if (!url || typeof url !== 'string') {
    return { isValid: false, error: 'URL is required' };
  }
  
  const trimmed = url.trim();
  
  if (!YOUTUBE_VIDEO_URL_REGEX.test(trimmed)) {
    return { isValid: false, error: 'Invalid YouTube video URL format' };
  }
  
  // Extract video ID for additional validation
  const match = trimmed.match(YOUTUBE_VIDEO_URL_REGEX);
  if (!match || !match[1] || match[1].length !== 11) {
    return { isValid: false, error: 'Invalid YouTube video ID' };
  }
  
  return { isValid: true, sanitized: trimmed };
};

/**
 * Validates YouTube channel URL or handle
 */
export const validateYouTubeChannelQuery = (query: string): ValidationResult => {
  if (!query || typeof query !== 'string') {
    return { isValid: false, error: 'Channel query is required' };
  }
  
  const trimmed = query.trim();
  
  if (trimmed.length === 0) {
    return { isValid: false, error: 'Channel query cannot be empty' };
  }
  
  if (trimmed.length > 500) {
    return { isValid: false, error: 'Channel query is too long' };
  }
  
  // Check if it's a URL
  if (trimmed.startsWith('http')) {
    if (!YOUTUBE_CHANNEL_URL_REGEX.test(trimmed)) {
      return { isValid: false, error: 'Invalid YouTube channel URL format' };
    }
  }
  // Check if it's a handle
  else if (trimmed.startsWith('@')) {
    if (!YOUTUBE_HANDLE_REGEX.test(trimmed)) {
      return { isValid: false, error: 'Invalid YouTube handle format' };
    }
  }
  // Otherwise it's a search term - basic validation
  else {
    if (!SAFE_NAME_REGEX.test(trimmed)) {
      return { isValid: false, error: 'Search query contains invalid characters' };
    }
  }
  
  // Check for SQL injection patterns
  for (const pattern of SQL_INJECTION_PATTERNS) {
    if (pattern.test(trimmed)) {
      return { isValid: false, error: 'Query contains potentially dangerous content' };
    }
  }
  
  return { isValid: true, sanitized: sanitizeString(trimmed) };
};

/**
 * Validates notes/description text
 */
export const validateNotes = (notes: string): ValidationResult => {
  if (!notes) {
    return { isValid: true, sanitized: '' };
  }
  
  if (typeof notes !== 'string') {
    return { isValid: false, error: 'Notes must be text' };
  }
  
  if (notes.length > 2000) {
    return { isValid: false, error: 'Notes must be 2000 characters or less' };
  }
  
  // Check for SQL injection patterns
  for (const pattern of SQL_INJECTION_PATTERNS) {
    if (pattern.test(notes)) {
      return { isValid: false, error: 'Notes contain potentially dangerous content' };
    }
  }
  
  return { isValid: true, sanitized: sanitizeString(notes) };
};

/**
 * Validates numeric input (for filters)
 */
export const validateNumericInput = (value: string | number, min?: number, max?: number): ValidationResult => {
  if (value === '' || value === null || value === undefined) {
    return { isValid: true, sanitized: '' };
  }
  
  const num = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(num)) {
    return { isValid: false, error: 'Must be a valid number' };
  }
  
  if (min !== undefined && num < min) {
    return { isValid: false, error: `Must be at least ${min}` };
  }
  
  if (max !== undefined && num > max) {
    return { isValid: false, error: `Must be at most ${max}` };
  }
  
  return { isValid: true, sanitized: num.toString() };
};

/**
 * Validates API key format
 */
export const validateApiKey = (key: string): ValidationResult => {
  if (!key || typeof key !== 'string') {
    return { isValid: false, error: 'API key is required' };
  }
  
  const trimmed = key.trim();
  
  if (!/^AIza[0-9A-Za-z-_]{35}$/.test(trimmed)) {
    return { isValid: false, error: 'Invalid YouTube API key format' };
  }
  
  return { isValid: true, sanitized: trimmed };
};

/**
 * Batch validation for multiple inputs
 */
export const validateBatch = (validations: Array<() => ValidationResult>): ValidationResult[] => {
  return validations.map(validate => validate());
};

/**
 * Check if all validations passed
 */
export const allValidationsPassed = (results: ValidationResult[]): boolean => {
  return results.every(result => result.isValid);
};

/**
 * Get first validation error
 */
export const getFirstValidationError = (results: ValidationResult[]): string | null => {
  const failed = results.find(result => !result.isValid);
  return failed?.error || null;
};
