
import React from 'react';

const codeSnippet = `
const supabaseUrl = 'YOUR_SUPABASE_URL';
const supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
`;

export const SupabaseSetupNotice: React.FC = () => {
  return (
    <div className="w-full h-screen flex items-center justify-center bg-[#0d0d0d] p-4">
      <div className="max-w-2xl w-full bg-dark-card border border-dark-border rounded-lg p-8 text-center shadow-2xl">
        <h1 className="text-3xl font-bold text-red-400 mb-4">Configuration Required</h1>
        <p className="text-gray-300 mb-6 text-lg">
          The application cannot connect to the backend because Supabase credentials are missing.
        </p>
        <div className="text-left bg-[#1a1a1a] p-4 rounded-lg border border-dark-border">
          <p className="text-gray-400 mb-2">Please open the following file in your code editor:</p>
          <code className="text-accent bg-black/30 px-2 py-1 rounded-md">
            lib/supabase.ts
          </code>
          <p className="text-gray-400 mt-4 mb-2">Then, replace the placeholder values with your actual Supabase Project URL and Anon Key:</p>
          <pre className="bg-black/50 p-4 rounded-md overflow-x-auto">
            <code className="text-gray-300 font-mono text-sm whitespace-pre-wrap">
              {codeSnippet.trim()}
            </code>
          </pre>
        </div>
        <p className="text-gray-500 mt-6 text-sm">
          You can find these credentials in your Supabase project's API settings. The app will reload automatically when you save the changes.
        </p>
      </div>
    </div>
  );
};
