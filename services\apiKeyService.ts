import type { <PERSON><PERSON><PERSON><PERSON> } from '../types';
import { supabase } from '../lib/supabase';
import {
  encryptApiKey,
  decryptApiKey,
  validateApiKeyFormat,
  validateApiUsage,
  API_USAGE_LIMITS,
  performSecurityCheck
} from '../lib/apiKeySecurity';
import { validateName } from '../lib/inputValidation';

export const ACTIVE_API_KEY_ID_STORAGE_KEY = 'youtube_active_api_key_id';

export const apiKeyService = {
    getApiKeys: async (): Promise<ApiKey[]> => {
        const { data, error } = await supabase
            .from('api_keys')
            .select('*')
            .order('created_at', { ascending: true });
        if (error) throw new Error(error.message);

        // Decrypt API keys and perform security checks
        return data.map(key => {
            try {
                const decryptedKey = decryptApiKey(key.key);
                const securityWarnings = performSecurityCheck(key);

                if (securityWarnings.length > 0) {
                    console.warn(`Security warnings for API key "${key.name}":`, securityWarnings);
                }

                return {
                    ...key,
                    key: decryptedKey
                };
            } catch (error) {
                console.error(`Failed to decrypt API key "${key.name}":`, error);
                // Return key with placeholder to avoid breaking the UI
                return {
                    ...key,
                    key: '[ENCRYPTED - DECRYPTION FAILED]'
                };
            }
        });
    },

    getActiveApiKey: async (): Promise<ApiKey> => {
        let activeKeyId = localStorage.getItem(ACTIVE_API_KEY_ID_STORAGE_KEY);

        if (!activeKeyId) {
            const { data: keys, error } = await supabase
                .from('api_keys')
                .select('*')
                .order('created_at', { ascending: true })
                .limit(1);

            if (error) throw new Error(error.message);

            if (keys && keys.length > 0) {
                const firstKey = keys[0];
                localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, firstKey.id);

                // Decrypt and validate the key
                try {
                    const decryptedKey = decryptApiKey(firstKey.key);
                    return { ...firstKey, key: decryptedKey };
                } catch (error) {
                    throw new Error(`Failed to decrypt API key: ${error}`);
                }
            } else {
                throw new Error("No API key found. Please add one in Settings to continue.");
            }
        }

        const { data: keyData, error: keyError } = await supabase
            .from('api_keys')
            .select('*')
            .eq('id', activeKeyId)
            .single();

        if (keyError || !keyData) {
            localStorage.removeItem(ACTIVE_API_KEY_ID_STORAGE_KEY);

            // Try to find a fallback key
            const { data: fallbackKeys, error: fallbackError } = await supabase
                .from('api_keys')
                .select('*')
                .order('created_at', { ascending: true })
                .limit(1);

            if (fallbackError) throw new Error(fallbackError.message);

            if (fallbackKeys && fallbackKeys.length > 0) {
                const fallbackKey = fallbackKeys[0];
                localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, fallbackKey.id);

                try {
                    const decryptedKey = decryptApiKey(fallbackKey.key);
                    return { ...fallbackKey, key: decryptedKey };
                } catch (error) {
                    throw new Error(`Failed to decrypt fallback API key: ${error}`);
                }
            }

            throw new Error("The active API key is invalid and no other keys were found. Please add a new key in Settings.");
        }

        // Decrypt and validate the key
        try {
            const decryptedKey = decryptApiKey(keyData.key);

            // Check usage limits
            if (!validateApiUsage(keyData.usage, 0)) {
                throw new Error(`API key "${keyData.name}" has exceeded daily quota limit`);
            }

            return { ...keyData, key: decryptedKey };
        } catch (error) {
            throw new Error(`Failed to decrypt API key: ${error}`);
        }
    },

    addApiKey: async (name: string, key: string): Promise<ApiKey> => {
        // Validate API key name
        const nameValidation = validateName(name);
        if (!nameValidation.isValid) {
            throw new Error(`Invalid API key name: ${nameValidation.error}`);
        }

        // Validate API key format
        if (!validateApiKeyFormat(key)) {
            throw new Error('Invalid YouTube API key format. Keys should start with "AIza" and be 39 characters long.');
        }

        const sanitizedName = nameValidation.sanitized!;

        // Encrypt the API key before storing
        const encryptedKey = encryptApiKey(key);

        const { data, error } = await supabase
            .from('api_keys')
            .insert({ name: sanitizedName, key: encryptedKey })
            .select()
            .single();

        if (error) {
            if (error.code === '23505') { // unique_violation on 'key'
                throw new Error('This API key already exists.');
            }
            throw new Error(error.message);
        }

        // Return with decrypted key for immediate use
        return { ...data, key };
    },

    deleteApiKey: async (id: string): Promise<{ success: true }> => {
        const { error } = await supabase.from('api_keys').delete().eq('id', id);
        if (error) throw new Error(error.message);
        return { success: true };
    },

    renameApiKey: async (id: string, newName: string): Promise<ApiKey> => {
        // Validate new API key name
        const nameValidation = validateName(newName);
        if (!nameValidation.isValid) {
            throw new Error(`Invalid API key name: ${nameValidation.error}`);
        }

        const sanitizedName = nameValidation.sanitized!;

        const { data, error } = await supabase
            .from('api_keys')
            .update({ name: sanitizedName })
            .eq('id', id)
            .select()
            .single();
        if (error) throw new Error(error.message);

        // Return with decrypted key
        try {
            const decryptedKey = decryptApiKey(data.key);
            return { ...data, key: decryptedKey };
        } catch (error) {
            console.error('Failed to decrypt API key after rename:', error);
            return data; // Return encrypted version if decryption fails
        }
    },
    
    // Note: Updating usage would typically be a backend operation
    // to avoid letting clients update their own usage counts.
    // For this client-side app, we'll include it here with validation.
    updateUsage: async (id: string, newUsage: number): Promise<ApiKey> => {
        // Validate usage limits
        if (newUsage > API_USAGE_LIMITS.DAILY_QUOTA) {
            console.warn(`API key usage (${newUsage}) exceeds daily quota (${API_USAGE_LIMITS.DAILY_QUOTA})`);
        }

        const { data, error } = await supabase
            .from('api_keys')
            .update({ usage: newUsage })
            .eq('id', id)
            .select()
            .single();
        if (error) throw new Error(error.message);

        // Return with decrypted key
        try {
            const decryptedKey = decryptApiKey(data.key);
            return { ...data, key: decryptedKey };
        } catch (error) {
            console.error('Failed to decrypt API key after usage update:', error);
            return data; // Return encrypted version if decryption fails
        }
    },

    // New method to validate API operation before execution
    validateOperation: async (operationCost: number): Promise<{ canProceed: boolean; remainingQuota: number; warning?: string }> => {
        try {
            const activeKey = await apiKeyService.getActiveApiKey();
            const canProceed = validateApiUsage(activeKey.usage, operationCost);
            const remainingQuota = API_USAGE_LIMITS.DAILY_QUOTA - activeKey.usage;

            let warning: string | undefined;
            if (!canProceed) {
                warning = `Operation would exceed daily quota. Current usage: ${activeKey.usage}, Operation cost: ${operationCost}, Daily limit: ${API_USAGE_LIMITS.DAILY_QUOTA}`;
            } else if (remainingQuota < operationCost * 2) {
                warning = `Low quota warning: Only ${remainingQuota} quota units remaining`;
            }

            return { canProceed, remainingQuota, warning };
        } catch (error) {
            return { canProceed: false, remainingQuota: 0, warning: `Failed to validate API key: ${error}` };
        }
    }
};