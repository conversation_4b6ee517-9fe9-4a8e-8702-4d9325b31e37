import type { <PERSON><PERSON><PERSON><PERSON> } from '../types';
import { supabase } from '../lib/supabase';

export const ACTIVE_API_KEY_ID_STORAGE_KEY = 'youtube_active_api_key_id';

export const apiKeyService = {
    getApiKeys: async (): Promise<ApiKey[]> => {
        const { data, error } = await supabase
            .from('api_keys')
            .select('*')
            .order('created_at', { ascending: true });
        if (error) throw new Error(error.message);
        return data;
    },

    getActiveApiKey: async (): Promise<ApiKey> => {
        let activeKeyId = localStorage.getItem(ACTIVE_API_KEY_ID_STORAGE_KEY);

        if (!activeKeyId) {
            const { data: keys, error } = await supabase
                .from('api_keys')
                .select('*')
                .order('created_at', { ascending: true })
                .limit(1);
            
            if (error) throw new Error(error.message);

            if (keys && keys.length > 0) {
                const firstKey = keys[0];
                localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, firstKey.id);
                return firstKey;
            } else {
                throw new Error("No API key found. Please add one in Settings to continue.");
            }
        }
        
        const { data: keyData, error: keyError } = await supabase
            .from('api_keys')
            .select('*')
            .eq('id', activeKeyId)
            .single();

        if (keyError || !keyData) {
            localStorage.removeItem(ACTIVE_API_KEY_ID_STORAGE_KEY);
            
            // Try to find a fallback key
            const { data: fallbackKeys, error: fallbackError } = await supabase
                .from('api_keys')
                .select('*')
                .order('created_at', { ascending: true })
                .limit(1);

            if (fallbackError) throw new Error(fallbackError.message);
            
            if (fallbackKeys && fallbackKeys.length > 0) {
                const fallbackKey = fallbackKeys[0];
                localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, fallbackKey.id);
                return fallbackKey;
            }
            
            throw new Error("The active API key is invalid and no other keys were found. Please add a new key in Settings.");
        }
        
        return keyData;
    },

    addApiKey: async (name: string, key: string): Promise<ApiKey> => {
        const { data, error } = await supabase
            .from('api_keys')
            .insert({ name, key })
            .select()
            .single();

        if (error) {
            if (error.code === '23505') { // unique_violation on 'key'
                throw new Error('This API key already exists.');
            }
            throw new Error(error.message);
        }
        return data;
    },

    deleteApiKey: async (id: string): Promise<{ success: true }> => {
        const { error } = await supabase.from('api_keys').delete().eq('id', id);
        if (error) throw new Error(error.message);
        return { success: true };
    },

    renameApiKey: async (id: string, newName: string): Promise<ApiKey> => {
        const { data, error } = await supabase
            .from('api_keys')
            .update({ name: newName })
            .eq('id', id)
            .select()
            .single();
        if (error) throw new Error(error.message);
        return data;
    },
    
    // Note: Updating usage would typically be a backend operation
    // to avoid letting clients update their own usage counts.
    // For this client-side app, we'll include it here.
    updateUsage: async (id: string, newUsage: number): Promise<ApiKey> => {
        const { data, error } = await supabase
            .from('api_keys')
            .update({ usage: newUsage })
            .eq('id', id)
            .select()
            .single();
        if (error) throw new Error(error.message);
        return data;
    }
};