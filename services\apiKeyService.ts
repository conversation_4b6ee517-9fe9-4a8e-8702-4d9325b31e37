import type { <PERSON><PERSON><PERSON><PERSON> } from '../types';
import { supabase } from '../lib/supabase';
import {
  encryptApi<PERSON>ey,
  decryptApiKey,
  validateApiKeyFormat,
  validateApiUsage,
  API_USAGE_LIMITS,
  performSecurityCheck,
  isKeyEncrypted,
  needsKeyMigration,
  getApiKeyErrorMessage
} from '../lib/apiKeySecurity';
import { validateName } from '../lib/inputValidation';
import { quotaService, QUOTA_CONSTANTS } from './quotaService';

export const ACTIVE_API_KEY_ID_STORAGE_KEY = 'youtube_active_api_key_id';

export const apiKeyService = {
    getApiKeys: async (): Promise<ApiKey[]> => {
        const { data, error } = await supabase
            .from('api_keys')
            .select('*')
            .order('created_at', { ascending: true });
        if (error) throw new Error(error.message);

        // Decrypt API keys and perform security checks
        return data.map(key => {
            try {
                const decryptedKey = decryptApiKey(key.key);
                const securityWarnings = performSecurityCheck(key);

                if (securityWarnings.length > 0) {
                    console.warn(`Security warnings for API key "${key.name}":`, securityWarnings);
                }

                return {
                    ...key,
                    key: decryptedKey
                };
            } catch (error) {
                console.error(`Failed to decrypt API key "${key.name}":`, error);
                // Return key with placeholder to avoid breaking the UI
                return {
                    ...key,
                    key: '[ENCRYPTED - DECRYPTION FAILED]'
                };
            }
        });
    },

    getActiveApiKey: async (): Promise<ApiKey> => {
        let activeKeyId = localStorage.getItem(ACTIVE_API_KEY_ID_STORAGE_KEY);

        if (!activeKeyId) {
            const { data: keys, error } = await supabase
                .from('api_keys')
                .select('*')
                .order('created_at', { ascending: true })
                .limit(1);

            if (error) throw new Error(error.message);

            if (keys && keys.length > 0) {
                const firstKey = keys[0];
                localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, firstKey.id);

                // Decrypt and validate the key
                try {
                    const isEncrypted = isKeyEncrypted(firstKey.key);
                    console.log(`Attempting to decrypt API key: ${firstKey.name} (encrypted: ${isEncrypted})`);

                    if (needsKeyMigration(firstKey.key)) {
                        console.warn(`🔄 API key "${firstKey.name}" is stored in plain text and should be migrated to encrypted format for better security.`);
                    }

                    const decryptedKey = decryptApiKey(firstKey.key);
                    console.log(`Successfully decrypted API key: ${firstKey.name}`);
                    return { ...firstKey, key: decryptedKey };
                } catch (error) {
                    console.error(`Failed to decrypt API key "${firstKey.name}":`, error);
                    const userFriendlyMessage = error instanceof Error ? getApiKeyErrorMessage(error, firstKey.name) : `Unknown error with API key "${firstKey.name}"`;
                    throw new Error(userFriendlyMessage);
                }
            } else {
                throw new Error("No API key found. Please add one in Settings to continue.");
            }
        }

        const { data: keyData, error: keyError } = await supabase
            .from('api_keys')
            .select('*')
            .eq('id', activeKeyId)
            .single();

        if (keyError || !keyData) {
            localStorage.removeItem(ACTIVE_API_KEY_ID_STORAGE_KEY);

            // Try to find a fallback key
            const { data: fallbackKeys, error: fallbackError } = await supabase
                .from('api_keys')
                .select('*')
                .order('created_at', { ascending: true })
                .limit(1);

            if (fallbackError) throw new Error(fallbackError.message);

            if (fallbackKeys && fallbackKeys.length > 0) {
                const fallbackKey = fallbackKeys[0];
                localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, fallbackKey.id);

                try {
                    console.log(`Attempting to decrypt fallback API key: ${fallbackKey.name} (encrypted: ${isKeyEncrypted(fallbackKey.key)})`);
                    const decryptedKey = decryptApiKey(fallbackKey.key);
                    console.log(`Successfully decrypted fallback API key: ${fallbackKey.name}`);
                    return { ...fallbackKey, key: decryptedKey };
                } catch (error) {
                    console.error(`Failed to decrypt fallback API key "${fallbackKey.name}":`, error);
                    const userFriendlyMessage = error instanceof Error ? getApiKeyErrorMessage(error, fallbackKey.name) : `Unknown error with fallback API key "${fallbackKey.name}"`;
                    throw new Error(userFriendlyMessage);
                }
            }

            throw new Error("The active API key is invalid and no other keys were found. Please add a new key in Settings.");
        }

        // Decrypt and validate the key
        try {
            console.log(`Attempting to decrypt active API key: ${keyData.name} (encrypted: ${isKeyEncrypted(keyData.key)})`);
            const decryptedKey = decryptApiKey(keyData.key);
            console.log(`Successfully decrypted active API key: ${keyData.name}`);

            // Check usage limits
            if (!validateApiUsage(keyData.usage, 0)) {
                throw new Error(`API key "${keyData.name}" has exceeded daily quota limit`);
            }

            return { ...keyData, key: decryptedKey };
        } catch (error) {
            console.error(`Failed to decrypt active API key "${keyData.name}":`, error);
            const userFriendlyMessage = error instanceof Error ? getApiKeyErrorMessage(error, keyData.name) : `Unknown error with API key "${keyData.name}"`;
            throw new Error(userFriendlyMessage);
        }
    },

    addApiKey: async (name: string, key: string, notes?: string): Promise<ApiKey> => {
        // Validate API key name
        const nameValidation = validateName(name);
        if (!nameValidation.isValid) {
            throw new Error(`Invalid API key name: ${nameValidation.error}`);
        }

        // Validate API key format
        if (!validateApiKeyFormat(key)) {
            throw new Error('Invalid YouTube API key format. Keys should start with "AIza" and be 39 characters long.');
        }

        const sanitizedName = nameValidation.sanitized!;

        // Encrypt the API key before storing
        const encryptedKey = encryptApiKey(key);

        // Prepare insert data with quota management fields
        const insertData: {
            name: string;
            key: string;
            quota_limit: number;
            last_reset_at: string;
            usage: number;
            notes?: string;
        } = {
            name: sanitizedName,
            key: encryptedKey,
            quota_limit: QUOTA_CONSTANTS.DEFAULT_DAILY_QUOTA,
            last_reset_at: new Date().toISOString(),
            usage: 0 // Initialize usage to 0
        };

        // Add notes if provided
        if (notes && notes.trim()) {
            insertData.notes = notes.trim();
        }

        const { data, error } = await supabase
            .from('api_keys')
            .insert(insertData)
            .select()
            .single();

        if (error) {
            console.error('Database insertion error:', error);

            if (error.code === '23505') { // unique_violation on 'key'
                throw new Error('This API key already exists.');
            }

            if (error.code === '23514') { // check_violation
                if (error.message.includes('api_keys_valid_format')) {
                    throw new Error('API key format validation failed. Please ensure you\'re using a valid YouTube API key.');
                }
                if (error.message.includes('api_keys_name_valid')) {
                    throw new Error('Invalid API key name. Please use a name between 1-100 characters.');
                }
                throw new Error(`Database validation failed: ${error.message}`);
            }

            throw new Error(`Failed to add API key: ${error.message}`);
        }

        // Return with decrypted key for immediate use
        return { ...data, key };
    },

    getApiKeyById: async (id: string): Promise<ApiKey> => {
        const { data, error } = await supabase
            .from('api_keys')
            .select('*')
            .eq('id', id)
            .single();

        if (error) throw new Error(error.message);

        try {
            const decryptedKey = decryptApiKey(data.key);
            return { ...data, key: decryptedKey };
        } catch (error) {
            console.error(`Failed to decrypt API key "${data.name}":`, error);
            return { ...data, key: '[ENCRYPTED - DECRYPTION FAILED]' };
        }
    },

    deleteApiKey: async (id: string): Promise<{ success: true }> => {
        const { error } = await supabase.from('api_keys').delete().eq('id', id);
        if (error) throw new Error(error.message);
        return { success: true };
    },

    renameApiKey: async (id: string, newName: string): Promise<ApiKey> => {
        // Validate new API key name
        const nameValidation = validateName(newName);
        if (!nameValidation.isValid) {
            throw new Error(`Invalid API key name: ${nameValidation.error}`);
        }

        const sanitizedName = nameValidation.sanitized!;

        const { data, error } = await supabase
            .from('api_keys')
            .update({ name: sanitizedName })
            .eq('id', id)
            .select()
            .single();
        if (error) throw new Error(error.message);

        // Return with decrypted key
        try {
            const decryptedKey = decryptApiKey(data.key);
            return { ...data, key: decryptedKey };
        } catch (error) {
            console.error('Failed to decrypt API key after rename:', error);
            return data; // Return encrypted version if decryption fails
        }
    },

    updateApiKeyNotes: async (id: string, notes?: string): Promise<ApiKey> => {
        const { data, error } = await supabase
            .from('api_keys')
            .update({ notes: notes?.trim() || null })
            .eq('id', id)
            .select()
            .single();
        if (error) throw new Error(error.message);

        // Return with decrypted key
        try {
            const decryptedKey = decryptApiKey(data.key);
            return { ...data, key: decryptedKey };
        } catch (error) {
            console.error('Failed to decrypt API key after notes update:', error);
            return data; // Return encrypted version if decryption fails
        }
    },

    // Note: Updating usage would typically be a backend operation
    // to avoid letting clients update their own usage counts.
    // For this client-side app, we'll include it here with validation.
    updateUsage: async (id: string, newUsage: number): Promise<ApiKey> => {
        // Get current quota status
        const quotaStatus = await quotaService.getQuotaStatus(id);

        // Validate usage limits
        if (newUsage > quotaStatus.quota_limit) {
            console.warn(`API key usage (${newUsage}) exceeds daily quota (${quotaStatus.quota_limit})`);
        }

        const { data, error } = await supabase
            .from('api_keys')
            .update({ usage: newUsage })
            .eq('id', id)
            .select()
            .single();
        if (error) throw new Error(error.message);

        // Return with decrypted key
        try {
            const decryptedKey = decryptApiKey(data.key);
            return { ...data, key: decryptedKey };
        } catch (error) {
            console.error('Failed to decrypt API key after usage update:', error);
            return data; // Return encrypted version if decryption fails
        }
    },

    // Enhanced method to validate API operation with quota management
    validateOperation: async (operationCost: number): Promise<{ canProceed: boolean; remainingQuota: number; warning?: string }> => {
        try {
            const activeKey = await apiKeyService.getActiveApiKey();
            const validation = await quotaService.validateQuotaUsage(activeKey.id, operationCost);

            let warning: string | undefined;
            if (!validation.canProceed) {
                warning = validation.reason;
            } else if (validation.quotaStatus.usage_percentage > 80) {
                warning = `High quota usage warning: ${validation.quotaStatus.usage_percentage.toFixed(1)}% of daily quota used`;
            }

            return {
                canProceed: validation.canProceed,
                remainingQuota: validation.quotaStatus.remaining_quota,
                warning
            };
        } catch (error) {
            return { canProceed: false, remainingQuota: 0, warning: `Failed to validate API key: ${error}` };
        }
    },

    /**
     * Increment API key usage with quota validation
     */
    incrementUsage: async (id: string, operationCost: number): Promise<{
        success: boolean;
        apiKey: ApiKey;
        quotaExceeded: boolean;
        message?: string;
    }> => {
        try {
            const result = await quotaService.incrementUsage(id, operationCost);

            if (!result.success) {
                return {
                    success: false,
                    apiKey: await apiKeyService.getApiKeyById(id),
                    quotaExceeded: true,
                    message: result.reason
                };
            }

            const updatedApiKey = await apiKeyService.getApiKeyById(id);
            return {
                success: true,
                apiKey: updatedApiKey,
                quotaExceeded: false
            };
        } catch (error) {
            throw new Error(`Failed to increment usage: ${(error as Error).message}`);
        }
    },

    /**
     * Get API key with quota status
     */
    getApiKeyWithQuotaStatus: async (id: string): Promise<ApiKey & {
        quotaStatus: {
            remaining_quota: number;
            usage_percentage: number;
            next_reset_at: string;
            needs_reset: boolean;
        }
    }> => {
        const [apiKey, quotaStatus] = await Promise.all([
            apiKeyService.getApiKeyById(id),
            quotaService.getQuotaStatus(id)
        ]);

        return {
            ...apiKey,
            quotaStatus: {
                remaining_quota: quotaStatus.remaining_quota,
                usage_percentage: quotaStatus.usage_percentage,
                next_reset_at: quotaStatus.next_reset_at,
                needs_reset: quotaStatus.needs_reset
            }
        };
    }
};