
import React, { useState, useCallback, useMemo } from 'react';
import type { SearchResultChannel, Channel } from '../types';
import { youtubeService } from '../services/youtubeService';
import { BackArrowIcon, SearchIcon, CloseIcon, PlusIcon } from './Icons';
import { formatNumber } from '../lib/utils';
import { YouTubeApiDiagnostics } from './YouTubeApiDiagnostics';

interface CreateChannelListPageProps {
    channels: Channel[];
    onBack: () => void;
    onCreate: (listName: string, channels: SearchResultChannel[]) => Promise<void>;
    onNavigateToSettings: () => void;
    pageTitle?: string;
    submitText?: string;
    hideNameInput?: boolean;
}

export const CreateChannelListPage: React.FC<CreateChannelListPageProps> = ({ 
    channels, 
    onBack, 
    onCreate, 
    onNavigateToSettings,
    pageTitle,
    submitText,
    hideNameInput = false
}) => {
    const [listName, setListName] = useState('');
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState<SearchResultChannel[]>([]);
    const [addedChannels, setAddedChannels] = useState<SearchResultChannel[]>([]);
    
    const [isSearching, setIsSearching] = useState(false);
    const [isCreating, setIsCreating] = useState(false);
    const [searchError, setSearchError] = useState<string | null>(null);
    const [showDiagnostics, setShowDiagnostics] = useState(false);

    const isApiKeyError = searchError?.includes('API key') || searchError?.includes('No YouTube API key');
    const existingYoutubeIds = useMemo(() => new Set(channels.flatMap(c => c.youtubeId?.split(',') || [])), [channels]);

    const handleSearch = useCallback(async (query: string) => {
        if (!query.trim()) {
            setSearchResults([]);
            setSearchError(null);
            return;
        }
        setIsSearching(true);
        setSearchError(null);
        try {
            const results = await youtubeService.searchOrFetchChannelInfo(query);
            setSearchResults(results);
            if(results.length === 0) {
                setSearchError("No channels found for your query.");
            }
        } catch (error) {
            console.error("Search failed:", error);
            setSearchError((error as Error).message);
            setSearchResults([]);
        } finally {
            setIsSearching(false);
        }
    }, []);

    const handleAddChannel = (channel: SearchResultChannel) => {
        if (addedChannels.find(c => c.youtubeId === channel.youtubeId) || existingYoutubeIds.has(channel.youtubeId)) {
            return; 
        }
        setAddedChannels(prev => [...prev, channel]);
    };

    const handleRemoveChannel = (youtubeId: string) => {
        setAddedChannels(prev => prev.filter(c => c.youtubeId !== youtubeId));
    };

    const handleSubmit = async () => {
        if (!canSubmit || isCreating) return;
        
        setIsCreating(true);
        try {
            await onCreate(listName, addedChannels);
            // On success, the parent component will unmount this page.
        } catch (error) {
            // Error is handled by parent, just stop loading state here.
            setIsCreating(false);
        }
    };

    const canSubmit = hideNameInput
        ? addedChannels.length > 0 && !isCreating
        : listName.trim().length > 0 && addedChannels.length > 0 && !isCreating;
        
    const buttonLabel = submitText ? `${submitText} (${addedChannels.length})` : `Create (${addedChannels.length})`;

    const filteredSearchResults = searchResults.filter(
        result => !addedChannels.some(added => added.youtubeId === result.youtubeId) && !existingYoutubeIds.has(result.youtubeId)
    );

    return (
        <>
        <div className="flex-1 p-4 sm:p-6 lg:p-8 flex flex-col bg-[#1a1a1a]">
            <header className="flex items-center space-x-4 mb-8">
                <button onClick={onBack} className="p-2 rounded-full hover:bg-dark-card transition-colors" aria-label="Back to channel lists">
                    <BackArrowIcon className="w-6 h-6" />
                </button>
                <h2 className="text-2xl font-bold text-white">{pageTitle || 'Create a new channel list'}</h2>
            </header>

            <div className="w-full max-w-2xl mx-auto flex-1 flex flex-col">
                <div className="space-y-8">
                    {!hideNameInput && (
                        <div>
                            <label htmlFor="list-name" className="block text-lg font-semibold text-gray-200 mb-3">List Name</label>
                            <input
                                id="list-name"
                                type="text"
                                value={listName}
                                onChange={e => setListName(e.target.value)}
                                placeholder="e.g., 'My Favorite Tech Channels'"
                                className="w-full bg-[#0d0d0d] border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent"
                            />
                        </div>
                    )}


                    <div>
                        <label htmlFor="add-channels" className="block text-lg font-semibold text-gray-200 mb-3">Add channels to your list</label>
                        <div className={`bg-[#0d0d0d] border rounded-lg overflow-hidden transition-all duration-300 ${filteredSearchResults.length > 0 && !isSearching ? 'border-accent shadow-lg shadow-accent/10' : 'border-gray-700'}`}>
                            <div className="relative">
                                <SearchIcon className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                                <input
                                    id="add-channels"
                                    type="text"
                                    value={searchQuery}
                                    onChange={(e) => {
                                        setSearchQuery(e.target.value);
                                        handleSearch(e.target.value);
                                    }}
                                    disabled={isApiKeyError}
                                    placeholder="Search for a channel or paste a channel/video link"
                                    className="w-full bg-transparent pl-11 pr-4 py-3 text-white placeholder-gray-500 focus:outline-none disabled:bg-gray-800/50 disabled:cursor-not-allowed"
                                />
                            </div>
                            
                            {isSearching && <p className="text-gray-400 px-4 pt-1 pb-3 text-sm animate-pulse">Searching...</p>}
                            
                            {searchError && !isSearching && (
                                <div className="p-4 border-t border-dark-border">
                                    <div className="bg-red-900/20 border border-red-700 rounded-lg p-3 mb-3">
                                        <p className="text-red-400 text-sm font-medium mb-1">Channel Search Error</p>
                                        <p className="text-red-300 text-sm">{searchError}</p>
                                    </div>
                                    <div className="flex gap-2 flex-wrap">
                                        <button
                                            onClick={() => setShowDiagnostics(true)}
                                            className="text-sm bg-blue-600 text-white px-3 py-1.5 rounded-md hover:bg-blue-700 transition-colors"
                                        >
                                            Run Diagnostics
                                        </button>
                                        {isApiKeyError && (
                                            <button
                                                onClick={onNavigateToSettings}
                                                className="text-sm bg-accent text-darkbg font-semibold px-3 py-1.5 rounded-md hover:bg-accent-hover transition-colors"
                                            >
                                                Go to Settings
                                            </button>
                                        )}
                                        <button
                                            onClick={() => {
                                                setSearchError(null);
                                                if (searchQuery.trim()) {
                                                    handleSearch(searchQuery);
                                                }
                                            }}
                                            className="text-sm bg-gray-600 text-white px-3 py-1.5 rounded-md hover:bg-gray-700 transition-colors"
                                        >
                                            Retry
                                        </button>
                                    </div>
                                </div>
                            )}
                            
                            {filteredSearchResults.length > 0 && !isSearching && (
                                <div className="border-t border-dark-border max-h-60 overflow-y-auto">
                                    {filteredSearchResults.map(channel => (
                                        <div key={channel.youtubeId} className="flex items-center justify-between p-3 hover:bg-accent/5">
                                            <div className="flex items-center space-x-3 overflow-hidden">
                                                <img src={channel.avatar} alt={channel.name} className="w-11 h-11 rounded-full flex-shrink-0" />
                                                <div className="overflow-hidden">
                                                    <p className="font-semibold text-white truncate">{channel.name}</p>
                                                    <p className="text-sm text-gray-400 truncate">
                                                        {channel.handle}
                                                        {channel.subscriberCount !== undefined && ` · ${formatNumber(channel.subscriberCount)} Subscribers`}
                                                    </p>
                                                </div>
                                            </div>
                                            <button 
                                                onClick={() => handleAddChannel(channel)} 
                                                className="w-9 h-9 flex items-center justify-center rounded-full bg-accent/20 text-accent hover:bg-accent/30 transition-colors flex-shrink-0 ml-2"
                                                aria-label={`Add ${channel.name}`}
                                            >
                                                <PlusIcon className="w-5 h-5" />
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                    
                    <div>
                        <h3 className="text-lg font-semibold text-gray-200 mb-3">Added channels ({addedChannels.length})</h3>
                        <div className="bg-[#0d0d0d] border border-gray-800/80 rounded-lg p-3 min-h-[12rem] flex flex-col">
                            {addedChannels.length > 0 ? (
                                <div className="space-y-2 overflow-y-auto">
                                    {addedChannels.map(channel => (
                                         <div key={channel.youtubeId} className="w-full flex items-center justify-between bg-dark-card/50 p-2 rounded-md">
                                            <div className="flex items-center space-x-3 overflow-hidden">
                                                <img src={channel.avatar} alt={channel.name} className="w-10 h-10 rounded-full flex-shrink-0" />
                                                <div className="overflow-hidden">
                                                    <p className="font-semibold text-white truncate">{channel.name}</p>
                                                    <p className="text-sm text-gray-400 truncate">
                                                        {channel.handle}
                                                        {channel.subscriberCount !== undefined && ` · ${formatNumber(channel.subscriberCount)}`}
                                                    </p>
                                                </div>
                                            </div>
                                            <button onClick={() => handleRemoveChannel(channel.youtubeId)} className="p-1 rounded-full text-gray-400 hover:text-white hover:bg-gray-700 transition-colors ml-2 flex-shrink-0" aria-label={`Remove ${channel.name}`}>
                                                <CloseIcon className="w-5 h-5" />
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="flex-1 flex items-center justify-center">
                                    <p className="text-gray-500 text-center">Your added channels will appear here.</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                <div className="flex-1"></div>
                
                <footer className="flex justify-end py-4">
                    <button 
                        onClick={handleSubmit}
                        disabled={!canSubmit}
                        className="px-8 py-3 rounded-lg bg-accent text-darkbg font-bold hover:bg-accent-hover disabled:bg-gray-600 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center w-40"
                    >
                         {isCreating ? <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-darkbg"></div> : buttonLabel}
                    </button>
                </footer>
            </div>
        </div>

        {/* YouTube API Diagnostics Modal */}
        {showDiagnostics && (
            <YouTubeApiDiagnostics
                onClose={() => setShowDiagnostics(false)}
                onNavigateToSettings={() => {
                    setShowDiagnostics(false);
                    onNavigateToSettings();
                }}
            />
        )}
    );
};
