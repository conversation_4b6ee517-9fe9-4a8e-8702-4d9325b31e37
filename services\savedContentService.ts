

import type { Folder, Tag, SavedVideoPostData, SavedVideo, SavedVideoWithTags } from '../types';
import { supabase } from '../lib/supabase';
import { validateName, validateNotes } from '../lib/inputValidation';

export const savedContentService = {
  // --- FOLDERS ---
  getFolders: async (): Promise<Folder[]> => {
    const { data, error } = await supabase.from('folders').select('*').order('created_at');
    if (error) throw new Error(error.message);
    return data;
  },
  addFolder: async (name: string): Promise<Folder> => {
    // Validate folder name
    const nameValidation = validateName(name);
    if (!nameValidation.isValid) {
      throw new Error(`Invalid folder name: ${nameValidation.error}`);
    }

    const sanitizedName = nameValidation.sanitized!;

    const { data, error } = await supabase.from('folders').insert({ name: sanitizedName }).select().single();
    if (error) {
        if (error.code === '23505') {
            throw new Error(`A folder named "${sanitizedName}" already exists.`);
        }
        throw new Error(error.message);
    }
    return data;
  },
  renameFolder: async(id: string, newName: string): Promise<Folder> => {
    // Validate new folder name
    const nameValidation = validateName(newName);
    if (!nameValidation.isValid) {
      throw new Error(`Invalid folder name: ${nameValidation.error}`);
    }

    const sanitizedName = nameValidation.sanitized!;

    const { data, error } = await supabase.from('folders').update({ name: sanitizedName }).eq('id', id).select().single();
    if (error) {
        if (error.code === '23505') {
             throw new Error(`A folder named "${sanitizedName}" already exists.`);
        }
        throw new Error(error.message);
    }
    return data;
  },
  deleteFolder: async(id: string): Promise<{ success: true }> => {
    const { error } = await supabase.from('folders').delete().eq('id', id);
    if (error) throw new Error(error.message);
    return { success: true };
  },

  // --- TAGS ---
  getTags: async(): Promise<Tag[]> => {
    const { data, error } = await supabase.from('tags').select('*').order('created_at');
    if (error) throw new Error(error.message);
    return data;
  },
  addTag: async(name: string): Promise<Tag> => {
    // Validate tag name
    const nameValidation = validateName(name);
    if (!nameValidation.isValid) {
      throw new Error(`Invalid tag name: ${nameValidation.error}`);
    }

    const sanitizedName = nameValidation.sanitized!;

    const { data, error } = await supabase.from('tags').insert({ name: sanitizedName }).select().single();
    if (error) {
        if (error.code === '23505') {
             throw new Error(`A tag named "${sanitizedName}" already exists.`);
        }
        throw new Error(error.message);
    }
    return data;
  },
  renameTag: async(id: string, newName: string): Promise<Tag> => {
    // Validate new tag name
    const nameValidation = validateName(newName);
    if (!nameValidation.isValid) {
      throw new Error(`Invalid tag name: ${nameValidation.error}`);
    }

    const sanitizedName = nameValidation.sanitized!;

    const { data, error } = await supabase.from('tags').update({ name: sanitizedName }).eq('id', id).select().single();
    if (error) {
        if (error.code === '23505') {
            throw new Error(`A tag named "${sanitizedName}" already exists.`);
        }
        throw new Error(error.message);
    }
    return data;
  },
  deleteTag: async(id: string): Promise<{ success: true }> => {
    const { error } = await supabase.from('tags').delete().eq('id', id);
    if (error) throw new Error(error.message);
    return { success: true };
  },

  // --- SAVED VIDEOS ---
  getAllSavedVideoCounts: async (): Promise<Map<string, number>> => {
      const { data, error } = await supabase
          .from('saved_videos')
          .select('folder_id');
      
      if (error) {
          console.error("Error fetching video counts:", error);
          return new Map();
      }

      const counts = new Map<string, number>();
      for (const item of data) {
          if (item.folder_id) {
              counts.set(item.folder_id, (counts.get(item.folder_id) || 0) + 1);
          }
      }
      return counts;
  },

  getVideosByFolderId: async (folderId: string): Promise<SavedVideoWithTags[]> => {
    const { data: videos, error } = await supabase
        .from('saved_videos')
        .select('*')
        .eq('folder_id', folderId)
        .order('created_at', { ascending: false });

    if (error) throw new Error(error.message);

    const videoIds = videos.map(v => v.id);
    if(videoIds.length === 0) return [];
    
    const { data: tagLinks, error: tagLinkError } = await supabase
        .from('saved_video_tags')
        .select('saved_video_id, tags(id, name)')
        .in('saved_video_id', videoIds);
        
    if(tagLinkError) throw new Error(tagLinkError.message);

    const tagsByVideoId = new Map<string, Tag[]>();
    tagLinks.forEach((link: any) => {
        if (!tagsByVideoId.has(link.saved_video_id)) {
            tagsByVideoId.set(link.saved_video_id, []);
        }
        tagsByVideoId.get(link.saved_video_id)!.push(link.tags as Tag);
    });

    return videos.map(video => ({
        ...video,
        tags: tagsByVideoId.get(video.id) || []
    }));
  },

  saveVideo: async (videoData: SavedVideoPostData, tagIds: string[]): Promise<SavedVideo> => {
    // Validate notes if present
    if (videoData.notes) {
      const notesValidation = validateNotes(videoData.notes);
      if (!notesValidation.isValid) {
        throw new Error(`Invalid notes: ${notesValidation.error}`);
      }
      videoData.notes = notesValidation.sanitized;
    }

    // 1. Insert the video
    const { data: savedVideo, error: saveVideoError } = await supabase
        .from('saved_videos')
        .insert(videoData)
        .select()
        .single();
    
    if (saveVideoError) throw new Error(saveVideoError.message);
    if (!savedVideo) throw new Error('Failed to save video, no data returned.');
    
    // 2. If tags are provided, link them
    if (tagIds.length > 0) {
        const tagLinks = tagIds.map(tag_id => ({
            saved_video_id: savedVideo.id,
            tag_id: tag_id,
        }));
        
        const { error: saveTagsError } = await supabase
            .from('saved_video_tags')
            .insert(tagLinks);
            
        if (saveTagsError) {
            // In a real app, you might want to "roll back" the video insert.
            // For now, we'll just log the error and return the video.
            console.error("Failed to link tags to video:", saveTagsError.message);
        }
    }

    return savedVideo;
  },

  deleteSavedVideo: async(id: string): Promise<{ success: true }> => {
      // The database is set up with ON DELETE CASCADE for saved_video_tags,
      // so we only need to delete from the main table.
      const { error } = await supabase.from('saved_videos').delete().eq('id', id);
      if (error) throw new Error(error.message);
      return { success: true };
  },

  updateSavedVideoNotes: async(id: string, notes: string): Promise<SavedVideo> => {
      // Validate notes
      const notesValidation = validateNotes(notes);
      if (!notesValidation.isValid) {
        throw new Error(`Invalid notes: ${notesValidation.error}`);
      }

      const sanitizedNotes = notesValidation.sanitized!;

      const { data, error } = await supabase
          .from('saved_videos')
          .update({ notes: sanitizedNotes })
          .eq('id', id)
          .select()
          .single();

      if (error) throw new Error(error.message);
      return data;
  }
};