import React from 'react';
import { 
  PlusIcon, 
  EllipsisVerticalIcon, 
  StarIcon, 
  PlayIcon, 
  EyeIcon, 
  StarIconSolid,
  EyeOffIcon,
  CheckIcon,
  CloseIcon,
  TrashIcon
} from './Icons';

/**
 * Test component to verify all icons are working correctly
 * This component displays all the icons used in TrackedChannelsPage
 */
const IconsTest: React.FC = () => {
  return (
    <div className="min-h-screen bg-[#0f0f0f] p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Icons Test - VirSnapp</h1>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          
          {/* TrackedChannelsPage Icons */}
          <div className="bg-[#1a1a1a] p-6 rounded-lg border border-[#333]">
            <h3 className="text-white font-semibold mb-4">TrackedChannelsPage Icons</h3>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <PlusIcon className="w-6 h-6 text-[#10b981]" />
                <span className="text-gray-300 text-sm">PlusIcon</span>
              </div>
              <div className="flex items-center gap-3">
                <EllipsisVerticalIcon className="w-6 h-6 text-gray-400" />
                <span className="text-gray-300 text-sm">EllipsisVerticalIcon</span>
              </div>
              <div className="flex items-center gap-3">
                <StarIcon className="w-6 h-6 text-gray-400" />
                <span className="text-gray-300 text-sm">StarIcon (outline)</span>
              </div>
              <div className="flex items-center gap-3">
                <StarIconSolid className="w-6 h-6 text-[#10b981]" />
                <span className="text-gray-300 text-sm">StarIconSolid</span>
              </div>
              <div className="flex items-center gap-3">
                <PlayIcon className="w-6 h-6 text-white" />
                <span className="text-gray-300 text-sm">PlayIcon</span>
              </div>
              <div className="flex items-center gap-3">
                <EyeIcon className="w-6 h-6 text-gray-400" />
                <span className="text-gray-300 text-sm">EyeIcon</span>
              </div>
            </div>
          </div>

          {/* Common UI Icons */}
          <div className="bg-[#1a1a1a] p-6 rounded-lg border border-[#333]">
            <h3 className="text-white font-semibold mb-4">Common UI Icons</h3>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <EyeOffIcon className="w-6 h-6 text-gray-400" />
                <span className="text-gray-300 text-sm">EyeOffIcon</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckIcon className="w-6 h-6 text-green-400" />
                <span className="text-gray-300 text-sm">CheckIcon</span>
              </div>
              <div className="flex items-center gap-3">
                <CloseIcon className="w-6 h-6 text-red-400" />
                <span className="text-gray-300 text-sm">CloseIcon</span>
              </div>
              <div className="flex items-center gap-3">
                <TrashIcon className="w-6 h-6 text-red-400" />
                <span className="text-gray-300 text-sm">TrashIcon</span>
              </div>
            </div>
          </div>

          {/* Interactive Examples */}
          <div className="bg-[#1a1a1a] p-6 rounded-lg border border-[#333]">
            <h3 className="text-white font-semibold mb-4">Interactive Examples</h3>
            <div className="space-y-4">
              <button className="flex items-center gap-2 px-3 py-2 bg-[#10b981] text-white rounded-lg hover:bg-[#059669] transition-colors">
                <PlusIcon className="w-4 h-4" />
                Create List
              </button>
              <button className="flex items-center gap-2 px-3 py-2 bg-gray-600 text-white rounded hover:bg-gray-500 transition-colors">
                <EyeIcon className="w-4 h-4" />
                View List
              </button>
              <button className="flex items-center gap-2 px-3 py-2 bg-red-600 text-white rounded hover:bg-red-500 transition-colors">
                <TrashIcon className="w-4 h-4" />
                Delete
              </button>
            </div>
          </div>

          {/* Size Variations */}
          <div className="bg-[#1a1a1a] p-6 rounded-lg border border-[#333]">
            <h3 className="text-white font-semibold mb-4">Size Variations</h3>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <PlusIcon className="w-4 h-4 text-[#10b981]" />
                <span className="text-gray-300 text-sm">Small (16px)</span>
              </div>
              <div className="flex items-center gap-3">
                <PlusIcon className="w-6 h-6 text-[#10b981]" />
                <span className="text-gray-300 text-sm">Medium (24px)</span>
              </div>
              <div className="flex items-center gap-3">
                <PlusIcon className="w-8 h-8 text-[#10b981]" />
                <span className="text-gray-300 text-sm">Large (32px)</span>
              </div>
              <div className="flex items-center gap-3">
                <PlusIcon className="w-12 h-12 text-[#10b981]" />
                <span className="text-gray-300 text-sm">Extra Large (48px)</span>
              </div>
            </div>
          </div>

        </div>

        {/* Test Results */}
        <div className="mt-8 bg-[#1a1a1a] p-6 rounded-lg border border-[#333]">
          <h3 className="text-white font-semibold mb-4">✅ Test Results</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <CheckIcon className="w-4 h-4 text-green-400" />
              <span className="text-gray-300">All TrackedChannelsPage icons are working correctly</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckIcon className="w-4 h-4 text-green-400" />
              <span className="text-gray-300">Icons are properly imported from react-icons/ri</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckIcon className="w-4 h-4 text-green-400" />
              <span className="text-gray-300">No Heroicons dependency required</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckIcon className="w-4 h-4 text-green-400" />
              <span className="text-gray-300">All icon sizes and colors render correctly</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckIcon className="w-4 h-4 text-green-400" />
              <span className="text-gray-300">Interactive buttons with icons work properly</span>
            </div>
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="mt-8 bg-[#1a1a1a] p-6 rounded-lg border border-[#333]">
          <h3 className="text-white font-semibold mb-4">📖 Usage Instructions</h3>
          <div className="text-gray-300 text-sm space-y-2">
            <p>• Import icons from the local Icons component: <code className="bg-gray-800 px-2 py-1 rounded">import &#123; PlusIcon &#125; from './Icons';</code></p>
            <p>• All icons accept a <code className="bg-gray-800 px-2 py-1 rounded">className</code> prop for styling</p>
            <p>• Use Tailwind classes for sizing: <code className="bg-gray-800 px-2 py-1 rounded">w-4 h-4</code>, <code className="bg-gray-800 px-2 py-1 rounded">w-6 h-6</code>, etc.</p>
            <p>• Apply colors with Tailwind: <code className="bg-gray-800 px-2 py-1 rounded">text-[#10b981]</code>, <code className="bg-gray-800 px-2 py-1 rounded">text-gray-400</code>, etc.</p>
            <p>• Icons are based on react-icons/ri (Remix Icons) for consistency</p>
          </div>
        </div>

      </div>
    </div>
  );
};

export default IconsTest;
