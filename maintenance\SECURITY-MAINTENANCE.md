# 🔄 VirSnapp Security Maintenance Guide

This guide outlines ongoing security maintenance procedures to keep your VirSnapp deployment secure over time.

## 📅 Maintenance Schedule

### Daily Tasks (Automated)
- **Security Event Monitoring**: Automatic logging and alerting
- **API Usage Tracking**: Monitor quota consumption patterns
- **Health Checks**: Automated application health monitoring

### Weekly Tasks (5-10 minutes)
- **Security Log Review**: Check for unusual activity patterns
- **API Key Usage Analysis**: Review quota consumption trends
- **Performance Monitoring**: Check application performance metrics
- **Dependency Updates**: Review available security updates

### Monthly Tasks (30-45 minutes)
- **Comprehensive Security Audit**: Full security assessment
- **API Key Rotation**: Rotate YouTube API keys (recommended)
- **Database Maintenance**: Review and optimize database performance
- **Backup Verification**: Ensure backups are working correctly

### Quarterly Tasks (1-2 hours)
- **Security Policy Review**: Update security policies as needed
- **Penetration Testing**: Conduct security testing
- **Documentation Updates**: Keep security documentation current
- **Incident Response Testing**: Test emergency procedures

## 🔍 Daily Monitoring Procedures

### Security Event Monitoring

#### Automated Monitoring
The application automatically logs security events. Monitor these in your browser console or integrate with a logging service.

#### Key Events to Watch
- **Failed Authentication Attempts**: Unusual login patterns
- **API Quota Exceeded**: Potential abuse or misconfiguration
- **Invalid Input Attempts**: Possible attack attempts
- **Unusual Usage Patterns**: Spikes in activity or errors

#### Setting Up Alerts
For production environments, integrate with monitoring services:

```javascript
// Example: Integrate with external logging service
import { logSecurityEvent } from './lib/securityMonitoring.js';

// Override the logging function to send to external service
const originalLogSecurityEvent = logSecurityEvent;
logSecurityEvent = async (event) => {
  // Log locally
  await originalLogSecurityEvent(event);
  
  // Send to external service (e.g., Sentry, LogRocket)
  if (event.severity === 'critical' || event.severity === 'high') {
    await fetch('/api/security-alert', {
      method: 'POST',
      body: JSON.stringify(event)
    });
  }
};
```

## 🔑 API Key Management

### Weekly API Key Review

#### Check Usage Patterns
```javascript
// Run in browser console weekly
import { apiKeyService } from './services/apiKeyService.js';

const keys = await apiKeyService.getApiKeys();
keys.forEach(key => {
  console.log(`Key: ${key.name}, Usage: ${key.usage}/10000 (${(key.usage/100).toFixed(1)}%)`);
  
  if (key.usage > 8000) {
    console.warn(`⚠️ Key "${key.name}" approaching quota limit`);
  }
});
```

#### Monitor for Anomalies
- **Sudden Usage Spikes**: May indicate abuse or misconfiguration
- **Consistent High Usage**: May need additional API keys or optimization
- **Zero Usage**: May indicate broken functionality

### Monthly API Key Rotation

#### Why Rotate API Keys?
- **Security Best Practice**: Limits exposure if keys are compromised
- **Compliance**: Many security frameworks require regular rotation
- **Fresh Quotas**: New keys come with fresh daily quotas

#### Rotation Procedure
1. **Generate New Key**: Create new YouTube API key in Google Cloud Console
2. **Add to Application**: Add new key in Settings page
3. **Test Functionality**: Verify new key works correctly
4. **Update Active Key**: Switch to new key as active
5. **Monitor Usage**: Ensure new key is being used
6. **Remove Old Key**: Delete old key after 24-48 hours
7. **Update Documentation**: Record rotation in maintenance log

#### Rotation Script Template
```bash
#!/bin/bash
# API Key Rotation Checklist

echo "🔑 API Key Rotation Procedure"
echo "1. [ ] Generate new YouTube API key in Google Cloud Console"
echo "2. [ ] Add new key in VirSnapp Settings page"
echo "3. [ ] Test channel search functionality"
echo "4. [ ] Test video saving functionality"
echo "5. [ ] Switch to new key as active"
echo "6. [ ] Monitor usage for 24 hours"
echo "7. [ ] Remove old key from application"
echo "8. [ ] Remove old key from Google Cloud Console"
echo "9. [ ] Update maintenance log"

echo ""
echo "Date: $(date)"
echo "Old Key Name: ________________"
echo "New Key Name: ________________"
echo "Rotated By: ________________"
```

## 🗄️ Database Security Maintenance

### Weekly Database Review

#### Check RLS Policies
```sql
-- Verify RLS is still enabled on all tables
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';

-- Should show rowsecurity = true for all application tables
```

#### Monitor Database Activity
```sql
-- Check for unusual activity patterns
SELECT 
  table_name,
  COUNT(*) as operation_count,
  MAX(created_at) as last_activity
FROM (
  SELECT 'channels' as table_name, created_at FROM channels
  UNION ALL
  SELECT 'saved_videos' as table_name, created_at FROM saved_videos
  UNION ALL
  SELECT 'api_keys' as table_name, created_at FROM api_keys
) activity
GROUP BY table_name
ORDER BY operation_count DESC;
```

#### Review Data Integrity
```sql
-- Check for constraint violations or data anomalies
SELECT 
  'channels' as table_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN LENGTH(name) > 100 THEN 1 END) as name_too_long,
  COUNT(CASE WHEN name IS NULL THEN 1 END) as missing_names
FROM channels

UNION ALL

SELECT 
  'api_keys' as table_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN NOT validate_youtube_api_key(key) THEN 1 END) as invalid_keys,
  COUNT(CASE WHEN usage < 0 THEN 1 END) as negative_usage
FROM api_keys;
```

### Monthly Database Optimization

#### Update Statistics
```sql
-- Update table statistics for better query performance
ANALYZE channels;
ANALYZE saved_videos;
ANALYZE api_keys;
ANALYZE folders;
ANALYZE tags;
```

#### Check Index Usage
```sql
-- Monitor index usage
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_scan,
  idx_tup_read,
  idx_tup_fetch
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;
```

## 📊 Performance Monitoring

### Weekly Performance Review

#### Application Performance Metrics
Monitor these key metrics:
- **Page Load Time**: Should be < 3 seconds
- **API Response Time**: Should be < 5 seconds
- **Database Query Time**: Should be < 1 second
- **Memory Usage**: Should remain stable over time

#### YouTube API Performance
```javascript
// Monitor API performance weekly
import { API_USAGE_LIMITS } from './lib/apiKeySecurity.js';

// Calculate efficiency metrics
const calculateApiEfficiency = (operations, quotaUsed) => {
  const efficiency = operations / quotaUsed;
  console.log(`API Efficiency: ${efficiency.toFixed(2)} operations per quota unit`);
  
  if (efficiency < 0.1) {
    console.warn('⚠️ Low API efficiency - consider optimization');
  }
};
```

### Monthly Performance Optimization

#### Database Query Optimization
- **Review Slow Queries**: Check Supabase dashboard for slow queries
- **Optimize Indexes**: Add indexes for frequently queried columns
- **Clean Up Data**: Remove old or unnecessary data

#### Application Optimization
- **Bundle Size**: Monitor and optimize JavaScript bundle size
- **Caching**: Implement caching for frequently accessed data
- **Code Splitting**: Split code to improve initial load times

## 🚨 Incident Response Procedures

### Security Incident Classification

#### Severity Levels
- **Critical**: Data breach, system compromise, complete service outage
- **High**: Security vulnerability exploitation, partial service outage
- **Medium**: Suspicious activity, performance degradation
- **Low**: Minor security warnings, cosmetic issues

### Incident Response Steps

#### Immediate Response (0-1 hour)
1. **Assess Severity**: Determine incident classification
2. **Contain Threat**: Isolate affected systems if necessary
3. **Document Incident**: Record all details and actions taken
4. **Notify Stakeholders**: Alert relevant team members

#### Investigation (1-24 hours)
1. **Gather Evidence**: Collect logs, screenshots, and data
2. **Analyze Impact**: Determine scope and extent of incident
3. **Identify Root Cause**: Find the underlying cause
4. **Develop Fix**: Create plan to resolve the issue

#### Resolution (24-72 hours)
1. **Implement Fix**: Apply necessary changes
2. **Test Solution**: Verify fix resolves the issue
3. **Monitor System**: Watch for recurring issues
4. **Update Documentation**: Record lessons learned

### Emergency Contacts

```
Security Team Lead: ________________
Database Administrator: ________________
System Administrator: ________________
Management Contact: ________________

Supabase Support: https://supabase.com/support
YouTube API Support: https://developers.google.com/youtube/v3/support
```

## 📋 Maintenance Checklists

### Weekly Checklist
```
Week of: ___________
Completed by: ___________

Security Monitoring:
[ ] Reviewed security event logs
[ ] Checked API usage patterns
[ ] Verified no critical alerts
[ ] Monitored application performance

Database Review:
[ ] Verified RLS policies active
[ ] Checked for data anomalies
[ ] Reviewed query performance
[ ] Confirmed backup status

Performance Check:
[ ] Measured page load times
[ ] Checked API response times
[ ] Monitored memory usage
[ ] Reviewed error rates

Issues Found: ___________
Actions Taken: ___________
```

### Monthly Checklist
```
Month of: ___________
Completed by: ___________

Security Audit:
[ ] Ran comprehensive security tests
[ ] Reviewed and updated security policies
[ ] Checked for security updates
[ ] Tested incident response procedures

API Key Management:
[ ] Rotated YouTube API keys
[ ] Updated key documentation
[ ] Verified quota allocations
[ ] Cleaned up unused keys

Database Maintenance:
[ ] Updated table statistics
[ ] Optimized slow queries
[ ] Cleaned up old data
[ ] Verified backup integrity

Performance Optimization:
[ ] Analyzed performance metrics
[ ] Optimized slow operations
[ ] Updated dependencies
[ ] Tested under load

Documentation:
[ ] Updated security documentation
[ ] Reviewed maintenance procedures
[ ] Updated emergency contacts
[ ] Recorded lessons learned

Issues Found: ___________
Actions Taken: ___________
Next Month Priorities: ___________
```

## 📞 Support and Resources

### Internal Resources
- **Security Documentation**: `SECURITY.md`
- **Deployment Guide**: `deployment/PRODUCTION-SETUP.md`
- **Testing Procedures**: `testing/MANUAL-TESTING-CHECKLIST.md`

### External Resources
- **Supabase Security**: https://supabase.com/docs/guides/auth/row-level-security
- **YouTube API Best Practices**: https://developers.google.com/youtube/v3/guides/implementation
- **OWASP Security Guidelines**: https://owasp.org/www-project-top-ten/

### Emergency Procedures
For security emergencies, follow procedures in `SECURITY.md` under "Emergency Procedures" section.
