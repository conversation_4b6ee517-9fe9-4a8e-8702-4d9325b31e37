import React, { useMemo } from 'react';
import type { Channel } from '../types';
import { ChannelCard } from './ChannelCard';
import { PlusIcon } from './Icons';

interface TrackedChannelsPageProps {
    channels: Channel[];
    onSelectChannel: (id: string) => void;
    onAddChannelClick: () => void;
    onDeleteChannel: (id: string) => Promise<void>;
    onRenameChannel: (id: string, newName: string) => Promise<void>;
    onDuplicateChannel: (id: string) => Promise<void>;
    onPinChannel: (id: string, isPinned: boolean) => Promise<void>;
}

const CreateChannelButton: React.FC<{ onClick: () => void }> = ({ onClick }) => (
    <button
        onClick={onClick}
        className="flex items-center justify-center p-4 h-28 text-accent border-2 border-dashed border-accent/50 rounded-lg transition-all duration-300 hover:border-accent hover:bg-accent/10 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 focus:ring-offset-darkbg"
    >
        <PlusIcon className="w-6 h-6 mr-2" />
        <span className="font-semibold">Create new channel list</span>
    </button>
);

export const TrackedChannelsPage: React.FC<TrackedChannelsPageProps> = ({ 
    channels, 
    onSelectChannel, 
    onAddChannelClick,
    onDeleteChannel,
    onRenameChannel,
    onDuplicateChannel,
    onPinChannel
}) => {
    const sortedChannels = useMemo(() => {
        return [...channels].sort((a, b) => {
            // Pinned items come first, otherwise sort by creation date
            if (a.isPinned && !b.isPinned) return -1;
            if (!a.isPinned && b.isPinned) return 1;
            return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        });
    }, [channels]);
    
    return (
        <div className="p-4 sm:p-6 lg:p-8 space-y-8">
            <header>
                <h2 className="text-3xl font-bold text-white">Tracked channels</h2>
            </header>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                <CreateChannelButton onClick={onAddChannelClick} />
                {sortedChannels.map(channel => (
                    <ChannelCard 
                        key={channel.id} 
                        channel={channel}
                        onSelect={() => onSelectChannel(channel.id)}
                        onDelete={onDeleteChannel}
                        onRename={onRenameChannel}
                        onDuplicate={onDuplicateChannel}
                        onPin={onPinChannel}
                    />
                ))}
            </div>
        </div>
    );
};
