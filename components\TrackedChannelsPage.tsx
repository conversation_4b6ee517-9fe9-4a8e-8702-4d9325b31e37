
import React, { useState, useEffect } from 'react';
import { PlusIcon, EllipsisVerticalIcon, StarIcon, PlayIcon, EyeIcon, StarIconSolid } from './Icons';

interface ChannelPreview {
  id: string;
  name: string;
  avatar: string;
}

interface VideoPreview {
  id: string;
  title: string;
  thumbnail: string;
  viewCount: string;
}

interface ChannelList {
  id: string;
  name: string;
  channelCount: number;
  lastUpdated: string;
  isPinned: boolean;
  channels: ChannelPreview[];
  popularVideo?: VideoPreview;
}

interface TrackedChannelsPageProps {
  channelLists?: ChannelList[];
  onCreateList: () => void;
  onViewList: (listId: string) => void;
  onTogglePin: (listId: string) => void;
  onDeleteList: (listId: string) => void;
}

export const TrackedChannelsPage: React.FC<TrackedChannelsPageProps> = ({
  channelLists = [],
  onCreateList,
  onViewList,
  onTogglePin,
  onDeleteList
}) => {
  const [filter, setFilter] = useState<'all' | 'pinned' | 'recent'>('all');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const filteredLists = channelLists.filter(list => {
    if (filter === 'pinned') return list.isPinned;
    if (filter === 'recent') {
      const daysSinceUpdate = Math.floor((Date.now() - new Date(list.lastUpdated).getTime()) / (1000 * 60 * 60 * 24));
      return daysSinceUpdate <= 7;
    }
    return true;
  });

  const totalChannels = channelLists.reduce((sum, list) => sum + list.channelCount, 0);
  const lastSync = channelLists.length > 0 ?
    Math.min(...channelLists.map(list => new Date(list.lastUpdated).getTime())) : Date.now();

  if (isLoading) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="min-h-screen bg-[#0f0f0f]">
      <div className="w-full px-8 py-8">
        {/* Header Section */}
        <header className="mb-12">
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-[32px] font-bold text-white mb-2">Tracked channels</h1>
              <p className="text-base text-gray-400">
                Create and manage your YouTube channel lists to track popular videos
              </p>
            </div>
            <button
              onClick={onCreateList}
              className="flex items-center gap-2 px-3 py-3 bg-[#10b981] text-white rounded-lg hover:bg-[#059669] transition-colors duration-150"
            >
              <PlusIcon className="w-5 h-5" />
              <span className="font-medium">Create List</span>
            </button>
          </div>
        </header>

        {channelLists.length === 0 ? (
          <EmptyState onCreateList={onCreateList} />
        ) : (
          <>
            {/* Statistics Bar */}
            <div className="mb-8">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-8">
                  <div className="text-sm">
                    <span className="text-gray-400">Total lists: </span>
                    <span className="text-white font-semibold">{channelLists.length}</span>
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-400">Channels tracked: </span>
                    <span className="text-white font-semibold">{totalChannels}</span>
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-400">Last sync: </span>
                    <span className="text-white font-semibold">
                      {new Date(lastSync).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                {/* Quick Filters */}
                <div className="flex items-center gap-1 bg-[#1a1a1a] rounded-lg p-1">
                  {[
                    { key: 'all', label: 'All' },
                    { key: 'pinned', label: 'Pinned' },
                    { key: 'recent', label: 'Recent' }
                  ].map(({ key, label }) => (
                    <button
                      key={key}
                      onClick={() => setFilter(key as any)}
                      className={`px-3 py-1.5 text-sm rounded-md transition-colors duration-150 ${
                        filter === key
                          ? 'bg-[#10b981] text-white'
                          : 'text-gray-400 hover:text-white'
                      }`}
                    >
                      {label}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Channel Lists Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-6">
              {/* Create New List Card */}
              <CreateNewListCard onClick={onCreateList} />

              {/* Channel List Cards */}
              {filteredLists.map((list, index) => (
                <ChannelListCard
                  key={list.id}
                  list={list}
                  onView={() => onViewList(list.id)}
                  onTogglePin={() => onTogglePin(list.id)}
                  onDelete={() => onDeleteList(list.id)}
                  style={{
                    animationDelay: `${index * 100}ms`
                  }}
                />
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

const CreateNewListCard: React.FC<{ onClick: () => void }> = ({ onClick }) => {
  return (
    <button
      onClick={onClick}
      className="group min-h-[200px] bg-[#10b981]/5 border-2 border-dashed border-[#10b981] rounded-lg
                 hover:bg-[#10b981]/10 transition-all duration-200 hover:scale-[1.02]
                 flex flex-col items-center justify-center p-6 focus:outline-none focus:ring-2 focus:ring-[#10b981]"
    >
      <PlusIcon className="w-12 h-12 text-[#10b981] mb-4 group-hover:scale-110 transition-transform duration-200" />
      <h3 className="text-lg font-semibold text-white mb-2">Create new channel list</h3>
      <p className="text-sm text-gray-400 text-center">Start tracking your favorite channels</p>
    </button>
  );
};

const ChannelListCard: React.FC<{
  list: ChannelList;
  onView: () => void;
  onTogglePin: () => void;
  onDelete: () => void;
  style?: React.CSSProperties;
}> = ({ list, onView, onTogglePin, onDelete, style }) => {
  const [showMenu, setShowMenu] = useState(false);

  return (
    <div
      className="group min-h-[200px] bg-gradient-to-br from-[#1a1a1a] to-[#151515] border border-[#333]
                 rounded-lg p-6 hover:scale-[1.02] hover:shadow-xl hover:shadow-black/20
                 transition-all duration-200 animate-fade-in relative"
      style={style}
    >
      {/* Pin Indicator */}
      {list.isPinned && (
        <StarIconSolid className="absolute top-4 right-4 w-4 h-4 text-[#10b981]" />
      )}

      {/* Header */}
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-white mb-2 truncate">{list.name}</h3>
        <div className="flex items-center gap-3 text-xs">
          <span className="text-gray-400">{list.channelCount} channels</span>
          <span className="text-gray-500">Updated {list.lastUpdated}</span>
        </div>
      </div>

      {/* Channel Previews */}
      <div className="mb-4">
        <div className="flex items-center -space-x-2 mb-3">
          {list.channels.slice(0, 4).map((channel, index) => (
            <img
              key={channel.id}
              src={channel.avatar}
              alt={channel.name}
              className="w-8 h-8 rounded-full border-2 border-[#1a1a1a] bg-gray-700"
              style={{ zIndex: 4 - index }}
            />
          ))}
          {list.channelCount > 4 && (
            <div className="w-8 h-8 rounded-full bg-gray-700 border-2 border-[#1a1a1a]
                           flex items-center justify-center text-xs text-gray-300">
              +{list.channelCount - 4}
            </div>
          )}
        </div>

        {/* Popular Video Preview */}
        {list.popularVideo && (
          <div className="flex gap-3">
            <div className="relative">
              <img
                src={list.popularVideo.thumbnail}
                alt={list.popularVideo.title}
                className="w-20 h-[45px] object-cover rounded bg-gray-700"
              />
              <PlayIcon className="absolute inset-0 m-auto w-4 h-4 text-white opacity-80" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-300 line-clamp-2 mb-1">
                {list.popularVideo.title}
              </p>
              <div className="flex items-center gap-1 text-xs text-gray-400">
                <EyeIcon className="w-3 h-3" />
                {list.popularVideo.viewCount}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between mt-auto">
        <button
          onClick={onView}
          className="px-2.5 py-1.5 bg-[#10b981] text-white text-sm rounded hover:bg-[#059669]
                     transition-colors duration-150"
        >
          View List
        </button>

        <div className="relative">
          <button
            onClick={() => setShowMenu(!showMenu)}
            className="p-1 text-gray-400 hover:text-white transition-colors duration-150"
          >
            <EllipsisVerticalIcon className="w-4 h-4" />
          </button>

          {showMenu && (
            <div className="absolute right-0 bottom-full mb-2 bg-[#1a1a1a] border border-[#333]
                           rounded-lg shadow-xl z-10 min-w-[120px]">
              <button
                onClick={() => {
                  onTogglePin();
                  setShowMenu(false);
                }}
                className="w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-[#333]
                           flex items-center gap-2"
              >
                <StarIcon className="w-4 h-4" />
                {list.isPinned ? 'Unpin' : 'Pin'}
              </button>
              <button
                onClick={() => {
                  onDelete();
                  setShowMenu(false);
                }}
                className="w-full px-3 py-2 text-left text-sm text-red-400 hover:bg-[#333]
                           flex items-center gap-2"
              >
                Delete
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const EmptyState: React.FC<{ onCreateList: () => void }> = ({ onCreateList }) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
      {/* Large Illustration */}
      <div className="w-32 h-32 mb-8 bg-gray-600/20 rounded-full flex items-center justify-center">
        <svg className="w-16 h-16 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5}
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      </div>

      {/* Main Message */}
      <h2 className="text-2xl font-semibold text-white mb-4">No channel lists yet</h2>

      {/* Supporting Text */}
      <p className="text-base text-gray-400 mb-8 max-w-md">
        Create your first list to start tracking YouTube channels and discover popular videos
      </p>

      {/* Primary CTA */}
      <button
        onClick={onCreateList}
        className="flex items-center gap-3 px-6 py-4 bg-[#10b981] text-white rounded-lg
                   hover:bg-[#059669] transition-colors duration-150 font-medium text-base mb-6"
      >
        <PlusIcon className="w-5 h-5" />
        Create Your First List
      </button>

      {/* Secondary Links */}
      <div className="flex items-center gap-6 text-sm">
        <button className="text-gray-400 hover:text-white hover:underline transition-colors duration-150">
          Import from CSV
        </button>
        <span className="text-gray-600">|</span>
        <button className="text-gray-400 hover:text-white hover:underline transition-colors duration-150">
          Browse Examples
        </button>
      </div>
    </div>
  );
};

const LoadingSkeleton: React.FC = () => {
  return (
    <div className="min-h-screen bg-[#0f0f0f]">
      <div className="w-full px-8 py-8">
        {/* Header Skeleton */}
        <header className="mb-12">
          <div className="flex items-start justify-between">
            <div>
              <div className="h-8 w-64 bg-gray-700 rounded mb-2 animate-pulse"></div>
              <div className="h-4 w-96 bg-gray-700 rounded animate-pulse"></div>
            </div>
            <div className="h-12 w-32 bg-gray-700 rounded-lg animate-pulse"></div>
          </div>
        </header>

        {/* Stats Bar Skeleton */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-8">
              <div className="h-4 w-24 bg-gray-700 rounded animate-pulse"></div>
              <div className="h-4 w-32 bg-gray-700 rounded animate-pulse"></div>
              <div className="h-4 w-28 bg-gray-700 rounded animate-pulse"></div>
            </div>
            <div className="h-8 w-48 bg-gray-700 rounded-lg animate-pulse"></div>
          </div>
        </div>

        {/* Grid Skeleton */}
        <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, index) => (
            <div
              key={index}
              className="min-h-[200px] bg-gray-800 rounded-lg p-6 animate-pulse"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="h-5 w-3/4 bg-gray-700 rounded mb-3"></div>
              <div className="h-3 w-1/2 bg-gray-700 rounded mb-4"></div>
              <div className="flex -space-x-2 mb-4">
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className="w-8 h-8 bg-gray-700 rounded-full"></div>
                ))}
              </div>
              <div className="flex gap-3 mb-4">
                <div className="w-20 h-[45px] bg-gray-700 rounded"></div>
                <div className="flex-1">
                  <div className="h-3 w-full bg-gray-700 rounded mb-2"></div>
                  <div className="h-3 w-2/3 bg-gray-700 rounded"></div>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <div className="h-6 w-16 bg-gray-700 rounded"></div>
                <div className="h-4 w-4 bg-gray-700 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};