import React, { useMemo, useState, useRef } from 'react';
import type { Channel } from '../types';
import { PlusIcon, MoreHorizontalIcon, PinIcon } from './Icons';
import { useClickOutside } from '../lib/utils';

interface TrackedChannelsPageProps {
    channels: Channel[];
    onSelectChannel: (id: string) => void;
    onAddChannelClick: () => void;
    onDeleteChannel: (id: string) => Promise<void>;
    onRenameChannel: (id: string, newName: string) => Promise<void>;
    onDuplicateChannel: (id: string) => Promise<void>;
    onPinChannel: (id: string, isPinned: boolean) => Promise<void>;
}

// Create New Channel List Card - Exact Design Specification
const CreateChannelButton: React.FC<{ onClick: () => void }> = ({ onClick }) => (
    <div
        className="border-2 border-dashed border-[#10b981] rounded-xl transition-all duration-150 hover:border-solid hover:bg-[#10b981]/5"
        style={{
            height: '140px',
            background: 'transparent'
        }}
    >
        <button
            onClick={onClick}
            className="w-full h-full flex flex-col items-center justify-center text-[#10b981] focus:outline-none focus:ring-2 focus:ring-[#10b981] focus:ring-offset-2 focus:ring-offset-[#0f0f0f]"
            style={{ padding: '24px' }}
            aria-label="Create new channel list"
        >
            <PlusIcon
                className="w-6 h-6 mb-3 text-[#10b981]"
            />
            <span
                className="text-center font-medium"
                style={{
                    fontSize: '16px',
                    color: '#10b981',
                    lineHeight: '1.4'
                }}
            >
                Create new channel list
            </span>
        </button>
    </div>
);

// Standard Channel List Card - Exact Design Specification
const ChannelListCard: React.FC<{
    channel: Channel;
    onSelect: () => void;
    onDelete: (id: string) => Promise<void>;
    onRename: (id: string, newName: string) => Promise<void>;
    onDuplicate: (id: string) => Promise<void>;
    onPin: (id: string, isPinned: boolean) => Promise<void>;
}> = ({ channel, onSelect, onDelete, onRename, onDuplicate, onPin }) => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isRenaming, setIsRenaming] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [isDeleteLoading, setIsDeleteLoading] = useState(false);
    const [isModalExiting, setIsModalExiting] = useState(false);
    const [editedName, setEditedName] = useState(channel.name);
    const [showToast, setShowToast] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);
    const cancelButtonRef = useRef<HTMLButtonElement>(null);
    const deleteModalRef = useRef<HTMLDivElement>(null);

    useClickOutside(menuRef, () => setIsMenuOpen(false));

    const handlePin = async () => {
        await onPin(channel.id, !channel.isPinned);
        setIsMenuOpen(false);
    };

    const handleRename = async () => {
        if (editedName.trim() && editedName !== channel.name) {
            await onRename(channel.id, editedName.trim());
        }
        setIsRenaming(false);
    };

    const handleDuplicate = async () => {
        await onDuplicate(channel.id);
        setIsMenuOpen(false);
        setShowToast(true);
        setTimeout(() => setShowToast(false), 3000);
    };

    const handleDelete = async () => {
        setIsDeleteLoading(true);
        try {
            await onDelete(channel.id);
            setIsDeleting(false);
        } catch (error) {
            // Error handling - keep modal open and show error state
            console.error('Failed to delete channel:', error);
        } finally {
            setIsDeleteLoading(false);
        }
    };

    // Enhanced modal close with exit animation
    const handleDeleteModalClose = () => {
        setIsModalExiting(true);
        setTimeout(() => {
            setIsDeleting(false);
            setIsModalExiting(false);
        }, 150); // Match exit animation duration
    };

    // Focus management for delete modal
    const handleDeleteModalOpen = () => {
        setIsDeleting(true);
        setIsModalExiting(false);
        // Focus the cancel button by default for better UX
        setTimeout(() => {
            cancelButtonRef.current?.focus();
        }, 200); // Wait for enter animation
    };

    // Enhanced keyboard navigation for delete modal
    const handleDeleteModalKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Escape') {
            handleDeleteModalClose();
        }
        // Focus trap within modal
        if (e.key === 'Tab') {
            const focusableElements = deleteModalRef.current?.querySelectorAll(
                'button:not([disabled]), [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            if (focusableElements && focusableElements.length > 0) {
                const firstElement = focusableElements[0] as HTMLElement;
                const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

                if (e.shiftKey && document.activeElement === firstElement) {
                    e.preventDefault();
                    lastElement.focus();
                } else if (!e.shiftKey && document.activeElement === lastElement) {
                    e.preventDefault();
                    firstElement.focus();
                }
            }
        }
    };

    return (
        <>
            <div
                className="relative bg-[#1a1a1a] border border-[#333] rounded-xl transition-all duration-150 hover:shadow-lg cursor-pointer"
                style={{ height: '140px' }}
                onClick={onSelect}
            >
                {/* Card Header Section */}
                <div className="absolute top-5 left-5 right-5 flex items-start justify-between">
                    <h3
                        className="text-white font-semibold truncate pr-2"
                        style={{ fontSize: '16px', fontWeight: 600 }}
                    >
                        {channel.name}
                    </h3>
                    <div className="flex items-center gap-1 flex-shrink-0">
                        {channel.isPinned && (
                            <PinIcon className="w-4 h-4 text-[#10b981]" />
                        )}
                        <div ref={menuRef} className="relative">
                            <button
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setIsMenuOpen(!isMenuOpen);
                                }}
                                className="p-1 rounded text-[#666] hover:text-white hover:bg-[#333] focus:outline-none focus:ring-2 focus:ring-[#00d4ff] focus:ring-offset-2 focus:ring-offset-[#1a1a1a] transition-all duration-200"
                                aria-label="Channel options"
                                aria-expanded={isMenuOpen}
                                aria-haspopup="menu"
                                id="options-menu-button"
                            >
                                <MoreHorizontalIcon className="w-4 h-4" />
                            </button>

                            {/* Futuristic Options Menu */}
                            {isMenuOpen && (
                                <div
                                    className="absolute top-full right-0 options-menu options-menu-enter z-20"
                                    style={{ marginTop: '8px' }}
                                    role="menu"
                                    aria-labelledby="options-menu-button"
                                >
                                    {/* Pin/Unpin Item with Electric Blue */}
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handlePin();
                                        }}
                                        className="menu-item menu-item-pin"
                                        role="menuitem"
                                        aria-label={channel.isPinned ? 'Unpin channel list' : 'Pin channel list'}
                                    >
                                        <svg
                                            className="menu-icon"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                            strokeWidth={1.5}
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"
                                            />
                                        </svg>
                                        {channel.isPinned ? 'Unpin' : 'Pin'}
                                    </button>

                                    {/* Rename Item with Neon Orange */}
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            setIsRenaming(true);
                                            setIsMenuOpen(false);
                                        }}
                                        className="menu-item menu-item-rename"
                                        role="menuitem"
                                        aria-label="Rename channel list"
                                    >
                                        <svg
                                            className="menu-icon"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                            strokeWidth={1.5}
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
                                            />
                                        </svg>
                                        Rename
                                    </button>

                                    {/* Duplicate Item with Electric Purple */}
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleDuplicate();
                                        }}
                                        className="menu-item menu-item-duplicate"
                                        role="menuitem"
                                        aria-label="Duplicate channel list"
                                    >
                                        <svg
                                            className="menu-icon"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                            strokeWidth={1.5}
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75"
                                            />
                                        </svg>
                                        Duplicate
                                    </button>

                                    {/* Elegant Divider */}
                                    <div className="menu-divider" />

                                    {/* Delete Item with Bright Red */}
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleDeleteModalOpen();
                                            setIsMenuOpen(false);
                                        }}
                                        className="menu-item menu-item-delete"
                                        role="menuitem"
                                        aria-label="Delete channel list"
                                    >
                                        <svg
                                            className="menu-icon"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                            strokeWidth={1.5}
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                            />
                                        </svg>
                                        Delete
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Card Content Area - Middle section for future content */}
                <div className="absolute top-16 left-5 right-5 bottom-5">
                    {/* Future content area */}
                </div>
            </div>

            {/* Futuristic Rename Modal */}
            {isRenaming && (
                <div
                    className="fixed inset-0 flex items-center justify-center z-50 rename-modal-overlay"
                    onClick={() => setIsRenaming(false)}
                >
                    <div
                        className="rename-modal rename-modal-enter"
                        onClick={(e) => e.stopPropagation()}
                        role="dialog"
                        aria-modal="true"
                        aria-labelledby="rename-modal-title"
                    >
                        {/* Modal Title */}
                        <h2
                            id="rename-modal-title"
                            className="rename-modal-title"
                        >
                            Rename channel list
                        </h2>

                        {/* Elegant Divider */}
                        <div className="rename-modal-divider" />

                        {/* Input Field */}
                        <input
                            type="text"
                            value={editedName}
                            onChange={(e) => setEditedName(e.target.value)}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    e.preventDefault();
                                    handleRename();
                                }
                                if (e.key === 'Escape') {
                                    e.preventDefault();
                                    setIsRenaming(false);
                                }
                            }}
                            className="rename-input"
                            placeholder="Enter channel list name..."
                            autoFocus
                            onFocus={(e) => e.target.select()}
                            aria-label="Channel list name"
                        />

                        {/* Another Elegant Divider */}
                        <div className="rename-modal-divider" />

                        {/* Save Button */}
                        <button
                            onClick={handleRename}
                            className="rename-save-button"
                            disabled={!editedName.trim() || editedName === channel.name}
                            aria-label="Save channel list name"
                        >
                            Save Changes
                        </button>

                        {/* Cancel Button */}
                        <button
                            onClick={() => setIsRenaming(false)}
                            className="rename-cancel-button"
                            aria-label="Cancel rename operation"
                        >
                            Cancel
                        </button>
                    </div>
                </div>
            )}

            {/* Enhanced Confirm Delete Modal */}
            {isDeleting && (
                <div
                    className="fixed inset-0 flex items-center justify-center z-50 backdrop-blur"
                    style={{
                        backgroundColor: 'rgba(0, 0, 0, 0.6)',
                        animation: 'modalEnter 200ms ease-out forwards'
                    }}
                    onClick={handleDeleteModalClose}
                >
                    <div
                        ref={deleteModalRef}
                        className={`bg-[#1a1a1a] w-full mx-4 ${isModalExiting ? 'modal-exit' : 'modal-enter'}`}
                        style={{
                            maxWidth: '400px',
                            borderRadius: '16px',
                            padding: '32px',
                            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.4)'
                        }}
                        onClick={(e) => e.stopPropagation()}
                        onKeyDown={handleDeleteModalKeyDown}
                        role="alertdialog"
                        aria-modal="true"
                        aria-labelledby="delete-modal-title"
                        aria-describedby="delete-modal-description"
                    >
                        {/* Modal Title */}
                        <h2
                            id="delete-modal-title"
                            className="text-white font-bold"
                            style={{
                                fontSize: '20px',
                                fontWeight: 700,
                                marginBottom: '24px',
                                lineHeight: '1.2'
                            }}
                        >
                            Confirm Deletion
                        </h2>

                        {/* Modal Description */}
                        <p
                            id="delete-modal-description"
                            className="text-[#ccc]"
                            style={{
                                fontSize: '14px',
                                lineHeight: '1.5',
                                marginBottom: '32px'
                            }}
                        >
                            Are you sure you want to delete this item? This action cannot be undone.
                        </p>

                        {/* Optional Divider */}
                        <div
                            className="border-t border-[#333]"
                            style={{ marginBottom: '32px' }}
                        />

                        {/* Enhanced Button Container */}
                        <div className="flex" style={{ gap: '16px' }}>
                            {/* Cancel Button - Modern Secondary Design */}
                            <button
                                ref={cancelButtonRef}
                                onClick={handleDeleteModalClose}
                                disabled={isDeleteLoading}
                                className="flex-1 text-[#ccc] hover:text-white hover:bg-[#333]/50 hover:border-[#666] focus:outline-none focus:border-[#10b981] focus:ring-2 focus:ring-[#10b981]/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-150 border border-[#444]"
                                style={{
                                    fontSize: '14px',
                                    fontWeight: 600,
                                    height: '48px',
                                    borderRadius: '12px',
                                    padding: '0 24px'
                                }}
                                aria-label="Cancel delete operation"
                                type="button"
                            >
                                Cancel
                            </button>

                            {/* Delete Button - Modern Primary Destructive Design */}
                            <button
                                onClick={handleDelete}
                                disabled={isDeleteLoading}
                                className="flex-1 text-white focus:outline-none focus:ring-2 focus:ring-[#ef4444]/20 disabled:opacity-50 disabled:cursor-not-allowed delete-button-gradient"
                                style={{
                                    fontSize: '14px',
                                    fontWeight: 600,
                                    height: '48px',
                                    borderRadius: '12px',
                                    padding: '0 24px',
                                    border: 'none'
                                }}
                                aria-label={isDeleteLoading ? 'Deleting item...' : 'Confirm delete item'}
                                type="button"
                            >
                                {isDeleteLoading ? (
                                    <span className="flex items-center justify-center gap-2">
                                        <svg
                                            className="animate-spin w-4 h-4"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            aria-hidden="true"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            />
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                            />
                                        </svg>
                                        Deleting...
                                    </span>
                                ) : (
                                    'Delete'
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Toast Notification */}
            {showToast && (
                <div
                    className="fixed top-4 left-1/2 transform -translate-x-1/2 bg-[#10b981] text-white rounded-lg z-50 transition-all duration-300"
                    style={{
                        padding: '12px 16px',
                        fontSize: '14px',
                        animation: 'slideDown 0.3s ease-out'
                    }}
                >
                    Duplicated list as "Copy of {channel.name}"
                </div>
            )}
        </>
    );
};

export const TrackedChannelsPage: React.FC<TrackedChannelsPageProps> = ({ 
    channels, 
    onSelectChannel, 
    onAddChannelClick,
    onDeleteChannel,
    onRenameChannel,
    onDuplicateChannel,
    onPinChannel
}) => {
    const sortedChannels = useMemo(() => {
        return [...channels].sort((a, b) => {
            // Pinned items come first, otherwise sort by creation date
            if (a.isPinned && !b.isPinned) return -1;
            if (!a.isPinned && b.isPinned) return 1;
            return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        });
    }, [channels]);
    
    return (
        <>
            {/* CSS Animations & Styles */}
            <style>{`
                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translate(-50%, -20px);
                    }
                    to {
                        opacity: 1;
                        transform: translate(-50%, 0);
                    }
                }

                @keyframes modalEnter {
                    from {
                        opacity: 0;
                        transform: scale(0.95);
                    }
                    to {
                        opacity: 1;
                        transform: scale(1);
                    }
                }

                @keyframes modalExit {
                    from {
                        opacity: 1;
                        transform: scale(1);
                    }
                    to {
                        opacity: 0;
                        transform: scale(0.95);
                    }
                }

                .modal-enter {
                    animation: modalEnter 200ms ease-out forwards;
                }

                .modal-exit {
                    animation: modalExit 150ms ease-in forwards;
                }

                .delete-button-gradient {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
                    transition: all 150ms ease;
                }

                .delete-button-gradient:hover {
                    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
                    box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
                }

                .delete-button-gradient:active {
                    transform: scale(0.98);
                }

                .backdrop-blur {
                    backdrop-filter: blur(4px);
                    -webkit-backdrop-filter: blur(4px);
                }

                /* Futuristic Options Menu - Dark Theme with Bold Accents */
                .options-menu {
                    background: rgba(15, 15, 15, 0.85);
                    backdrop-filter: blur(12px) saturate(180%);
                    -webkit-backdrop-filter: blur(12px) saturate(180%);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 24px;
                    box-shadow:
                        0 20px 40px rgba(0, 0, 0, 0.6),
                        0 0 0 1px rgba(255, 255, 255, 0.05),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
                    min-width: 200px;
                    max-width: 240px;
                    padding: 16px 0;
                    transform-origin: top right;
                    overflow: hidden;
                }

                .options-menu-enter {
                    animation: futuristicMenuEnter 300ms cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
                }

                .options-menu-exit {
                    animation: futuristicMenuExit 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
                }

                @keyframes futuristicMenuEnter {
                    from {
                        opacity: 0;
                        transform: scale(0.9) translateY(-10px);
                        filter: blur(4px);
                    }
                    to {
                        opacity: 1;
                        transform: scale(1) translateY(0);
                        filter: blur(0);
                    }
                }

                @keyframes futuristicMenuExit {
                    from {
                        opacity: 1;
                        transform: scale(1) translateY(0);
                        filter: blur(0);
                    }
                    to {
                        opacity: 0;
                        transform: scale(0.95) translateY(-5px);
                        filter: blur(2px);
                    }
                }

                .menu-item {
                    display: flex;
                    align-items: center;
                    width: 100%;
                    padding: 14px 20px;
                    min-height: 48px;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                    font-size: 14px;
                    font-weight: 500;
                    letter-spacing: 0.01em;
                    line-height: 1.4;
                    color: rgba(255, 255, 255, 0.8);
                    background: transparent;
                    border: none;
                    cursor: pointer;
                    transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
                    text-align: left;
                    position: relative;
                }

                .menu-item::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.1), transparent);
                    opacity: 0;
                    transition: opacity 250ms ease;
                }

                .menu-item:hover {
                    color: rgba(255, 255, 255, 1);
                    background: rgba(255, 255, 255, 0.05);
                    transform: translateX(2px);
                }

                .menu-item:hover::before {
                    opacity: 1;
                }

                .menu-item:hover .menu-icon {
                    transform: scale(1.1);
                    filter: drop-shadow(0 0 8px currentColor);
                }

                .menu-item:focus {
                    outline: none;
                    color: rgba(255, 255, 255, 1);
                    background: rgba(255, 255, 255, 0.08);
                    box-shadow: inset 0 0 0 2px rgba(0, 123, 255, 0.6);
                }

                .menu-item:active {
                    transform: translateX(2px) scale(0.98);
                    background: rgba(255, 255, 255, 0.1);
                }

                .menu-item:active .menu-icon {
                    transform: scale(1.05);
                }

                .menu-icon {
                    width: 18px;
                    height: 18px;
                    margin-right: 16px;
                    flex-shrink: 0;
                    transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
                }

                .menu-divider {
                    height: 1px;
                    margin: 8px 20px;
                    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
                }

                /* Accent Colors for Different Actions */
                .menu-item-pin .menu-icon {
                    color: #00d4ff; /* Electric Blue */
                }

                .menu-item-rename .menu-icon {
                    color: #ff6b35; /* Neon Orange */
                }

                .menu-item-duplicate .menu-icon {
                    color: #7c3aed; /* Electric Purple */
                }

                .menu-item-delete .menu-icon {
                    color: #ff1744; /* Bright Red */
                }

                .menu-item-delete {
                    color: rgba(255, 23, 68, 0.9);
                }

                .menu-item-delete:hover {
                    color: rgba(255, 23, 68, 1);
                    background: rgba(255, 23, 68, 0.1);
                }

                .menu-item-delete:hover::before {
                    background: linear-gradient(90deg, transparent, rgba(255, 23, 68, 0.2), transparent);
                }

                .menu-item-delete:focus {
                    box-shadow: inset 0 0 0 2px rgba(255, 23, 68, 0.6);
                }

                /* Futuristic Rename Modal - Dark Theme with Bold Accents */
                .rename-modal-overlay {
                    background: rgba(0, 0, 0, 0.7);
                    backdrop-filter: blur(8px) saturate(150%);
                    -webkit-backdrop-filter: blur(8px) saturate(150%);
                }

                .rename-modal {
                    background: rgba(20, 20, 20, 0.95);
                    backdrop-filter: blur(16px) saturate(180%);
                    -webkit-backdrop-filter: blur(16px) saturate(180%);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 24px;
                    box-shadow:
                        0 25px 50px rgba(0, 0, 0, 0.7),
                        0 0 0 1px rgba(255, 255, 255, 0.05),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1),
                        0 0 40px rgba(0, 255, 136, 0.1);
                    max-width: 480px;
                    width: 100%;
                    margin: 0 16px;
                    padding: 32px;
                    transform-origin: center;
                }

                .rename-modal-enter {
                    animation: renameModalEnter 350ms cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
                }

                .rename-modal-exit {
                    animation: renameModalExit 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
                }

                @keyframes renameModalEnter {
                    from {
                        opacity: 0;
                        transform: scale(0.9) translateY(-20px);
                        filter: blur(4px);
                    }
                    to {
                        opacity: 1;
                        transform: scale(1) translateY(0);
                        filter: blur(0);
                    }
                }

                @keyframes renameModalExit {
                    from {
                        opacity: 1;
                        transform: scale(1) translateY(0);
                        filter: blur(0);
                    }
                    to {
                        opacity: 0;
                        transform: scale(0.95) translateY(-10px);
                        filter: blur(2px);
                    }
                }

                .rename-modal-title {
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                    font-size: 24px;
                    font-weight: 700;
                    letter-spacing: -0.02em;
                    line-height: 1.2;
                    color: rgba(255, 255, 255, 0.95);
                    text-align: center;
                    margin-bottom: 32px;
                }

                .rename-modal-divider {
                    height: 1px;
                    margin: 24px 0;
                    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
                }

                .rename-input {
                    width: 100%;
                    padding: 16px 20px;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                    font-size: 16px;
                    font-weight: 500;
                    line-height: 1.4;
                    color: rgba(255, 255, 255, 0.9);
                    background: rgba(255, 255, 255, 0.05);
                    border: 2px solid rgba(255, 255, 255, 0.1);
                    border-radius: 16px;
                    outline: none;
                    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
                    margin-bottom: 24px;
                }

                .rename-input::placeholder {
                    color: rgba(255, 255, 255, 0.4);
                }

                .rename-input:focus {
                    border-color: #00ff88;
                    background: rgba(255, 255, 255, 0.08);
                    box-shadow:
                        0 0 0 4px rgba(0, 255, 136, 0.2),
                        0 0 20px rgba(0, 255, 136, 0.3);
                    transform: translateY(-1px);
                }

                .rename-save-button {
                    width: 100%;
                    padding: 16px 24px;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                    font-size: 16px;
                    font-weight: 600;
                    letter-spacing: 0.01em;
                    color: rgba(0, 0, 0, 0.9);
                    background: linear-gradient(135deg, #00ff88 0%, #00d4ff 100%);
                    border: none;
                    border-radius: 16px;
                    cursor: pointer;
                    transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow:
                        0 8px 24px rgba(0, 255, 136, 0.3),
                        0 0 0 1px rgba(255, 255, 255, 0.1);
                    position: relative;
                    overflow: hidden;
                }

                .rename-save-button::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                    transition: left 500ms ease;
                }

                .rename-save-button:hover {
                    background: linear-gradient(135deg, #00ff88 0%, #00d4ff 100%);
                    box-shadow:
                        0 12px 32px rgba(0, 255, 136, 0.4),
                        0 0 40px rgba(0, 255, 136, 0.3);
                    transform: translateY(-2px);
                    filter: brightness(1.1);
                }

                .rename-save-button:hover::before {
                    left: 100%;
                }

                .rename-save-button:active {
                    transform: translateY(-1px) scale(0.98);
                    box-shadow:
                        0 6px 16px rgba(0, 255, 136, 0.4),
                        0 0 20px rgba(0, 255, 136, 0.3);
                }

                .rename-save-button:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                    transform: none;
                    box-shadow: 0 4px 12px rgba(0, 255, 136, 0.2);
                }

                .rename-cancel-button {
                    width: 100%;
                    padding: 12px 24px;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                    font-size: 14px;
                    font-weight: 500;
                    color: rgba(255, 255, 255, 0.7);
                    background: transparent;
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 12px;
                    cursor: pointer;
                    transition: all 200ms ease;
                    margin-top: 12px;
                }

                .rename-cancel-button:hover {
                    color: rgba(255, 255, 255, 0.9);
                    border-color: rgba(255, 255, 255, 0.4);
                    background: rgba(255, 255, 255, 0.05);
                }
            `}</style>

            <div
                className="min-h-screen"
                style={{
                    background: '#0f0f0f',
                    fontFamily: 'system-ui, -apple-system, sans-serif'
                }}
            >
            {/* Container with exact 32px padding */}
            <div style={{ padding: '32px' }}>
                {/* Page Title - Exact Design Specification */}
                <h1
                    className="text-white font-bold mb-8"
                    style={{
                        fontSize: '28px',
                        fontWeight: 700,
                        lineHeight: '1.2'
                    }}
                >
                    Tracked channels
                </h1>

                {/* Card Grid System - Exact Design Specification */}
                <div
                    className="grid"
                    style={{
                        gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
                        gap: '20px'
                    }}
                >
                    <CreateChannelButton onClick={onAddChannelClick} />
                    {sortedChannels.map(channel => (
                        <ChannelListCard
                            key={channel.id}
                            channel={channel}
                            onSelect={() => onSelectChannel(channel.id)}
                            onDelete={onDeleteChannel}
                            onRename={onRenameChannel}
                            onDuplicate={onDuplicateChannel}
                            onPin={onPinChannel}
                        />
                    ))}
                </div>
            </div>
        </div>
        </>
    );
};
