import React, { useMemo, useState, useRef } from 'react';
import type { Channel } from '../types';
import { PlusIcon, MoreHorizontalIcon, PinIcon } from './Icons';
import { useClickOutside } from '../lib/utils';

interface TrackedChannelsPageProps {
    channels: Channel[];
    onSelectChannel: (id: string) => void;
    onAddChannelClick: () => void;
    onDeleteChannel: (id: string) => Promise<void>;
    onRenameChannel: (id: string, newName: string) => Promise<void>;
    onDuplicateChannel: (id: string) => Promise<void>;
    onPinChannel: (id: string, isPinned: boolean) => Promise<void>;
}

// Create New Channel List Card - Exact Design Specification
const CreateChannelButton: React.FC<{ onClick: () => void }> = ({ onClick }) => (
    <div
        className="border-2 border-dashed border-[#10b981] rounded-xl transition-all duration-150 hover:border-solid hover:bg-[#10b981]/5"
        style={{
            height: '140px',
            background: 'transparent'
        }}
    >
        <button
            onClick={onClick}
            className="w-full h-full flex flex-col items-center justify-center text-[#10b981] focus:outline-none focus:ring-2 focus:ring-[#10b981] focus:ring-offset-2 focus:ring-offset-[#0f0f0f]"
            style={{ padding: '24px' }}
            aria-label="Create new channel list"
        >
            <PlusIcon
                className="w-6 h-6 mb-3 text-[#10b981]"
            />
            <span
                className="text-center font-medium"
                style={{
                    fontSize: '16px',
                    color: '#10b981',
                    lineHeight: '1.4'
                }}
            >
                Create new channel list
            </span>
        </button>
    </div>
);

// Standard Channel List Card - Exact Design Specification
const ChannelListCard: React.FC<{
    channel: Channel;
    onSelect: () => void;
    onDelete: (id: string) => Promise<void>;
    onRename: (id: string, newName: string) => Promise<void>;
    onDuplicate: (id: string) => Promise<void>;
    onPin: (id: string, isPinned: boolean) => Promise<void>;
}> = ({ channel, onSelect, onDelete, onRename, onDuplicate, onPin }) => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isRenaming, setIsRenaming] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [editedName, setEditedName] = useState(channel.name);
    const [showToast, setShowToast] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);

    useClickOutside(menuRef, () => setIsMenuOpen(false));

    const handlePin = async () => {
        await onPin(channel.id, !channel.isPinned);
        setIsMenuOpen(false);
    };

    const handleRename = async () => {
        if (editedName.trim() && editedName !== channel.name) {
            await onRename(channel.id, editedName.trim());
        }
        setIsRenaming(false);
    };

    const handleDuplicate = async () => {
        await onDuplicate(channel.id);
        setIsMenuOpen(false);
        setShowToast(true);
        setTimeout(() => setShowToast(false), 3000);
    };

    const handleDelete = async () => {
        await onDelete(channel.id);
        setIsDeleting(false);
    };

    return (
        <>
            <div
                className="relative bg-[#1a1a1a] border border-[#333] rounded-xl transition-all duration-150 hover:shadow-lg cursor-pointer"
                style={{ height: '140px' }}
                onClick={onSelect}
            >
                {/* Card Header Section */}
                <div className="absolute top-5 left-5 right-5 flex items-start justify-between">
                    <h3
                        className="text-white font-semibold truncate pr-2"
                        style={{ fontSize: '16px', fontWeight: 600 }}
                    >
                        {channel.name}
                    </h3>
                    <div className="flex items-center gap-1 flex-shrink-0">
                        {channel.isPinned && (
                            <PinIcon className="w-4 h-4 text-[#10b981]" />
                        )}
                        <div ref={menuRef} className="relative">
                            <button
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setIsMenuOpen(!isMenuOpen);
                                }}
                                className="p-1 rounded text-[#666] hover:text-white hover:bg-[#333] transition-colors duration-150"
                                aria-label="Channel options"
                            >
                                <MoreHorizontalIcon className="w-4 h-4" />
                            </button>

                            {/* Options Menu */}
                            {isMenuOpen && (
                                <div
                                    className="absolute top-full right-0 mt-1 bg-[#2a2a2a] border border-[#444] rounded-lg shadow-xl z-20"
                                    style={{
                                        minWidth: '160px',
                                        boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
                                    }}
                                >
                                    <div className="py-2">
                                        <button
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handlePin();
                                            }}
                                            className="w-full px-4 py-3 text-left text-white hover:bg-[#333] transition-colors duration-150 flex items-center gap-3"
                                            style={{ fontSize: '14px' }}
                                        >
                                            <PinIcon className="w-4 h-4" />
                                            {channel.isPinned ? 'Unpin' : 'Pin'}
                                        </button>
                                        <button
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setIsRenaming(true);
                                                setIsMenuOpen(false);
                                            }}
                                            className="w-full px-4 py-3 text-left text-white hover:bg-[#333] transition-colors duration-150"
                                            style={{ fontSize: '14px' }}
                                        >
                                            Rename
                                        </button>
                                        <button
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleDuplicate();
                                            }}
                                            className="w-full px-4 py-3 text-left text-white hover:bg-[#333] transition-colors duration-150"
                                            style={{ fontSize: '14px' }}
                                        >
                                            Duplicate
                                        </button>
                                        <button
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setIsDeleting(true);
                                                setIsMenuOpen(false);
                                            }}
                                            className="w-full px-4 py-3 text-left text-[#ef4444] hover:bg-[#333] transition-colors duration-150"
                                            style={{ fontSize: '14px' }}
                                        >
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Card Content Area - Middle section for future content */}
                <div className="absolute top-16 left-5 right-5 bottom-5">
                    {/* Future content area */}
                </div>
            </div>

            {/* Rename Modal */}
            {isRenaming && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                    <div
                        className="bg-[#1a1a1a] rounded-xl p-6 w-full max-w-md mx-4"
                        onClick={(e) => e.stopPropagation()}
                    >
                        <h3
                            className="text-white font-semibold mb-4"
                            style={{ fontSize: '18px', fontWeight: 600 }}
                        >
                            Rename channel list
                        </h3>
                        <input
                            type="text"
                            value={editedName}
                            onChange={(e) => setEditedName(e.target.value)}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') handleRename();
                                if (e.key === 'Escape') setIsRenaming(false);
                            }}
                            className="w-full px-4 py-3 bg-[#0f0f0f] border-2 border-[#333] rounded-lg text-white focus:border-[#10b981] focus:outline-none"
                            style={{ fontSize: '14px' }}
                            autoFocus
                            onFocus={(e) => e.target.select()}
                        />
                        <div className="flex gap-3 mt-6">
                            <button
                                onClick={() => setIsRenaming(false)}
                                className="flex-1 px-4 py-2 text-[#ccc] hover:text-white transition-colors duration-150"
                                style={{ fontSize: '14px' }}
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleRename}
                                className="flex-1 px-4 py-2 bg-[#10b981] text-white rounded-lg hover:bg-[#0ea474] transition-colors duration-150"
                                style={{ fontSize: '14px' }}
                            >
                                Save
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Delete Confirmation Modal */}
            {isDeleting && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                    <div
                        className="bg-[#1a1a1a] rounded-xl p-6 w-full max-w-md mx-4"
                        onClick={(e) => e.stopPropagation()}
                    >
                        <h3
                            className="text-white font-semibold mb-4"
                            style={{ fontSize: '18px', fontWeight: 600 }}
                        >
                            Delete List
                        </h3>
                        <p
                            className="text-[#ccc] mb-6"
                            style={{ fontSize: '14px', lineHeight: '1.5' }}
                        >
                            Are you sure you want to delete this channel list? This cannot be undone.
                        </p>
                        <div className="flex gap-3">
                            <button
                                onClick={() => setIsDeleting(false)}
                                className="flex-1 px-4 py-2 text-[#ccc] hover:text-white transition-colors duration-150"
                                style={{ fontSize: '14px' }}
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleDelete}
                                className="flex-1 px-4 py-2 bg-[#ef4444] text-white rounded-lg hover:bg-[#dc2626] transition-colors duration-150"
                                style={{ fontSize: '14px' }}
                            >
                                Delete
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Toast Notification */}
            {showToast && (
                <div
                    className="fixed top-4 left-1/2 transform -translate-x-1/2 bg-[#10b981] text-white rounded-lg z-50 transition-all duration-300"
                    style={{
                        padding: '12px 16px',
                        fontSize: '14px',
                        animation: 'slideDown 0.3s ease-out'
                    }}
                >
                    Duplicated list as "Copy of {channel.name}"
                </div>
            )}
        </>
    );
};

export const TrackedChannelsPage: React.FC<TrackedChannelsPageProps> = ({ 
    channels, 
    onSelectChannel, 
    onAddChannelClick,
    onDeleteChannel,
    onRenameChannel,
    onDuplicateChannel,
    onPinChannel
}) => {
    const sortedChannels = useMemo(() => {
        return [...channels].sort((a, b) => {
            // Pinned items come first, otherwise sort by creation date
            if (a.isPinned && !b.isPinned) return -1;
            if (!a.isPinned && b.isPinned) return 1;
            return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        });
    }, [channels]);
    
    return (
        <>
            {/* CSS Animations */}
            <style>{`
                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translate(-50%, -20px);
                    }
                    to {
                        opacity: 1;
                        transform: translate(-50%, 0);
                    }
                }
            `}</style>

            <div
                className="min-h-screen"
                style={{
                    background: '#0f0f0f',
                    fontFamily: 'system-ui, -apple-system, sans-serif'
                }}
            >
            {/* Container with exact 32px padding */}
            <div style={{ padding: '32px' }}>
                {/* Page Title - Exact Design Specification */}
                <h1
                    className="text-white font-bold mb-8"
                    style={{
                        fontSize: '28px',
                        fontWeight: 700,
                        lineHeight: '1.2'
                    }}
                >
                    Tracked channels
                </h1>

                {/* Card Grid System - Exact Design Specification */}
                <div
                    className="grid"
                    style={{
                        gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
                        gap: '20px'
                    }}
                >
                    <CreateChannelButton onClick={onAddChannelClick} />
                    {sortedChannels.map(channel => (
                        <ChannelListCard
                            key={channel.id}
                            channel={channel}
                            onSelect={() => onSelectChannel(channel.id)}
                            onDelete={onDeleteChannel}
                            onRename={onRenameChannel}
                            onDuplicate={onDuplicateChannel}
                            onPin={onPinChannel}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
};
