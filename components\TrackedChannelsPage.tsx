
import React, { useMemo } from 'react';
import type { Channel } from '../types';
import { ChannelCard } from './ChannelCard';
import { PlusIcon, RefreshIcon, StarIconSolid, PlayIcon } from './Icons';

interface TrackedChannelsPageProps {
    channels: Channel[];
    onSelectChannel: (id: string) => void;
    onAddChannelClick: () => void;
    onDeleteChannel: (id: string) => Promise<void>;
    onRenameChannel: (id: string, newName: string) => Promise<void>;
    onDuplicateChannel: (id: string) => Promise<void>;
    onPinChannel: (id: string, isPinned: boolean) => Promise<void>;
}

const CreateChannelButton: React.FC<{ onClick: () => void }> = ({ onClick }) => (
    <div className="min-h-[180px] max-w-[300px]">
        <button
            onClick={onClick}
            className="w-full h-full flex flex-col items-center justify-center p-8 text-[#10b981] border-2 border-dashed border-[#10b981] rounded-lg transition-all duration-200 hover:border-[#10b981] hover:bg-[#10b981]/8 hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-[#10b981] focus:ring-offset-2 focus:ring-offset-[#0f0f0f]"
        >
            <PlusIcon className="w-10 h-10 mb-4" />
            <h3 className="text-base font-semibold text-white mb-2">Create new channel list</h3>
            <p className="text-[13px] text-gray-400 text-center leading-relaxed">Track popular videos from your favorite channels</p>
        </button>
    </div>
);

export const TrackedChannelsPage: React.FC<TrackedChannelsPageProps> = ({
    channels,
    onSelectChannel,
    onAddChannelClick,
    onDeleteChannel,
    onRenameChannel,
    onDuplicateChannel,
    onPinChannel
}) => {
    const sortedChannels = useMemo(() => {
        return [...channels].sort((a, b) => {
            // Pinned items come first, otherwise sort by creation date
            if (a.isPinned && !b.isPinned) return -1;
            if (!a.isPinned && b.isPinned) return 1;
            return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        });
    }, [channels]);

    // Calculate statistics
    const totalChannels = channels.length;
    const pinnedChannels = channels.filter(c => c.isPinned).length;
    const lastUpdated = channels.length > 0 ?
        Math.max(...channels.map(c => new Date(c.updatedAt).getTime())) : Date.now();

    return (
        <div className="min-h-screen bg-[#0f0f0f]">
            <div className="px-8 py-6">
                {/* Enhanced Header Section */}
                <header className="pt-8 pb-6 mb-6">
                    <div className="flex items-start justify-between">
                        <div className="max-w-2xl">
                            <h1 className="text-[28px] font-bold text-white mb-2">Tracked channels</h1>
                            <p className="text-sm text-gray-400 leading-relaxed max-w-[600px]">
                                Organize your YouTube channels into lists to track popular content
                            </p>
                        </div>
                        {totalChannels > 0 && (
                            <div className="text-sm text-gray-500">
                                {totalChannels} list{totalChannels !== 1 ? 's' : ''}
                            </div>
                        )}
                    </div>
                </header>

                {/* Statistics Bar */}
                {totalChannels > 0 && (
                    <div className="mb-5">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-6">
                                <div className="text-sm">
                                    <span className="text-gray-400">Total lists: </span>
                                    <span className="text-white font-medium">{totalChannels}</span>
                                </div>
                                {pinnedChannels > 0 && (
                                    <div className="flex items-center gap-1 text-sm">
                                        <StarIconSolid className="w-3 h-3 text-[#10b981]" />
                                        <span className="text-gray-400">{pinnedChannels} pinned</span>
                                    </div>
                                )}
                                <div className="text-sm">
                                    <span className="text-gray-400">Last updated: </span>
                                    <span className="text-white font-medium">
                                        {new Date(lastUpdated).toLocaleDateString()}
                                    </span>
                                </div>
                            </div>

                            <button className="flex items-center gap-2 px-3 py-1.5 text-sm text-gray-400 hover:text-white border border-gray-600 rounded-lg hover:border-gray-500 transition-colors duration-200">
                                <RefreshIcon className="w-4 h-4" />
                                Sync All Lists
                            </button>
                        </div>
                    </div>
                )}

                {/* Enhanced Grid Layout */}
                <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-5">
                    <CreateChannelButton onClick={onAddChannelClick} />
                    {sortedChannels.map(channel => (
                        <EnhancedChannelCard
                            key={channel.id}
                            channel={channel}
                            onSelect={() => onSelectChannel(channel.id)}
                            onDelete={onDeleteChannel}
                            onRename={onRenameChannel}
                            onDuplicate={onDuplicateChannel}
                            onPin={onPinChannel}
                        />
                    ))}
                </div>

                {/* Quick Start Tips for few lists */}
                {totalChannels > 0 && totalChannels <= 2 && (
                    <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-5">
                        <QuickStartCard
                            title="Getting Started"
                            description="Add 5+ channels for better recommendations and trending insights"
                            icon="📈"
                        />
                        <QuickStartCard
                            title="Pro Tip"
                            description="Pin your most important lists to keep them at the top"
                            icon="📌"
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

// Enhanced Channel Card with better layout and visual hierarchy
const EnhancedChannelCard: React.FC<{
    channel: Channel;
    onSelect: () => void;
    onDelete: (id: string) => Promise<void>;
    onRename: (id: string, newName: string) => Promise<void>;
    onDuplicate: (id: string) => Promise<void>;
    onPin: (id: string, isPinned: boolean) => Promise<void>;
}> = ({ channel, onSelect, onDelete, onRename, onDuplicate, onPin }) => {
    // Parse channel count from youtubeId (comma-separated IDs)
    const channelCount = channel.youtubeId ? channel.youtubeId.split(',').filter(id => id.trim()).length : 0;

    // Calculate time since last update
    const timeSinceUpdate = () => {
        const now = new Date();
        const updated = new Date(channel.updatedAt);
        const diffHours = Math.floor((now.getTime() - updated.getTime()) / (1000 * 60 * 60));

        if (diffHours < 1) return 'Updated just now';
        if (diffHours < 24) return `Updated ${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
        const diffDays = Math.floor(diffHours / 24);
        return `Updated ${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    };

    return (
        <div className="min-h-[180px] max-w-[300px]">
            <div className="h-full bg-[#1a1a1a] border border-[#333] rounded-lg p-6 hover:scale-[1.02] hover:shadow-lg hover:shadow-black/20 transition-all duration-200 cursor-pointer relative"
                 onClick={onSelect}>

                {/* Pin Indicator */}
                {channel.isPinned && (
                    <StarIconSolid className="absolute top-4 right-4 w-4 h-4 text-[#10b981]" />
                )}

                {/* Card Header */}
                <div className="mb-4">
                    <div className="flex items-start justify-between">
                        <h3 className="text-lg font-semibold text-white truncate pr-2">{channel.name}</h3>
                        {channelCount > 0 && (
                            <span className="text-xs text-gray-400 whitespace-nowrap">
                                {channelCount} channel{channelCount !== 1 ? 's' : ''}
                            </span>
                        )}
                    </div>
                    <p className="text-[11px] text-gray-500 mt-1">{timeSinceUpdate()}</p>
                </div>

                {/* Content Preview */}
                <div className="mb-4">
                    {/* Channel Avatars Preview */}
                    {channelCount > 0 && (
                        <div className="flex items-center mb-3">
                            <div className="flex -space-x-2">
                                {/* Mock avatars - in real app these would be actual channel avatars */}
                                {Array.from({ length: Math.min(channelCount, 3) }).map((_, i) => (
                                    <div
                                        key={i}
                                        className="w-7 h-7 bg-gradient-to-br from-gray-600 to-gray-700 rounded-full border-2 border-[#1a1a1a] flex items-center justify-center text-xs text-white"
                                        style={{ zIndex: 3 - i }}
                                    >
                                        {String.fromCharCode(65 + i)}
                                    </div>
                                ))}
                                {channelCount > 3 && (
                                    <div className="w-7 h-7 bg-gray-700 rounded-full border-2 border-[#1a1a1a] flex items-center justify-center text-xs text-gray-300">
                                        +{channelCount - 3}
                                    </div>
                                )}
                            </div>
                            {channelCount > 0 && (
                                <div className="ml-3 flex items-center gap-1">
                                    <div className="w-2 h-2 bg-[#10b981] rounded-full"></div>
                                    <span className="text-xs text-gray-400">Active</span>
                                </div>
                            )}
                        </div>
                    )}

                    {/* Mock Popular Video Preview */}
                    {channelCount > 0 && (
                        <div className="flex gap-3">
                            <div className="w-15 h-[34px] bg-gradient-to-br from-gray-700 to-gray-800 rounded flex items-center justify-center">
                                <PlayIcon className="w-3 h-3 text-white opacity-80" />
                            </div>
                            <div className="flex-1 min-w-0">
                                <p className="text-[13px] text-gray-300 line-clamp-2 leading-tight mb-1">
                                    Latest trending video from tracked channels
                                </p>
                                <p className="text-[11px] text-gray-400">2.1M views</p>
                            </div>
                        </div>
                    )}
                </div>

                {/* Card Footer */}
                <div className="mt-auto">
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            onSelect();
                        }}
                        className="w-full py-2.5 bg-[#10b981] text-white text-sm font-medium rounded hover:bg-[#059669] transition-colors duration-200"
                    >
                        View List
                    </button>
                </div>
            </div>
        </div>
    );
};

// Quick Start Tips Card
const QuickStartCard: React.FC<{
    title: string;
    description: string;
    icon: string;
}> = ({ title, description, icon }) => {
    return (
        <div className="bg-[#1a1a1a] border border-[#333] rounded-lg p-6">
            <div className="flex items-start gap-4">
                <div className="text-2xl">{icon}</div>
                <div>
                    <h3 className="text-base font-semibold text-white mb-2">{title}</h3>
                    <p className="text-sm text-gray-400 leading-relaxed">{description}</p>
                </div>
            </div>
        </div>
    );
};

