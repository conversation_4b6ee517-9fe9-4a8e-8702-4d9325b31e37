import React, { useMemo } from 'react';
import type { Channel } from '../types';
import { ChannelCard } from './ChannelCard';
import { PlusIcon } from './Icons';

interface TrackedChannelsPageProps {
    channels: Channel[];
    onSelectChannel: (id: string) => void;
    onAddChannelClick: () => void;
    onDeleteChannel: (id: string) => Promise<void>;
    onRenameChannel: (id: string, newName: string) => Promise<void>;
    onDuplicateChannel: (id: string) => Promise<void>;
    onPinChannel: (id: string, isPinned: boolean) => Promise<void>;
}

const CreateChannelButton: React.FC<{ onClick: () => void }> = ({ onClick }) => (
    <div
        className="bg-[#1a1a1a] border-2 border-dashed border-[#10b981] rounded-lg transition-all duration-200 hover:border-solid hover:shadow-lg focus-within:outline-none focus-within:ring-2 focus-within:ring-[#10b981]"
        style={{ height: '112px' }}
    >
        <button
            onClick={onClick}
            className="w-full h-full flex flex-col items-center justify-center text-[#10b981] focus:outline-none"
            style={{ padding: '16px' }}
        >
            <PlusIcon className="w-6 h-6" style={{ marginBottom: '8px' }} />
            <span className="text-sm text-white font-medium" style={{ lineHeight: '1.4' }}>
                Create new channel list
            </span>
        </button>
    </div>
);

export const TrackedChannelsPage: React.FC<TrackedChannelsPageProps> = ({ 
    channels, 
    onSelectChannel, 
    onAddChannelClick,
    onDeleteChannel,
    onRenameChannel,
    onDuplicateChannel,
    onPinChannel
}) => {
    const sortedChannels = useMemo(() => {
        return [...channels].sort((a, b) => {
            // Pinned items come first, otherwise sort by creation date
            if (a.isPinned && !b.isPinned) return -1;
            if (!a.isPinned && b.isPinned) return 1;
            return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        });
    }, [channels]);
    
    return (
        <div className="min-h-screen bg-[#0f0f0f]">
            {/* Container with responsive padding */}
            <div className="px-4 py-4 sm:px-6 sm:py-6 lg:px-8 lg:py-8">
                {/* Header with proper spacing */}
                <header style={{ marginBottom: '32px' }}>
                    <h1 className="text-3xl font-bold text-white" style={{ marginBottom: '8px', lineHeight: '1.4' }}>
                        Tracked channels
                    </h1>
                    <p className="text-base text-gray-400" style={{ lineHeight: '1.4' }}>
                        Organize your YouTube channels into lists to track popular content
                    </p>
                </header>

                {/* Improved grid layout with responsive spacing */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-5">
                    <CreateChannelButton onClick={onAddChannelClick} />
                    {sortedChannels.map(channel => (
                        <ChannelCard
                            key={channel.id}
                            channel={channel}
                            onSelect={() => onSelectChannel(channel.id)}
                            onDelete={onDeleteChannel}
                            onRename={onRenameChannel}
                            onDuplicate={onDuplicateChannel}
                            onPin={onPinChannel}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
};
