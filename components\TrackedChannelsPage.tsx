
import React, { useMemo } from 'react';
import type { Channel } from '../types';
import { ChannelCard } from './ChannelCard';
import { PlusIcon, RefreshIcon, StarIconSolid, PlayIcon, EyeIcon, SubscribersIcon } from './Icons';

interface TrackedChannelsPageProps {
    channels: Channel[];
    onSelectChannel: (id: string) => void;
    onAddChannelClick: () => void;
    onDeleteChannel: (id: string) => Promise<void>;
    onRenameChannel: (id: string, newName: string) => Promise<void>;
    onDuplicateChannel: (id: string) => Promise<void>;
    onPinChannel: (id: string, isPinned: boolean) => Promise<void>;
}

const CreateChannelButton: React.FC<{ onClick: () => void }> = ({ onClick }) => (
    <div className="min-h-[200px] bg-[#0f0f0f] rounded-xl">
        <button
            onClick={onClick}
            className="w-full h-full flex flex-col items-center justify-center p-8 text-[#10b981] border-2 border-dashed border-[#10b981] rounded-xl transition-all duration-250 hover:border-[#10b981] hover:bg-[#10b981]/8 hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-[#10b981] focus:ring-offset-2 focus:ring-offset-[#0f0f0f] group"
        >
            <PlusIcon className="w-12 h-12 mb-4 transition-transform duration-250 group-hover:scale-110" />
            <h3 className="text-lg font-semibold text-white mb-2">Create new channel list</h3>
            <p className="text-sm text-gray-400 text-center leading-relaxed">Start tracking popular videos from your favorite channels</p>
        </button>
    </div>
);

export const TrackedChannelsPage: React.FC<TrackedChannelsPageProps> = ({
    channels,
    onSelectChannel,
    onAddChannelClick,
    onDeleteChannel,
    onRenameChannel,
    onDuplicateChannel,
    onPinChannel
}) => {
    const sortedChannels = useMemo(() => {
        return [...channels].sort((a, b) => {
            // Pinned items come first, otherwise sort by creation date
            if (a.isPinned && !b.isPinned) return -1;
            if (!a.isPinned && b.isPinned) return 1;
            return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        });
    }, [channels]);

    // Calculate statistics
    const totalChannels = channels.length;
    const pinnedChannels = channels.filter(c => c.isPinned).length;
    const totalTrackedChannels = channels.reduce((sum, channel) => {
        const channelCount = channel.youtubeId ? channel.youtubeId.split(',').filter(id => id.trim()).length : 0;
        return sum + channelCount;
    }, 0);
    const lastUpdated = channels.length > 0 ?
        Math.max(...channels.map(c => new Date(c.updatedAt).getTime())) : Date.now();

    const formatLastSync = (timestamp: number) => {
        const now = new Date();
        const updated = new Date(timestamp);
        const diffHours = Math.floor((now.getTime() - updated.getTime()) / (1000 * 60 * 60));

        if (diffHours < 1) return 'just now';
        if (diffHours < 24) return `${diffHours}h ago`;
        const diffDays = Math.floor(diffHours / 24);
        return `${diffDays}d ago`;
    };

    return (
        <div className="min-h-screen bg-[#0f0f0f]">
            <div className="max-w-7xl mx-auto px-8 py-8">
                {/* Enhanced Header Section */}
                <header className="mb-12">
                    <div className="flex items-start justify-between mb-6">
                        <div>
                            <h1 className="text-[32px] font-bold text-white mb-3">Tracked channels</h1>
                            <p className="text-base text-gray-400 leading-relaxed">
                                Organize and monitor your YouTube channel collections
                            </p>
                        </div>
                        <button className="flex items-center gap-2 px-4 py-2.5 text-sm text-gray-400 hover:text-white border border-gray-600 rounded-lg hover:border-gray-500 transition-all duration-200 hover:bg-gray-800/50">
                            <RefreshIcon className="w-4 h-4" />
                            Sync All
                        </button>
                    </div>

                    {/* Summary Stats Bar */}
                    {totalChannels > 0 && (
                        <div className="flex items-center gap-6 text-sm text-gray-500">
                            <span>{totalChannels} list{totalChannels !== 1 ? 's' : ''}</span>
                            <span>•</span>
                            <span>{totalTrackedChannels} total channels</span>
                            <span>•</span>
                            <span>Last synced {formatLastSync(lastUpdated)}</span>
                        </div>
                    )}
                </header>

                {/* Enhanced Grid Layout */}
                <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-6">
                    <CreateChannelButton onClick={onAddChannelClick} />
                    {sortedChannels.map(channel => (
                        <EnhancedChannelCard
                            key={channel.id}
                            channel={channel}
                            onSelect={() => onSelectChannel(channel.id)}
                            onDelete={onDeleteChannel}
                            onRename={onRenameChannel}
                            onDuplicate={onDuplicateChannel}
                            onPin={onPinChannel}
                        />
                    ))}
                </div>

                {/* Empty Space Utilization - Quick Actions & Tips */}
                {totalChannels > 0 && totalChannels <= 3 && (
                    <div className="mt-12 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                        <QuickActionCard
                            title="Quick Actions"
                            description="Bulk operations and export options"
                            icon="⚡"
                            actions={['Export all lists', 'Bulk sync', 'Import channels']}
                        />
                        <QuickActionCard
                            title="Statistics"
                            description="Track your channel performance"
                            icon="📊"
                            actions={[`${totalTrackedChannels} channels tracked`, 'View trending videos', 'Analytics dashboard']}
                        />
                        <QuickActionCard
                            title="Pro Tips"
                            description="Optimize your channel tracking"
                            icon="💡"
                            actions={['Pin important lists', 'Set sync schedules', 'Use smart filters']}
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

// Enhanced Channel Card with rich content preview
const EnhancedChannelCard: React.FC<{
    channel: Channel;
    onSelect: () => void;
    onDelete: (id: string) => Promise<void>;
    onRename: (id: string, newName: string) => Promise<void>;
    onDuplicate: (id: string) => Promise<void>;
    onPin: (id: string, isPinned: boolean) => Promise<void>;
}> = ({ channel, onSelect, onDelete, onRename, onDuplicate, onPin }) => {
    // Parse channel count from youtubeId (comma-separated IDs)
    const channelCount = channel.youtubeId ? channel.youtubeId.split(',').filter(id => id.trim()).length : 0;

    // Calculate time since last update
    const timeSinceUpdate = () => {
        const now = new Date();
        const updated = new Date(channel.updatedAt);
        const diffHours = Math.floor((now.getTime() - updated.getTime()) / (1000 * 60 * 60));

        if (diffHours < 1) return 'Updated just now';
        if (diffHours < 24) return `Updated ${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
        const diffDays = Math.floor(diffHours / 24);
        return `Updated ${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    };

    const isPinned = channel.isPinned;

    return (
        <div className={`min-h-[200px] bg-[#1a1a1a] border rounded-xl p-6 hover:scale-[1.02] hover:shadow-lg hover:shadow-black/20 transition-all duration-250 cursor-pointer relative group ${
            isPinned
                ? 'border-[#10b981]/30 bg-gradient-to-br from-[#1a1a1a] to-[#10b981]/5 border-l-4 border-l-[#10b981]'
                : 'border-[#333] hover:border-[#10b981]/50'
        }`}
             onClick={onSelect}>

            {/* Pin Indicator */}
            {isPinned && (
                <StarIconSolid className="absolute top-4 right-4 w-4 h-4 text-[#10b981]" />
            )}

            {/* Card Header */}
            <div className="mb-4">
                <div className="flex items-start justify-between">
                    <h3 className="text-lg font-semibold text-white truncate pr-2">{channel.name}</h3>
                    {channelCount > 0 && (
                        <span className="text-xs text-gray-400 whitespace-nowrap bg-gray-800 px-2 py-1 rounded-full">
                            {channelCount} channel{channelCount !== 1 ? 's' : ''}
                        </span>
                    )}
                </div>
                <p className="text-[11px] text-gray-500 mt-2">{timeSinceUpdate()}</p>
            </div>

            {/* Content Preview */}
            <div className="mb-6">
                {/* Channel Avatars Preview */}
                {channelCount > 0 && (
                    <div className="flex items-center justify-between mb-4">
                        <div className="flex -space-x-2">
                            {/* Mock avatars - in real app these would be actual channel avatars */}
                            {Array.from({ length: Math.min(channelCount, 4) }).map((_, i) => (
                                <div
                                    key={i}
                                    className="w-8 h-8 bg-gradient-to-br from-gray-600 to-gray-700 rounded-full border-2 border-[#1a1a1a] flex items-center justify-center text-xs text-white font-medium"
                                    style={{ zIndex: 4 - i }}
                                >
                                    {String.fromCharCode(65 + i)}
                                </div>
                            ))}
                            {channelCount > 4 && (
                                <div className="w-8 h-8 bg-gray-700 rounded-full border-2 border-[#1a1a1a] flex items-center justify-center text-xs text-gray-300 font-medium">
                                    +{channelCount - 4}
                                </div>
                            )}
                        </div>
                        {channelCount > 0 && (
                            <div className="flex items-center gap-1">
                                <div className="w-2 h-2 bg-[#10b981] rounded-full animate-pulse"></div>
                                <span className="text-xs text-gray-400">Active</span>
                            </div>
                        )}
                    </div>
                )}

                {/* Popular Video Preview */}
                {channelCount > 0 && (
                    <div className="flex gap-3">
                        <div className="w-20 h-[45px] bg-gradient-to-br from-gray-700 to-gray-800 rounded-lg flex items-center justify-center relative overflow-hidden">
                            <PlayIcon className="w-4 h-4 text-white opacity-80" />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                        </div>
                        <div className="flex-1 min-w-0">
                            <p className="text-[13px] text-gray-300 leading-tight mb-1 line-clamp-2">
                                Latest trending video from tracked channels
                            </p>
                            <div className="flex items-center gap-2 text-[11px] text-gray-400">
                                <EyeIcon className="w-3 h-3" />
                                <span>1.2M views</span>
                                <span>•</span>
                                <span>2h ago</span>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Card Footer */}
            <div className="mt-auto">
                <button
                    onClick={(e) => {
                        e.stopPropagation();
                        onSelect();
                    }}
                    className="w-full py-3 bg-[#10b981] text-white text-sm font-medium rounded-lg hover:bg-[#059669] transition-colors duration-200 mb-2"
                >
                    Open List
                </button>
                <p className="text-[10px] text-gray-500 text-center">Last synced: {timeSinceUpdate().toLowerCase()}</p>
            </div>

            {/* Context Menu - Use existing ChannelCard's menu functionality */}
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <ChannelCard
                    channel={channel}
                    onSelect={() => {}} // Handled by card click
                    onDelete={onDelete}
                    onRename={onRename}
                    onDuplicate={onDuplicate}
                    onPin={onPin}
                />
            </div>
        </div>
    );
};

// Quick Action Card for empty space utilization
const QuickActionCard: React.FC<{
    title: string;
    description: string;
    icon: string;
    actions: string[];
}> = ({ title, description, icon, actions }) => {
    return (
        <div className="bg-[#1a1a1a] border border-[#333] rounded-xl p-6 hover:border-[#10b981]/30 transition-colors duration-200">
            <div className="flex items-start gap-4 mb-4">
                <div className="text-2xl">{icon}</div>
                <div>
                    <h3 className="text-base font-semibold text-white mb-1">{title}</h3>
                    <p className="text-sm text-gray-400">{description}</p>
                </div>
            </div>
            <div className="space-y-2">
                {actions.map((action, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-gray-300 hover:text-white transition-colors duration-150 cursor-pointer">
                        <div className="w-1.5 h-1.5 bg-[#10b981] rounded-full"></div>
                        <span>{action}</span>
                    </div>
                ))}
            </div>
        </div>
    );
};



