# 🧪 VirSnapp Manual Testing Checklist

This comprehensive checklist ensures all security measures and functionality work correctly before production deployment.

## 🔒 Security Testing

### Input Validation Tests

#### ✅ Channel Search Input
- [ ] **Valid YouTube Channel URL**: `https://www.youtube.com/c/validchannel`
- [ ] **Valid YouTube Handle**: `@validhandle`
- [ ] **Valid Search Term**: `tech channel`
- [ ] **SQL Injection Attempt**: `'; DROP TABLE channels; --` (should be rejected)
- [ ] **XSS Attempt**: `<script>alert('xss')</script>` (should be rejected)
- [ ] **Empty Input**: `` (should show validation error)
- [ ] **Very Long Input**: `a`.repeat(501) (should be rejected)

#### ✅ Video URL Input
- [ ] **Valid YouTube Video**: `https://www.youtube.com/watch?v=dQw4w9WgXcQ`
- [ ] **Valid Short URL**: `https://youtu.be/dQw4w9WgXcQ`
- [ ] **Invalid URL**: `https://example.com/video` (should be rejected)
- [ ] **Malicious URL**: `javascript:alert('xss')` (should be rejected)
- [ ] **Empty URL**: `` (should show validation error)

#### ✅ Name Fields (Folders, Tags, Channel Lists)
- [ ] **Valid Name**: `My Channel List`
- [ ] **Empty Name**: `` (should be rejected)
- [ ] **Too Long**: `a`.repeat(101) (should be rejected)
- [ ] **SQL Injection**: `'; DROP TABLE folders; --` (should be rejected)
- [ ] **XSS Attempt**: `<img src=x onerror=alert(1)>` (should be rejected)
- [ ] **Special Characters**: `Test & Channel's List!` (should be sanitized)

#### ✅ Notes Field
- [ ] **Valid Notes**: `This is a test note with details`
- [ ] **Empty Notes**: `` (should be allowed)
- [ ] **Long Notes**: `a`.repeat(2000) (should be allowed)
- [ ] **Too Long**: `a`.repeat(2001) (should be rejected)
- [ ] **SQL Injection**: `'; DELETE FROM saved_videos; --` (should be rejected)
- [ ] **HTML Content**: `<b>Bold text</b>` (should be sanitized)

### API Key Security Tests

#### ✅ API Key Format Validation
- [ ] **Valid Key**: `AIzaSyDummyKeyForTesting1234567890123456`
- [ ] **Invalid Prefix**: `BIzaSyDummyKeyForTesting1234567890123456` (should be rejected)
- [ ] **Too Short**: `AIzaShortKey` (should be rejected)
- [ ] **Too Long**: `AIzaSyDummyKeyForTesting1234567890123456789` (should be rejected)
- [ ] **Invalid Characters**: `AIzaSy@#$%^&*()1234567890123456` (should be rejected)
- [ ] **Empty Key**: `` (should be rejected)

#### ✅ API Key Encryption
- [ ] **Add API Key**: Verify key is encrypted in database
- [ ] **View API Key**: Verify key displays correctly (decrypted)
- [ ] **Edit API Key**: Verify encryption persists after edits
- [ ] **Delete API Key**: Verify encrypted data is removed

#### ✅ Usage Quota Validation
- [ ] **Normal Usage**: Verify operations work within quota
- [ ] **High Usage Warning**: Test with usage > 8000 (should show warning)
- [ ] **Quota Exceeded**: Test with usage > 10000 (should block operations)
- [ ] **Usage Tracking**: Verify usage increments after API calls

## 🗄️ Database Security Tests

### Row Level Security (RLS)
Run these in Supabase SQL Editor:

#### ✅ Verify RLS is Enabled
```sql
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND rowsecurity = true;
```
- [ ] **All tables show `rowsecurity = true`**

#### ✅ Test Data Constraints
```sql
-- Test name length constraint (should fail)
INSERT INTO channels (name) VALUES (REPEAT('a', 101));

-- Test API key format constraint (should fail)
INSERT INTO api_keys (name, key) VALUES ('test', 'invalid_key');

-- Test required fields (should fail)
INSERT INTO channels (name) VALUES (NULL);
```
- [ ] **All constraint violations are properly rejected**

#### ✅ Test Input Sanitization
```sql
-- Test automatic sanitization
INSERT INTO channels (name) VALUES ('<script>alert(1)</script>');
SELECT name FROM channels WHERE name LIKE '%script%';
```
- [ ] **Malicious content is sanitized automatically**

## ⚡ Performance Tests

### API Usage Optimization
- [ ] **Single Channel Search**: Monitor quota usage (should be ~101 units)
- [ ] **Multiple Channel Search**: Test with 5+ channels
- [ ] **Video Sync**: Monitor quota for video synchronization
- [ ] **Quota Warnings**: Verify warnings appear at 80% usage

### Application Performance
- [ ] **Initial Load Time**: < 3 seconds on normal connection
- [ ] **Channel List Load**: < 2 seconds with 10+ channels
- [ ] **Video Search**: < 5 seconds for popular channels
- [ ] **Database Operations**: < 1 second for CRUD operations

### Memory Usage
- [ ] **Memory Leaks**: Monitor for increasing memory usage
- [ ] **Large Datasets**: Test with 100+ saved videos
- [ ] **Long Sessions**: Test app running for 30+ minutes

## 🎯 Functional Testing

### Core Features
- [ ] **Add Channel List**: Create new channel list successfully
- [ ] **Search Channels**: Find channels by URL/handle/name
- [ ] **Add Channels to List**: Add multiple channels to existing list
- [ ] **Remove Channels**: Remove channels from list
- [ ] **Rename Channel List**: Update list name
- [ ] **Delete Channel List**: Remove entire list
- [ ] **Duplicate Channel List**: Create copy of existing list
- [ ] **Pin/Unpin Lists**: Toggle pinned status

### Video Management
- [ ] **Save Video**: Save video with folder and tags
- [ ] **View Saved Videos**: Browse videos by folder
- [ ] **Edit Video Notes**: Update video notes
- [ ] **Delete Video**: Remove saved video
- [ ] **Video Search**: Find videos within folders

### Folder & Tag Management
- [ ] **Create Folder**: Add new video folder
- [ ] **Rename Folder**: Update folder name
- [ ] **Delete Folder**: Remove folder (with confirmation)
- [ ] **Create Tag**: Add new video tag
- [ ] **Rename Tag**: Update tag name
- [ ] **Delete Tag**: Remove tag
- [ ] **Tag Videos**: Apply multiple tags to videos

### Settings & Configuration
- [ ] **Add API Key**: Add YouTube API key
- [ ] **Rename API Key**: Update API key name
- [ ] **Delete API Key**: Remove API key
- [ ] **Switch Active Key**: Change active API key
- [ ] **View Usage**: Monitor API quota usage

## 🌐 Cross-Browser Testing

### Desktop Browsers
- [ ] **Chrome**: Latest version
- [ ] **Firefox**: Latest version
- [ ] **Safari**: Latest version (if on Mac)
- [ ] **Edge**: Latest version

### Mobile Browsers
- [ ] **Chrome Mobile**: Android
- [ ] **Safari Mobile**: iOS
- [ ] **Firefox Mobile**: Android

### Responsive Design
- [ ] **Desktop**: 1920x1080 and 1366x768
- [ ] **Tablet**: 768x1024 (portrait and landscape)
- [ ] **Mobile**: 375x667 and 414x896

## 🔍 Security Monitoring Tests

### Security Event Logging
- [ ] **Application Start**: Verify startup event is logged
- [ ] **API Key Added**: Verify security event is logged
- [ ] **Invalid Input**: Verify rejection is logged
- [ ] **Quota Exceeded**: Verify warning is logged
- [ ] **Application Close**: Verify shutdown event is logged

### Health Check
Run in browser console:
```javascript
import { performSecurityHealthCheck } from './lib/securityMonitoring.js';
const healthCheck = await performSecurityHealthCheck();
console.log('Security Status:', healthCheck);
```
- [ ] **Status**: Should be 'healthy' or 'warning'
- [ ] **All Checks**: Environment, API Keys, Database should pass

## 📊 Test Results Documentation

### Test Execution Log
```
Date: ___________
Tester: ___________
Environment: ___________

Security Tests:
- Input Validation: ___/__ passed
- API Key Security: ___/__ passed
- Database Security: ___/__ passed

Performance Tests:
- API Usage: ___/__ passed
- Application Performance: ___/__ passed
- Memory Usage: ___/__ passed

Functional Tests:
- Core Features: ___/__ passed
- Video Management: ___/__ passed
- Settings: ___/__ passed

Cross-Browser Tests:
- Desktop: ___/__ passed
- Mobile: ___/__ passed
- Responsive: ___/__ passed

Overall Score: ___/__ (___%)
```

### Issues Found
```
Issue #1:
- Description: 
- Severity: Critical/High/Medium/Low
- Steps to Reproduce:
- Expected Result:
- Actual Result:
- Status: Open/Fixed/Deferred

Issue #2:
...
```

## ✅ Sign-off Criteria

The application is ready for production when:
- [ ] **100% of Critical security tests pass**
- [ ] **95%+ of all functional tests pass**
- [ ] **No Critical or High severity issues remain**
- [ ] **Performance meets acceptable thresholds**
- [ ] **Cross-browser compatibility verified**
- [ ] **Security monitoring is functional**

**Tester Signature**: _____________________ **Date**: _________

**Security Review**: _____________________ **Date**: _________

**Final Approval**: _____________________ **Date**: _________
