
import React, { useState } from 'react';
import type { VideoDetails } from '../types';
import { formatNumber, formatTimeAgo, formatDuration } from '../lib/utils';
import { LinkIcon, CheckIcon, SavedVideosIcon } from './Icons';

type GridLayout = '2-grid' | '4-grid' | '6-grid';

interface VideoCardProps {
    video: VideoDetails;
    onSaveClick: () => void;
    layout: GridLayout;
}

const cardStyles: Record<GridLayout, {
    thumbnail: string;
    title: string;
    detailsContainer: string;
    channelInfo: string;
    stats: string;
}> = {
    '2-grid': {
        thumbnail: 'rounded-lg',
        title: 'text-base font-semibold leading-snug line-clamp-2',
        detailsContainer: 'flex items-start space-x-3',
        channelInfo: 'flex-shrink-0',
        stats: 'text-sm text-gray-400 mt-1',
    },
    '4-grid': {
        thumbnail: 'rounded-lg',
        title: 'text-sm font-semibold leading-snug line-clamp-2',
        detailsContainer: 'flex items-start space-x-3',
        channelInfo: 'flex-shrink-0',
        stats: 'text-xs text-gray-400 mt-1',
    },
    '6-grid': {
        thumbnail: 'rounded-md',
        title: 'text-xs font-semibold leading-snug line-clamp-2',
        detailsContainer: 'flex',
        channelInfo: 'hidden',
        stats: 'text-xs text-gray-400 mt-0.5',
    },
};

export const VideoCard: React.FC<VideoCardProps> = ({ video, onSaveClick, layout }) => {
    const [isCopied, setIsCopied] = useState(false);
    const videoUrl = `https://www.youtube.com/watch?v=${video.id}`;
    const styles = cardStyles[layout];

    const handleCopyLink = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        navigator.clipboard.writeText(videoUrl);
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
    };

    return (
        <div className="flex flex-col space-y-2 transition-all duration-300">
            <div className={`relative group aspect-video overflow-hidden ${styles.thumbnail}`}>
                <a 
                    href={videoUrl} 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="absolute inset-0 z-0" 
                    aria-label={`Watch video: ${video.title}`}
                ></a>
                <img 
                    src={video.thumbnailUrl} 
                    alt={video.title} 
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" 
                />
                
                <div className="absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1.5 py-0.5 rounded z-10 pointer-events-none group-hover:opacity-0 transition-opacity duration-300">
                    {formatDuration(video.duration)}
                </div>
                
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20 flex flex-row justify-between items-end p-2">
                    <div className="text-white text-xs font-bold bg-black/60 backdrop-blur-sm px-2 py-1 rounded-full pointer-events-none">
                        {formatDuration(video.duration)}
                    </div>
                    
                    <div className="flex items-center gap-2">
                         <button 
                            onClick={handleCopyLink} 
                            className={`h-8 flex items-center justify-center bg-black/60 backdrop-blur-sm rounded-full text-white text-xs font-semibold transition-all duration-200 transform hover:scale-105 ${isCopied ? 'px-3 bg-accent/20' : 'px-3 hover:bg-gray-700'}`}
                            aria-label="Copy link"
                            title="Copy link"
                        >
                            {isCopied ? (
                                <>
                                    <CheckIcon className="w-4 h-4 text-accent mr-1.5" />
                                    <span className="text-accent">Copied!</span>
                                </>
                            ) : (
                                <>
                                    <LinkIcon className="w-4 h-4 mr-1.5" />
                                    <span>Copy</span>
                                </>
                            )}
                        </button>
                        <button 
                            onClick={(e) => { e.preventDefault(); e.stopPropagation(); onSaveClick(); }} 
                            className="w-8 h-8 flex items-center justify-center bg-black/60 backdrop-blur-sm hover:bg-gray-700 rounded-full text-white transition-all duration-200 transform hover:scale-105"
                            aria-label="Save video"
                            title="Save video"
                        >
                            <SavedVideosIcon className="w-4 h-4"/>
                        </button>
                    </div>
                </div>
            </div>
            <div className={styles.detailsContainer}>
                <a href={`https://www.youtube.com/channel/${video.channelId}`} target="_blank" rel="noopener noreferrer" className={styles.channelInfo}>
                    <img src={video.channelAvatar} alt={video.channelTitle} className="w-9 h-9 rounded-full" />
                </a>
                <div className="flex-1 min-w-0">
                    <a href={videoUrl} target="_blank" rel="noopener noreferrer" title={video.title}>
                        <h3 className={`${styles.title} text-white`}>{video.title}</h3>
                    </a>
                    <div className={styles.stats}>
                         <a href={`https://www.youtube.com/channel/${video.channelId}`} target="_blank" rel="noopener noreferrer" className={`hover:text-white transition-colors ${layout === '6-grid' ? 'hidden' : ''}`}>
                            <p className="truncate" title={video.channelTitle}>{video.channelTitle}</p>
                        </a>
                        <p className={`truncate ${layout === '6-grid' ? 'hidden' : ''}`}>{formatNumber(video.subscriberCount)} subscribers</p>
                        <p className="truncate">{formatNumber(video.viewCount)} views &bull; {formatTimeAgo(video.publishedAt)}</p>
                    </div>
                </div>
            </div>
        </div>
    );
};
