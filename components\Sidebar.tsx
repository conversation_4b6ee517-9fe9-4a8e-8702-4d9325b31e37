
import React from 'react';
import { VelioIcon, TrackedChannelsIcon, SavedVideosIcon, SettingsIcon, CloseIcon } from './Icons';

type Page = 'tracked' | 'saved' | 'settings';

interface SidebarProps {
    currentPage: Page;
    onNavigate: (page: Page) => void;
    isOpen: boolean;
    setIsOpen: (isOpen: boolean) => void;
}

const NavItem: React.FC<{
    page: Page;
    currentPage: Page;
    onNavigate: (page: Page) => void;
    icon: React.ReactNode;
    label: string;
}> = ({ page, currentPage, onNavigate, icon, label }) => {
    const isActive = currentPage === page;
    const classes = `flex items-center p-3 rounded-lg transition-colors duration-200 cursor-pointer ${
        isActive ? 'text-accent font-semibold bg-accent/10' : 'text-gray-400 hover:bg-gray-700/50'
    }`;
    
    return (
        <a href="#" onClick={(e) => { e.preventDefault(); onNavigate(page); }} className={classes}>
            <div className={`mr-4 ${isActive ? 'text-accent' : ''}`}>{icon}</div>
            <span>{label}</span>
        </a>
    );
};

export const Sidebar: React.FC<SidebarProps> = ({ currentPage, onNavigate, isOpen, setIsOpen }) => {
    return (
        <>
            <div 
                className={`fixed inset-0 bg-black/50 z-30 transition-opacity lg:hidden ${isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
                onClick={() => setIsOpen(false)}
            ></div>
            <aside className={`fixed top-0 left-0 h-full bg-[#121212] border-r border-dark-border w-64 md:w-72 flex-shrink-0 flex flex-col transition-transform duration-300 ease-in-out z-40 ${isOpen ? 'translate-x-0' : '-translate-x-full'} lg:relative lg:translate-x-0`}>
                <div className="p-4 border-b border-dark-border flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                        <VelioIcon className="w-7 h-7 text-accent" />
                        <div className="flex items-center">
                            <h1 className="text-xl font-bold text-white tracking-wider">VELIO</h1>
                            <span className="ml-2 text-xs font-semibold bg-accent/20 text-accent px-2 py-0.5 rounded-full">Beta</span>
                        </div>
                    </div>
                     <button onClick={() => setIsOpen(false)} className="lg:hidden p-1 rounded-md hover:bg-gray-700">
                        <CloseIcon className="w-6 h-6" />
                    </button>
                </div>

                <nav className="flex-1 overflow-y-auto p-4 space-y-2">
                    <NavItem 
                        page="tracked"
                        currentPage={currentPage}
                        onNavigate={onNavigate}
                        icon={<TrackedChannelsIcon className="w-6 h-6" />}
                        label="Tracked channels"
                    />
                    <NavItem 
                        page="saved"
                        currentPage={currentPage}
                        onNavigate={onNavigate}
                        icon={<SavedVideosIcon className="w-6 h-6" />}
                        label="Saved videos"
                    />
                     <NavItem 
                        page="settings"
                        currentPage={currentPage}
                        onNavigate={onNavigate}
                        icon={<SettingsIcon className="w-6 h-6" />}
                        label="Setting"
                    />
                </nav>
            </aside>
        </>
    );
};