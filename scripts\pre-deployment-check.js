#!/usr/bin/env node

/**
 * Pre-Deployment Security and Readiness Check
 * Comprehensive validation before production deployment
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function checkCritical(condition, successMsg, failMsg) {
  if (condition) {
    log(`✅ ${successMsg}`, colors.green);
    return true;
  } else {
    log(`🚨 CRITICAL: ${failMsg}`, colors.red);
    return false;
  }
}

function checkWarning(condition, successMsg, warningMsg) {
  if (condition) {
    log(`✅ ${successMsg}`, colors.green);
    return true;
  } else {
    log(`⚠️  WARNING: ${warningMsg}`, colors.yellow);
    return false;
  }
}

function checkInfo(condition, successMsg, infoMsg) {
  if (condition) {
    log(`✅ ${successMsg}`, colors.green);
  } else {
    log(`ℹ️  INFO: ${infoMsg}`, colors.blue);
  }
  return condition;
}

async function checkEnvironmentReadiness() {
  log('\n🌍 ENVIRONMENT READINESS CHECK', colors.bold);
  log('=' .repeat(50), colors.blue);
  
  let criticalIssues = 0;
  let warnings = 0;
  
  // Check for production environment variables
  const hasEnvExample = fs.existsSync('.env.example');
  if (!checkCritical(hasEnvExample, 'Environment template exists', 'Missing .env.example file')) {
    criticalIssues++;
  }
  
  // Check that .env.local is not in git
  const gitignoreContent = fs.existsSync('.gitignore') ? fs.readFileSync('.gitignore', 'utf8') : '';
  const envProtected = gitignoreContent.includes('.env.local') || gitignoreContent.includes('.env');
  if (!checkCritical(envProtected, 'Environment files protected in .gitignore', '.env files not properly gitignored')) {
    criticalIssues++;
  }
  
  // Check for hardcoded secrets in source code
  const supabaseContent = fs.readFileSync('lib/supabase.ts', 'utf8');
  const noHardcodedSecrets = !supabaseContent.includes('https://lofjviejdjqlrapaoyee.supabase.co') &&
                            !supabaseContent.includes('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9');
  if (!checkCritical(noHardcodedSecrets, 'No hardcoded secrets in source code', 'Hardcoded secrets found in source code')) {
    criticalIssues++;
  }
  
  // Check environment variable usage (either direct or through getEnvironmentConfig)
  const usesEnvVars = (supabaseContent.includes('import.meta.env.VITE_SUPABASE_URL') &&
                      supabaseContent.includes('import.meta.env.VITE_SUPABASE_ANON_KEY')) ||
                     (supabaseContent.includes('getEnvironmentConfig') &&
                      supabaseContent.includes('config.supabaseUrl') &&
                      supabaseContent.includes('config.supabaseAnonKey'));
  if (!checkCritical(usesEnvVars, 'Environment variables properly configured', 'Environment variables not properly used')) {
    criticalIssues++;
  }
  
  return { criticalIssues, warnings };
}

async function checkSecurityImplementation() {
  log('\n🔒 SECURITY IMPLEMENTATION CHECK', colors.bold);
  log('=' .repeat(50), colors.blue);
  
  let criticalIssues = 0;
  let warnings = 0;
  
  // Check security files exist
  const securityFiles = [
    'lib/inputValidation.ts',
    'lib/apiKeySecurity.ts',
    'lib/securityMonitoring.ts',
    'database/security-policies.sql',
    'SECURITY.md'
  ];
  
  for (const file of securityFiles) {
    if (!checkCritical(fs.existsSync(file), `Security file exists: ${file}`, `Missing security file: ${file}`)) {
      criticalIssues++;
    }
  }
  
  // Check input validation implementation
  const youtubeService = fs.readFileSync('services/youtubeService.ts', 'utf8');
  const hasInputValidation = youtubeService.includes('validateYouTubeChannelQuery') &&
                            youtubeService.includes('validateYouTubeVideoUrl');
  if (!checkCritical(hasInputValidation, 'Input validation implemented in YouTube service', 'Missing input validation in YouTube service')) {
    criticalIssues++;
  }
  
  // Check API key encryption
  const apiKeyService = fs.readFileSync('services/apiKeyService.ts', 'utf8');
  const hasEncryption = apiKeyService.includes('encryptApiKey') && apiKeyService.includes('decryptApiKey');
  if (!checkCritical(hasEncryption, 'API key encryption implemented', 'Missing API key encryption')) {
    criticalIssues++;
  }
  
  // Check security monitoring
  const appContent = fs.readFileSync('App.tsx', 'utf8');
  const hasMonitoring = appContent.includes('initializeSecurityMonitoring');
  if (!checkWarning(hasMonitoring, 'Security monitoring initialized', 'Security monitoring not initialized')) {
    warnings++;
  }
  
  return { criticalIssues, warnings };
}

async function checkDatabaseReadiness() {
  log('\n🗄️ DATABASE READINESS CHECK', colors.bold);
  log('=' .repeat(50), colors.blue);
  
  let criticalIssues = 0;
  let warnings = 0;
  
  // Check database security policies exist
  const policiesExist = fs.existsSync('database/security-policies.sql');
  if (!checkCritical(policiesExist, 'Database security policies file exists', 'Missing database security policies')) {
    criticalIssues++;
  }
  
  if (policiesExist) {
    const policiesContent = fs.readFileSync('database/security-policies.sql', 'utf8');
    
    // Check for RLS policies
    const hasRLS = policiesContent.includes('ENABLE ROW LEVEL SECURITY');
    if (!checkCritical(hasRLS, 'RLS policies defined', 'Missing RLS policies')) {
      criticalIssues++;
    }
    
    // Check for constraints
    const hasConstraints = policiesContent.includes('ADD CONSTRAINT');
    if (!checkWarning(hasConstraints, 'Database constraints defined', 'Missing database constraints')) {
      warnings++;
    }
    
    // Check for validation functions
    const hasValidation = policiesContent.includes('validate_youtube_api_key') &&
                         policiesContent.includes('sanitize_text');
    if (!checkWarning(hasValidation, 'Database validation functions defined', 'Missing database validation functions')) {
      warnings++;
    }
  }
  
  return { criticalIssues, warnings };
}

async function checkBuildReadiness() {
  log('\n🏗️ BUILD READINESS CHECK', colors.bold);
  log('=' .repeat(50), colors.blue);
  
  let criticalIssues = 0;
  let warnings = 0;
  
  // Check package.json
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // Check build script exists
  const hasBuildScript = packageJson.scripts && packageJson.scripts.build;
  if (!checkCritical(hasBuildScript, 'Build script configured', 'Missing build script in package.json')) {
    criticalIssues++;
  }
  
  // Check security validation in prebuild
  const hasSecurityValidation = packageJson.scripts && packageJson.scripts.prebuild &&
                               packageJson.scripts.prebuild.includes('security:validate');
  if (!checkWarning(hasSecurityValidation, 'Security validation in prebuild', 'Security validation not in prebuild script')) {
    warnings++;
  }
  
  // Check TypeScript configuration
  const hasTsConfig = fs.existsSync('tsconfig.json');
  if (!checkCritical(hasTsConfig, 'TypeScript configuration exists', 'Missing tsconfig.json')) {
    criticalIssues++;
  }
  
  // Check Vite configuration
  const hasViteConfig = fs.existsSync('vite.config.ts');
  if (!checkCritical(hasViteConfig, 'Vite configuration exists', 'Missing vite.config.ts')) {
    criticalIssues++;
  }
  
  return { criticalIssues, warnings };
}

async function generateDeploymentInstructions() {
  log('\n📋 DEPLOYMENT INSTRUCTIONS', colors.bold);
  log('=' .repeat(50), colors.blue);
  
  log('\n1. ENVIRONMENT SETUP:', colors.cyan);
  log('   • Create production environment variables:');
  log('     - VITE_SUPABASE_URL=your_production_supabase_url');
  log('     - VITE_SUPABASE_ANON_KEY=your_production_supabase_anon_key');
  log('   • Ensure .env.local is NOT deployed to production');
  log('   • Set NODE_ENV=production');
  
  log('\n2. DATABASE SETUP:', colors.cyan);
  log('   • Apply security policies:');
  log('     - Go to Supabase Dashboard → SQL Editor');
  log('     - Run contents of database/security-policies.sql');
  log('     - Verify RLS is enabled on all tables');
  
  log('\n3. BUILD PROCESS:', colors.cyan);
  log('   • Run security validation: npm run security:validate');
  log('   • Build application: npm run build');
  log('   • Test build locally: npm run preview');
  
  log('\n4. DEPLOYMENT PLATFORMS:', colors.cyan);
  log('   • Vercel: vercel --prod');
  log('   • Netlify: netlify deploy --prod');
  log('   • Custom: Upload dist/ folder to your hosting');
  
  log('\n5. POST-DEPLOYMENT:', colors.cyan);
  log('   • Verify environment variables are set');
  log('   • Test application functionality');
  log('   • Run security health check');
  log('   • Monitor security logs');
}

async function runPreDeploymentCheck() {
  log('🚀 VIRSNAPP PRE-DEPLOYMENT CHECK', colors.bold);
  log('=' .repeat(60), colors.magenta);
  
  // Change to project root
  process.chdir(path.join(__dirname, '..'));
  
  const results = {
    environment: await checkEnvironmentReadiness(),
    security: await checkSecurityImplementation(),
    database: await checkDatabaseReadiness(),
    build: await checkBuildReadiness()
  };
  
  const totalCritical = Object.values(results).reduce((sum, r) => sum + r.criticalIssues, 0);
  const totalWarnings = Object.values(results).reduce((sum, r) => sum + r.warnings, 0);
  
  log('\n' + '=' .repeat(60), colors.bold);
  log('📊 DEPLOYMENT READINESS SUMMARY', colors.bold);
  log('=' .repeat(60), colors.bold);
  
  if (totalCritical === 0) {
    log('🎉 READY FOR DEPLOYMENT!', colors.green);
    log(`✅ All critical checks passed`, colors.green);
    if (totalWarnings > 0) {
      log(`⚠️  ${totalWarnings} warnings (recommended to fix)`, colors.yellow);
    }
  } else {
    log('🚨 NOT READY FOR DEPLOYMENT', colors.red);
    log(`❌ ${totalCritical} critical issues must be fixed`, colors.red);
    log(`⚠️  ${totalWarnings} warnings`, colors.yellow);
  }
  
  await generateDeploymentInstructions();
  
  log('\n' + '=' .repeat(60), colors.bold);
  
  return totalCritical === 0;
}

// Main execution
const isMainModule = import.meta.url === `file://${process.argv[1]}` || 
                     import.meta.url.endsWith(process.argv[1]) ||
                     process.argv[1].includes('pre-deployment-check.js');

if (isMainModule) {
  const isReady = await runPreDeploymentCheck();
  process.exit(isReady ? 0 : 1);
}

export { runPreDeploymentCheck };
