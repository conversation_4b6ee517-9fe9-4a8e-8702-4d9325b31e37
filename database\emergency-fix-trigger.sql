-- EMERGENCY FIX: Resolve "record 'new' has no field 'notes'" error
-- Run this script immediately in your Supabase SQL editor to fix the folder creation issue

-- 1. Drop existing problematic trigger function
DROP FUNCTION IF EXISTS sanitize_before_insert_update() CASCADE;

-- 2. Create a safer sanitization function that checks for column existence
CREATE OR REPLACE FUNCTION sanitize_before_insert_update()
RETURNS TRIGGER AS $$
DECLARE
    column_exists BOOLEAN;
BEGIN
    -- Sanitize name fields for tables that have a name column
    IF TG_TABLE_NAME IN ('channels', 'folders', 'tags', 'api_keys') THEN
        NEW.name = sanitize_text(NEW.name);
    END IF;
    
    -- Only sanitize notes field if the column actually exists in the table
    IF TG_TABLE_NAME = 'saved_videos' THEN
        -- Check if notes column exists in saved_videos table
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'saved_videos' 
            AND column_name = 'notes'
            AND table_schema = 'public'
        ) INTO column_exists;
        
        IF column_exists AND NEW.notes IS NOT NULL THEN
            NEW.notes = sanitize_text(NEW.notes);
        END IF;
    END IF;
    
    -- Only sanitize notes field for api_keys if the column exists
    IF TG_TABLE_NAME = 'api_keys' THEN
        -- Check if notes column exists in api_keys table
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'api_keys' 
            AND column_name = 'notes'
            AND table_schema = 'public'
        ) INTO column_exists;
        
        IF column_exists AND NEW.notes IS NOT NULL THEN
            NEW.notes = sanitize_text(NEW.notes);
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 3. Recreate all the sanitization triggers
CREATE TRIGGER sanitize_channels_trigger
    BEFORE INSERT OR UPDATE ON channels
    FOR EACH ROW EXECUTE FUNCTION sanitize_before_insert_update();

CREATE TRIGGER sanitize_folders_trigger
    BEFORE INSERT OR UPDATE ON folders
    FOR EACH ROW EXECUTE FUNCTION sanitize_before_insert_update();

CREATE TRIGGER sanitize_tags_trigger
    BEFORE INSERT OR UPDATE ON tags
    FOR EACH ROW EXECUTE FUNCTION sanitize_before_insert_update();

CREATE TRIGGER sanitize_api_keys_trigger
    BEFORE INSERT OR UPDATE ON api_keys
    FOR EACH ROW EXECUTE FUNCTION sanitize_before_insert_update();

CREATE TRIGGER sanitize_saved_videos_trigger
    BEFORE INSERT OR UPDATE ON saved_videos
    FOR EACH ROW EXECUTE FUNCTION sanitize_before_insert_update();

-- 4. Test the fix
DO $$
BEGIN
    RAISE NOTICE '✅ Emergency fix applied successfully!';
    RAISE NOTICE '   - Sanitization function: UPDATED with column existence checks';
    RAISE NOTICE '   - All triggers: RECREATED';
    RAISE NOTICE '   - Folder creation should now work without errors';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 You can now try creating a folder again in the application.';
END $$;
