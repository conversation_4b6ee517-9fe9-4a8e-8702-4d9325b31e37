

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type { Channel, Folder, Tag, ApiKey, Statistic, SavedVideo } from '../types';
import { getEnvironmentConfig } from './env';

// --- S<PERSON><PERSON>ASE CONFIGURATION ---
// Environment variables are loaded automatically by Vite
// Make sure to set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in your .env.local file

let supabaseUrl: string;
let supabaseAnonKey: string;

try {
  const config = getEnvironmentConfig();
  supabaseUrl = config.supabaseUrl;
  supabaseAnonKey = config.supabaseAnonKey;
} catch (error) {
  console.error('CRITICAL: Failed to load environment configuration:', error);
  // Fallback to direct access for development
  supabaseUrl = (import.meta as any).env?.VITE_SUPABASE_URL || '';
  supabaseAnonKey = (import.meta as any).env?.VITE_SUPABASE_ANON_KEY || '';
}


interface Database {
  public: {
    Tables: {
      channels: {
        Row: Channel;
        Insert: Omit<Channel, 'id' | 'createdAt' | 'updatedAt'>;
        Update: Partial<Channel>;
      };
      statistics: {
        Row: Statistic & {channel_id: string};
        Insert: Omit<Statistic, 'id' | 'recordedAt'> & {channel_id: string};
        Update: Partial<Statistic>;
      };
      folders: {
        Row: Folder;
        Insert: Omit<Folder, 'id'>;
        Update: Partial<Folder>;
      };
      tags: {
        Row: Tag;
        Insert: Omit<Tag, 'id'>;
        Update: Partial<Tag>;
      };
      api_keys: {
        Row: ApiKey;
        Insert: Omit<ApiKey, 'id' | 'usage' | 'created_at'>;
        Update: Partial<ApiKey>;
      };
      saved_videos: {
        Row: SavedVideo;
        Insert: Omit<SavedVideo, 'id' | 'created_at'>;
        Update: Partial<SavedVideo>;
      };
      saved_video_tags: {
          Row: { saved_video_id: string; tag_id: string };
          Insert: { saved_video_id: string; tag_id: string };
          Update: Partial<{ saved_video_id: string; tag_id: string }>;
      }
    };
  };
}

export const isSupabaseConfigured = Boolean(supabaseUrl && supabaseAnonKey &&
  supabaseUrl !== 'your_supabase_project_url_here' &&
  supabaseAnonKey !== 'your_supabase_anon_key_here');

let supabase: SupabaseClient<Database>;

if (isSupabaseConfigured) {
    supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);
} else {
    console.error("CRITICAL: Supabase credentials are not set. The application will not function. Please update `lib/supabase.ts` with your project's URL and anon key.");
    
    // Create a mock client to prevent the app from crashing on module load.
    // The UI will show a configuration notice, so these methods should not be called.
    const handler = {
        get: function(_target: any, prop: any) {
            // Return a function that throws an error, to make it clear that Supabase is not configured.
            return () => {
                throw new Error(`Supabase is not configured. Called method: ${prop.toString()}`);
            };
        }
    };
    supabase = new Proxy({}, handler) as SupabaseClient<Database>;
}


export { supabase };