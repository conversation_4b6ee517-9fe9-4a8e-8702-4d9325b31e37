

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import type { Channel, Folder, Tag, ApiKey, Statistic, SavedVideo } from '../types';

// --- IMPORTANT: SUPABASE SETUP ---
// 1. Create a new project at https://supabase.com/
// 2. Go to your project's "API" settings.
// 3. Find your "Project URL" and "anon" "public" key.
// 4. Paste them into the variables below.
const supabaseUrl: string = 'https://lofjviejdjqlrapaoyee.supabase.co';
const supabaseAnonKey: string = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxvZmp2aWVqZGpxbHJhcGFveWVlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2MTM5MjQsImV4cCI6MjA2NjE4OTkyNH0.BIFI2zB2SE9qNXTOTDStWIOA1YPUYzVva0k4LX_fn_U';


interface Database {
  public: {
    Tables: {
      channels: {
        Row: Channel;
        Insert: Omit<Channel, 'id' | 'createdAt' | 'updatedAt'>;
        Update: Partial<Channel>;
      };
      statistics: {
        Row: Statistic & {channel_id: string};
        Insert: Omit<Statistic, 'id' | 'recordedAt'> & {channel_id: string};
        Update: Partial<Statistic>;
      };
      folders: {
        Row: Folder;
        Insert: Omit<Folder, 'id'>;
        Update: Partial<Folder>;
      };
      tags: {
        Row: Tag;
        Insert: Omit<Tag, 'id'>;
        Update: Partial<Tag>;
      };
      api_keys: {
        Row: ApiKey;
        Insert: Omit<ApiKey, 'id' | 'usage'>;
        Update: Partial<ApiKey>;
      };
      saved_videos: {
        Row: SavedVideo;
        Insert: Omit<SavedVideo, 'id' | 'created_at'>;
        Update: Partial<SavedVideo>;
      };
      saved_video_tags: {
          Row: { saved_video_id: string; tag_id: string };
          Insert: { saved_video_id: string; tag_id: string };
          Update: Partial<{ saved_video_id: string; tag_id: string }>;
      }
    };
  };
}

export const isSupabaseConfigured = supabaseUrl !== 'YOUR_SUPABASE_URL' && supabaseAnonKey !== 'YOUR_SUPABASE_ANON_KEY';

let supabase: SupabaseClient<Database>;

if (isSupabaseConfigured) {
    supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);
} else {
    console.error("CRITICAL: Supabase credentials are not set. The application will not function. Please update `lib/supabase.ts` with your project's URL and anon key.");
    
    // Create a mock client to prevent the app from crashing on module load.
    // The UI will show a configuration notice, so these methods should not be called.
    const handler = {
        get: function(target: any, prop: any) {
            // Return a function that throws an error, to make it clear that Supabase is not configured.
            return () => {
                throw new Error(`Supabase is not configured. Called method: ${prop.toString()}`);
            };
        }
    };
    supabase = new Proxy({}, handler) as SupabaseClient<Database>;
}


export { supabase };