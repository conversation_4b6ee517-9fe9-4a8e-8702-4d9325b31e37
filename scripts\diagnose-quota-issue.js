#!/usr/bin/env node

/**
 * Quota System Diagnostic Script
 * Diagnoses and fixes the database function structure mismatch error
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`);
}

async function connectToDatabase() {
    try {
        const supabaseUrl = process.env.VITE_SUPABASE_URL;
        const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
        
        if (!supabaseUrl || !supabaseKey) {
            log('❌ Supabase environment variables not set', colors.red);
            log('   Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY', colors.yellow);
            return null;
        }
        
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        // Test connection
        const { data, error } = await supabase.from('api_keys').select('count').limit(1);
        
        if (error) {
            log(`❌ Database connection failed: ${error.message}`, colors.red);
            return null;
        }
        
        log('✅ Database connection successful', colors.green);
        return supabase;
    } catch (error) {
        log(`❌ Connection error: ${error.message}`, colors.red);
        return null;
    }
}

async function diagnoseQuotaFunction(supabase) {
    log('\n🔍 DIAGNOSING QUOTA FUNCTION ISSUE', colors.bright);
    log('=' .repeat(50), colors.blue);
    
    try {
        // First, check if we have any API keys to test with
        const { data: apiKeys, error: keysError } = await supabase
            .from('api_keys')
            .select('id, name, usage, quota_limit, last_reset_at')
            .limit(1);
        
        if (keysError) {
            log(`❌ Cannot access api_keys table: ${keysError.message}`, colors.red);
            return false;
        }
        
        if (!apiKeys || apiKeys.length === 0) {
            log('⚠️ No API keys found in database', colors.yellow);
            log('   Creating a test API key for diagnosis...', colors.cyan);
            
            // Create a test API key
            const { data: newKey, error: createError } = await supabase
                .from('api_keys')
                .insert({
                    name: 'Test Key for Diagnosis',
                    key: 'test_encrypted_key_for_diagnosis',
                    usage: 100,
                    quota_limit: 10000,
                    last_reset_at: new Date().toISOString()
                })
                .select()
                .single();
            
            if (createError) {
                log(`❌ Cannot create test API key: ${createError.message}`, colors.red);
                return false;
            }
            
            apiKeys.push(newKey);
            log(`✅ Created test API key: ${newKey.id}`, colors.green);
        }
        
        const testKey = apiKeys[0];
        log(`🔧 Testing with API key: ${testKey.id}`, colors.cyan);
        log(`   Name: ${testKey.name}`, colors.cyan);
        log(`   Current usage: ${testKey.usage}`, colors.cyan);
        
        // Test the get_quota_status function
        log('\n📊 Testing get_quota_status function...', colors.blue);
        
        try {
            const { data: quotaData, error: quotaError } = await supabase
                .rpc('get_quota_status', { api_key_id: testKey.id });
            
            if (quotaError) {
                log(`❌ Function call failed: ${quotaError.message}`, colors.red);
                log(`   Error details: ${JSON.stringify(quotaError, null, 2)}`, colors.yellow);
                
                // Check if it's a structure mismatch
                if (quotaError.message.includes('structure of query does not match function result type')) {
                    log('\n🎯 IDENTIFIED THE ISSUE!', colors.bright);
                    log('   The function return type structure doesn\'t match what Supabase expects', colors.yellow);
                    log('   This is likely due to column type mismatches or naming issues', colors.yellow);
                    return 'structure_mismatch';
                }
                
                return false;
            }
            
            if (!quotaData || quotaData.length === 0) {
                log('❌ Function returned no data', colors.red);
                return false;
            }
            
            const quota = quotaData[0];
            log('✅ Function call successful!', colors.green);
            log(`   Result structure:`, colors.cyan);
            log(`   - current_usage: ${quota.current_usage} (${typeof quota.current_usage})`, colors.cyan);
            log(`   - quota_limit: ${quota.quota_limit} (${typeof quota.quota_limit})`, colors.cyan);
            log(`   - remaining_quota: ${quota.remaining_quota} (${typeof quota.remaining_quota})`, colors.cyan);
            log(`   - usage_percentage: ${quota.usage_percentage} (${typeof quota.usage_percentage})`, colors.cyan);
            log(`   - last_reset_at: ${quota.last_reset_at} (${typeof quota.last_reset_at})`, colors.cyan);
            log(`   - next_reset_at: ${quota.next_reset_at} (${typeof quota.next_reset_at})`, colors.cyan);
            log(`   - needs_reset: ${quota.needs_reset} (${typeof quota.needs_reset})`, colors.cyan);
            
            return true;
            
        } catch (error) {
            log(`❌ Function test error: ${error.message}`, colors.red);
            return false;
        }
        
    } catch (error) {
        log(`❌ Diagnosis error: ${error.message}`, colors.red);
        return false;
    }
}

async function applyFix(supabase) {
    log('\n🔧 APPLYING QUOTA FUNCTION FIX', colors.bright);
    log('=' .repeat(50), colors.blue);
    
    try {
        // Read the fix script
        const fixScript = fs.readFileSync('database/fix-quota-function.sql', 'utf8');
        
        log('📄 Executing fix script...', colors.cyan);
        
        // Execute the fix script
        const { data, error } = await supabase.rpc('exec_sql', { sql: fixScript })
            .catch(async () => {
                // If exec_sql doesn't exist, try executing parts manually
                log('⚠️ exec_sql function not available, applying fix manually...', colors.yellow);
                
                // Drop and recreate the function
                const dropResult = await supabase.rpc('exec', { 
                    sql: 'DROP FUNCTION IF EXISTS get_quota_status(UUID);' 
                });
                
                // This is a simplified approach - in practice, you'd need to execute the SQL
                // through the Supabase dashboard or a proper SQL client
                throw new Error('Manual SQL execution required');
            });
        
        if (error) {
            log(`❌ Fix script execution failed: ${error.message}`, colors.red);
            log('\n📋 MANUAL FIX REQUIRED', colors.yellow);
            log('Please execute the following script in your Supabase SQL Editor:', colors.yellow);
            log('   database/fix-quota-function.sql', colors.cyan);
            return false;
        }
        
        log('✅ Fix script executed successfully', colors.green);
        return true;
        
    } catch (error) {
        log(`❌ Fix application error: ${error.message}`, colors.red);
        log('\n📋 MANUAL FIX REQUIRED', colors.yellow);
        log('Please execute the following script in your Supabase SQL Editor:', colors.yellow);
        log('   database/fix-quota-function.sql', colors.cyan);
        return false;
    }
}

async function verifyFix(supabase) {
    log('\n✅ VERIFYING FIX', colors.bright);
    log('=' .repeat(50), colors.blue);
    
    // Wait a moment for the function to be updated
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const result = await diagnoseQuotaFunction(supabase);
    
    if (result === true) {
        log('\n🎉 FIX SUCCESSFUL!', colors.green);
        log('✅ The quota function is now working correctly', colors.green);
        log('✅ Your application should no longer show structure mismatch errors', colors.green);
        return true;
    } else {
        log('\n❌ FIX VERIFICATION FAILED', colors.red);
        log('The issue may require manual intervention', colors.yellow);
        return false;
    }
}

async function cleanupTestData(supabase) {
    log('\n🧹 CLEANING UP TEST DATA', colors.bright);
    log('=' .repeat(50), colors.blue);
    
    try {
        // Remove test API key if it was created
        const { error } = await supabase
            .from('api_keys')
            .delete()
            .eq('name', 'Test Key for Diagnosis');
        
        if (error) {
            log(`⚠️ Could not clean up test data: ${error.message}`, colors.yellow);
        } else {
            log('✅ Test data cleaned up', colors.green);
        }
    } catch (error) {
        log(`⚠️ Cleanup error: ${error.message}`, colors.yellow);
    }
}

async function main() {
    log('🔍 QUOTA SYSTEM DIAGNOSTIC TOOL', colors.bright);
    log('=' .repeat(60), colors.blue);
    log('Diagnosing and fixing database function structure mismatch\n', colors.cyan);
    
    // Connect to database
    const supabase = await connectToDatabase();
    if (!supabase) {
        log('\n❌ Cannot proceed without database connection', colors.red);
        process.exit(1);
    }
    
    // Diagnose the issue
    const diagnosisResult = await diagnoseQuotaFunction(supabase);
    
    if (diagnosisResult === true) {
        log('\n✅ NO ISSUES FOUND', colors.green);
        log('The quota function is working correctly', colors.green);
        await cleanupTestData(supabase);
        return;
    }
    
    if (diagnosisResult === 'structure_mismatch') {
        log('\n🔧 STRUCTURE MISMATCH CONFIRMED', colors.yellow);
        log('Attempting to apply fix...', colors.cyan);
        
        const fixApplied = await applyFix(supabase);
        
        if (fixApplied) {
            await verifyFix(supabase);
        } else {
            log('\n📋 MANUAL STEPS REQUIRED:', colors.yellow);
            log('1. Go to your Supabase project dashboard', colors.cyan);
            log('2. Navigate to SQL Editor', colors.cyan);
            log('3. Execute the script: database/fix-quota-function.sql', colors.cyan);
            log('4. Run this diagnostic tool again to verify the fix', colors.cyan);
        }
    } else {
        log('\n❌ DIAGNOSIS FAILED', colors.red);
        log('Unable to identify the specific issue', colors.yellow);
        log('Please check your database configuration and try again', colors.yellow);
    }
    
    await cleanupTestData(supabase);
    
    log('\n📚 For more information, see:', colors.cyan);
    log('   docs/quota-management-system.md', colors.cyan);
}

// Run the diagnostic
if (require.main === module) {
    main().catch(error => {
        log(`\n💥 Diagnostic tool crashed: ${error.message}`, colors.red);
        process.exit(1);
    });
}

module.exports = { main };
