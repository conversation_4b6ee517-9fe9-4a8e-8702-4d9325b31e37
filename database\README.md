# Database Security Configuration

This directory contains SQL scripts and documentation for securing your Supabase database.

## 🔒 Security Policies Applied

### Row Level Security (RLS)
All tables have RLS enabled with appropriate policies:

- **channels**: Controls access to channel lists
- **statistics**: Controls access to channel statistics
- **folders**: Controls access to saved video folders
- **tags**: Controls access to video tags
- **api_keys**: Controls access to YouTube API keys (most sensitive)
- **saved_videos**: Controls access to saved videos
- **saved_video_tags**: Controls access to video-tag relationships
- **videos**: Controls access to cached YouTube video data

### Data Integrity Constraints

1. **Length Limits**:
   - Names: 100 characters max
   - Notes: 2000 characters max
   - API usage: Reasonable limits applied

2. **Format Validation**:
   - YouTube API keys must match correct format
   - Required fields cannot be null

3. **Automatic Sanitization**:
   - Database triggers sanitize input before storage
   - Control characters are removed
   - Text length is enforced

## 🚀 How to Apply These Policies

### Step 1: Access Supabase SQL Editor
1. Go to your Supabase project dashboard
2. Navigate to "SQL Editor" in the sidebar
3. Create a new query

### Step 2: Run Security Policies
Copy and paste the contents of `security-policies.sql` into the SQL editor and execute.

### Step 2.5: Apply Database Migrations (if needed)
If you encounter the error "record 'new' has no field 'notes'" when using API keys:
1. Copy and paste the contents of `migration-add-api-keys-notes.sql` into the SQL editor
2. Execute the migration script
3. The script will safely add the missing `notes` field and update constraints

### Step 3: Verify Policies
Run this query to check that RLS is enabled:

```sql
-- Check that RLS is enabled
SELECT schemaname, tablename, rowsecurity
FROM pg_tables
WHERE schemaname = 'public'
AND rowsecurity = true;

-- Verify api_keys table structure (should include notes field)
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'api_keys' AND table_schema = 'public'
ORDER BY ordinal_position;
```

### Step 4: Test Policies
Try inserting test data to ensure constraints work:

```sql
-- This should fail due to length constraint
INSERT INTO channels (name) VALUES (REPEAT('a', 101));

-- This should fail due to API key format constraint
INSERT INTO api_keys (name, key) VALUES ('test', 'invalid_key');
```

## ⚠️ Important Security Notes

### Current Limitations
- **Single-user application**: Current policies allow all operations
- **No user authentication**: All data is accessible to anyone with database access
- **Client-side validation**: Primary security relies on application-level validation

### Production Recommendations

1. **Implement User Authentication**:
   ```sql
   -- Example: Restrict to authenticated users only
   CREATE POLICY "Authenticated users only" ON channels
       FOR ALL USING (auth.role() = 'authenticated');
   ```

2. **Add User-based Access Control**:
   ```sql
   -- Example: Users can only access their own data
   CREATE POLICY "Users own data" ON channels
       FOR ALL USING (auth.uid() = user_id);
   ```

3. **Implement API Rate Limiting**:
   - Use Supabase Edge Functions for API proxying
   - Implement server-side rate limiting
   - Monitor API usage patterns

4. **Regular Security Audits**:
   - Review access logs
   - Monitor for suspicious activity
   - Update policies as needed

## 🔧 Maintenance

### Regular Tasks
1. **Monitor API Usage**: Check for unusual patterns
2. **Review Logs**: Look for failed authentication attempts
3. **Update Constraints**: Adjust limits as needed
4. **Backup Policies**: Keep security configurations in version control

### Emergency Procedures
1. **Disable RLS**: Only in extreme emergencies
2. **Revoke Access**: Remove compromised API keys immediately
3. **Audit Trail**: Maintain logs of all security changes

## 📞 Support

If you encounter issues with these security policies:

1. Check Supabase logs for error details
2. Verify all constraints are compatible with your data
3. Test policies in a development environment first
4. Contact Supabase support for complex RLS scenarios
