
import React, { useState, useRef, useEffect } from 'react';
import type { Tag } from '../types';
import { CloseIcon, MoreHorizontalIcon, PlusIcon, RenameIcon, TrashIcon, CheckIcon } from './Icons';
import { useClickOutside } from '../lib/utils';

interface ManageTagsModalProps {
    tags: Tag[];
    onClose: () => void;
    onAdd: (name: string) => Promise<void>;
    onRename: (id: string, newName: string) => Promise<void>;
    onDelete: (id: string) => Promise<void>;
}

export const ManageTagsModal: React.FC<ManageTagsModalProps> = ({ tags, onClose, onAdd, onRename, onDelete }) => {
    const [newTagName, setNewTagName] = useState('');
    const [isAdding, setIsAdding] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if(error) {
            const timer = setTimeout(() => setError(null), 3000);
            return () => clearTimeout(timer);
        }
    }, [error]);

    const handleAdd = async () => {
        if (!newTagName.trim() || isAdding) return;
        setIsAdding(true);
        setError(null);
        try {
            await onAdd(newTagName.trim());
            setNewTagName('');
        } catch (err) {
            setError((err as Error).message);
        } finally {
            setIsAdding(false);
        }
    };
    
    return (
         <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4" aria-modal="true" role="dialog">
            <div className="fixed inset-0" onClick={onClose} aria-hidden="true"></div>
            <div className="relative bg-dark-card border border-dark-border rounded-xl w-full max-w-md p-6 flex flex-col" style={{height: 'min(600px, 90vh)'}}>
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-bold text-white">Manage tags</h2>
                    <button onClick={onClose} className="p-1 rounded-full hover:bg-gray-700">
                        <CloseIcon className="w-6 h-6 text-gray-400" />
                    </button>
                </div>
                
                <div className="mb-6">
                    <p className="text-gray-400 text-sm mb-2">Create new tag</p>
                    <div className="flex items-center space-x-2">
                        <input
                            type="text"
                            value={newTagName}
                            onChange={e => setNewTagName(e.target.value)}
                            onKeyDown={e => e.key === 'Enter' && handleAdd()}
                            placeholder="Enter new tag name"
                            className="flex-grow bg-[#1a1a1a] border border-gray-600 rounded-lg px-4 py-2.5 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent"
                            disabled={isAdding}
                        />
                        <button
                            onClick={handleAdd}
                            disabled={isAdding || !newTagName.trim()}
                            className="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-gradient-to-r from-accent/20 to-accent/15 text-accent hover:from-accent/30 hover:to-accent/25 hover:scale-105 disabled:from-gray-600/50 disabled:to-gray-600/30 disabled:text-gray-400 disabled:cursor-not-allowed disabled:hover:scale-100 transition-all duration-200 border border-accent/20 hover:border-accent/30 disabled:border-gray-600/30 shadow-sm hover:shadow-accent/20"
                        >
                            {isAdding ? <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-accent"></div> : <PlusIcon className="w-6 h-6"/>}
                        </button>
                    </div>
                     {error && <p className="text-red-400 text-sm mt-2">{error}</p>}
                </div>

                <div className="flex-1 overflow-y-auto -mr-3 pr-3 space-y-2">
                    <div className="bg-[#1a1a1a]/50 p-3 rounded-lg space-y-2">
                        {tags.map(tag => 
                            <TagListItem key={tag.id} tag={tag} onRename={onRename} onDelete={onDelete} />
                        )}
                    </div>
                </div>

                <div className="mt-6 flex justify-end">
                    <button onClick={onClose} className="px-5 py-2.5 rounded-lg bg-white text-darkbg font-semibold hover:bg-gray-200 transition-colors">
                       Done
                    </button>
                </div>
            </div>
        </div>
    );
};

const TagListItem: React.FC<{tag: Tag} & Pick<ManageTagsModalProps, 'onRename' | 'onDelete'>> = ({tag, onRename, onDelete}) => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);
    useClickOutside(menuRef, () => setIsMenuOpen(false));

    const [isEditing, setIsEditing] = useState(false);
    const [editedName, setEditedName] = useState(tag.name);
    const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!isMenuOpen) setIsConfirmingDelete(false);
        if (isMenuOpen && isEditing) setIsEditing(false);
    }, [isMenuOpen]);
    
    useEffect(() => {
        if(error) {
            const timer = setTimeout(() => setError(null), 3000);
            return () => clearTimeout(timer);
        }
    }, [error]);

    const handleAction = async (action: Promise<any>, successCallback?: () => void) => {
        setIsLoading(true);
        setError(null);
        try {
            await action;
            if (successCallback) successCallback();
        } catch (err) {
            setError((err as Error).message);
        } finally {
            setIsLoading(false);
        }
    };
    
    const handleRename = () => {
        if(editedName.trim() && editedName.trim() !== tag.name) {
            handleAction(onRename(tag.id, editedName.trim()), () => setIsEditing(false));
        } else {
            setIsEditing(false);
        }
    };

    const handleDelete = () => handleAction(onDelete(tag.id));

    if (isEditing) {
        return (
            <div className="flex items-center justify-between bg-gradient-to-r from-dark-card to-dark-card/90 p-3 rounded-lg border-2 border-accent/50 shadow-lg shadow-accent/10">
                 <div className="flex-grow">
                    <input
                        type="text"
                        value={editedName}
                        onChange={e => setEditedName(e.target.value)}
                        onKeyDown={e => e.key === 'Enter' ? handleRename() : (e.key === 'Escape' && setIsEditing(false))}
                        className="w-full bg-[#1a1a1a] border border-gray-600 rounded-md px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent/50 focus:border-accent transition-all duration-200"
                        autoFocus
                        placeholder="Enter tag name..."
                    />
                    {error && <p className="text-red-400 text-xs mt-1 pl-1">{error}</p>}
                </div>
                <div className="flex items-center ml-3 gap-1">
                    <button
                        onClick={() => setIsEditing(false)}
                        className="p-2 text-gray-400 hover:text-white hover:bg-gray-700/50 rounded-md transition-all duration-200 hover:scale-110"
                    >
                        <CloseIcon className="w-5 h-5"/>
                    </button>
                    <button
                        onClick={handleRename}
                        disabled={isLoading}
                        className="p-2 text-accent hover:text-white hover:bg-accent/10 rounded-md transition-all duration-200 hover:scale-110 disabled:opacity-50 disabled:hover:scale-100"
                    >
                        <CheckIcon className="w-5 h-5"/>
                    </button>
                </div>
            </div>
        )
    }
    
    return (
        <div className="flex items-center justify-between bg-gradient-to-r from-dark-card to-dark-card/80 p-3 rounded-lg group border border-dark-border/50 hover:border-dark-border transition-all duration-200 hover:shadow-sm">
            <div className="flex items-center gap-3">
                <div className="w-2 h-2 rounded-full bg-accent/60 group-hover:bg-accent transition-colors duration-200"></div>
                <span className="text-white font-medium">{tag.name}</span>
            </div>
            <div className="relative" ref={menuRef}>
                <button
                    onClick={() => setIsMenuOpen(p => !p)}
                    className="p-2 text-gray-400 hover:text-white hover:bg-gray-700/50 rounded-md opacity-0 group-hover:opacity-100 focus:opacity-100 transition-all duration-200 hover:scale-110"
                >
                    <MoreHorizontalIcon className="w-5 h-5"/>
                </button>
                 {isMenuOpen && (
                    <div className="absolute top-full right-0 mt-2 w-40 bg-[#2a2a2a] border border-dark-border rounded-lg shadow-xl z-10 overflow-hidden">
                        <ul className="text-sm text-gray-200">
                             <li onClick={() => { setIsEditing(true); setIsMenuOpen(false); }} className="flex items-center px-4 py-2.5 cursor-pointer transition-colors duration-150 hover:bg-gray-700/50">
                                <RenameIcon className="w-4 h-4 mr-3" />
                                <span>Rename</span>
                            </li>
                            <li 
                                onClick={() => isConfirmingDelete ? handleDelete() : setIsConfirmingDelete(true)} 
                                className="flex items-center px-4 py-2.5 cursor-pointer transition-colors duration-150 text-red-400 hover:bg-red-500/10"
                            >
                                <TrashIcon className="w-4 h-4 mr-3" />
                                <span>{isConfirmingDelete ? 'Confirm' : 'Delete'}</span>
                            </li>
                        </ul>
                    </div>
                 )}
            </div>
        </div>
    )
}
