# 🔧 Quota Management System Troubleshooting Guide

## 🚨 Common Error: "structure of query does not match function result type"

### **Problem Description**
This error occurs when the `get_quota_status` database function's return structure doesn't match what the TypeScript code expects. The error typically appears in:
- **Location**: ChannelListPage.tsx line 174
- **Source**: youtubeService.ts:170 in getChannelsInfoByIds function
- **Error**: "Failed to validate API key: Error: Failed to validate quota usage: Failed to get quota status: structure of query does not match function result type"

### **Root Cause**
The PostgreSQL function `get_quota_status` has a type mismatch between:
1. The declared return type in the function definition
2. The actual data types returned by the SELECT query
3. What Supabase expects based on the function signature

### **Quick Fix Steps**

#### **Step 1: Apply the Database Fix**
Execute the fix script in your Supabase SQL Editor:

```sql
-- Go to Supabase Dashboard → SQL Editor
-- Copy and paste the contents of: database/fix-quota-function.sql
-- Click "Run" to execute the script
```

#### **Step 2: Verify the Fix**
Run the test script to confirm the fix worked:

```bash
# In your project directory
node scripts/test-quota-validation.js
```

#### **Step 3: Restart Your Application**
After applying the database fix:
1. Stop your development server
2. Restart with `npm run dev`
3. Test the quota functionality in Settings → Quota Management

## 🔍 **Detailed Diagnosis**

### **What the Error Means**
- PostgreSQL functions must have exact type matching between declaration and implementation
- Supabase's RPC calls are strict about return type structures
- Any mismatch in column names, types, or nullability causes this error

### **Common Causes**
1. **Type Mismatches**: INTEGER vs NUMERIC, TIMESTAMP vs TIMESTAMPTZ
2. **NULL Handling**: Missing COALESCE for nullable columns
3. **Column Aliases**: Inconsistent naming between function and TypeScript interface
4. **Precision Issues**: NUMERIC without proper precision specification

### **The Fix Explained**
The updated function includes:

```sql
-- Explicit type casting and null handling
SELECT 
    COALESCE(ak.usage, 0)::INTEGER AS current_usage,
    COALESCE(ak.quota_limit, 10000)::INTEGER AS quota_limit,
    GREATEST(0, COALESCE(ak.quota_limit, 10000) - COALESCE(ak.usage, 0))::INTEGER AS remaining_quota,
    CASE 
        WHEN COALESCE(ak.quota_limit, 10000) = 0 THEN 0::NUMERIC
        ELSE ROUND((COALESCE(ak.usage, 0)::NUMERIC / COALESCE(ak.quota_limit, 10000)::NUMERIC) * 100, 2)
    END AS usage_percentage,
    COALESCE(ak.last_reset_at, NOW()) AS last_reset_at,
    COALESCE(ak.last_reset_at, NOW()) + INTERVAL '24 hours' AS next_reset_at,
    CASE 
        WHEN ak.last_reset_at IS NULL THEN true
        ELSE (ak.last_reset_at < NOW() - INTERVAL '24 hours')
    END AS needs_reset
FROM api_keys ak
WHERE ak.id = api_key_id;
```

**Key Improvements:**
- ✅ **Explicit type casting**: `::INTEGER`, `::NUMERIC`
- ✅ **NULL safety**: `COALESCE()` for all nullable fields
- ✅ **Division by zero protection**: `NULLIF()` and `CASE` statements
- ✅ **Consistent column aliases**: Match TypeScript interface exactly

## 🧪 **Testing & Validation**

### **Test Scripts Available**
1. **`scripts/test-quota-validation.js`** - Quick validation test
2. **`scripts/diagnose-quota-issue.js`** - Comprehensive diagnosis
3. **`scripts/test-quota-system.js`** - Full system test

### **Manual Testing Steps**
1. **Check Function Exists**:
   ```sql
   SELECT routine_name, routine_type 
   FROM information_schema.routines 
   WHERE routine_name = 'get_quota_status';
   ```

2. **Test Function Call**:
   ```sql
   SELECT * FROM get_quota_status('your-api-key-id-here');
   ```

3. **Verify Return Structure**:
   ```sql
   SELECT 
       column_name, 
       data_type, 
       is_nullable 
   FROM information_schema.columns 
   WHERE table_name = 'get_quota_status';
   ```

## 🔄 **Alternative Solutions**

### **If the Fix Script Doesn't Work**

#### **Option 1: Manual Function Recreation**
```sql
-- Drop the problematic function
DROP FUNCTION IF EXISTS get_quota_status(UUID);

-- Recreate with simplified structure
CREATE OR REPLACE FUNCTION get_quota_status(api_key_id UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'current_usage', COALESCE(usage, 0),
        'quota_limit', COALESCE(quota_limit, 10000),
        'remaining_quota', GREATEST(0, COALESCE(quota_limit, 10000) - COALESCE(usage, 0)),
        'usage_percentage', ROUND((COALESCE(usage, 0)::NUMERIC / COALESCE(quota_limit, 10000)::NUMERIC) * 100, 2),
        'last_reset_at', COALESCE(last_reset_at, NOW()),
        'next_reset_at', COALESCE(last_reset_at, NOW()) + INTERVAL '24 hours',
        'needs_reset', CASE WHEN last_reset_at IS NULL THEN true ELSE (last_reset_at < NOW() - INTERVAL '24 hours') END
    ) INTO result
    FROM api_keys
    WHERE id = api_key_id;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### **Option 2: Use Direct Table Queries**
Modify the quota service to use direct table queries instead of functions:

```typescript
// In quotaService.ts - alternative implementation
getQuotaStatus: async (apiKeyId: string): Promise<QuotaStatus> => {
    const { data, error } = await supabase
        .from('api_keys')
        .select('usage, quota_limit, last_reset_at')
        .eq('id', apiKeyId)
        .single();
    
    if (error) throw new Error(`Failed to get quota status: ${error.message}`);
    if (!data) throw new Error('API key not found');
    
    const now = new Date();
    const lastReset = new Date(data.last_reset_at);
    const nextReset = new Date(lastReset.getTime() + 24 * 60 * 60 * 1000);
    const needsReset = (now.getTime() - lastReset.getTime()) > (24 * 60 * 60 * 1000);
    
    return {
        current_usage: data.usage || 0,
        quota_limit: data.quota_limit || 10000,
        remaining_quota: Math.max(0, (data.quota_limit || 10000) - (data.usage || 0)),
        usage_percentage: Math.round(((data.usage || 0) / (data.quota_limit || 10000)) * 100 * 100) / 100,
        last_reset_at: data.last_reset_at,
        next_reset_at: nextReset.toISOString(),
        needs_reset: needsReset
    };
}
```

## 🚨 **Emergency Workaround**

If you need to get the application working immediately while fixing the database:

### **Disable Quota Validation Temporarily**
```typescript
// In services/apiKeyService.ts - temporary workaround
validateOperation: async (operationCost: number): Promise<{ canProceed: boolean; remainingQuota: number; warning?: string }> => {
    try {
        // Temporary: Always allow operations
        console.warn('⚠️ Quota validation temporarily disabled');
        return {
            canProceed: true,
            remainingQuota: 10000,
            warning: 'Quota validation temporarily disabled'
        };
    } catch (error) {
        // Fallback to allow operations
        return {
            canProceed: true,
            remainingQuota: 10000,
            warning: 'Quota validation error - allowing operation'
        };
    }
}
```

**⚠️ Important**: Remove this workaround after fixing the database function!

## 📋 **Prevention Checklist**

To avoid similar issues in the future:

- ✅ **Test database functions** before deploying
- ✅ **Use explicit type casting** in PostgreSQL functions
- ✅ **Handle NULL values** with COALESCE
- ✅ **Match TypeScript interfaces** exactly
- ✅ **Include comprehensive tests** for database functions
- ✅ **Use migration scripts** for database changes
- ✅ **Document function signatures** clearly

## 🆘 **Getting Help**

If you're still experiencing issues:

1. **Check the logs** in your browser's developer console
2. **Run the diagnostic script**: `node scripts/diagnose-quota-issue.js`
3. **Verify your Supabase connection** and permissions
4. **Check the database migration** was applied correctly
5. **Review the function definition** in Supabase SQL Editor

## 📚 **Related Documentation**

- **Main Documentation**: `docs/quota-management-system.md`
- **Database Migration**: `database/api-key-quota-migration.sql`
- **Fix Script**: `database/fix-quota-function.sql`
- **Test Scripts**: `scripts/test-quota-*.js`

The quota management system should work smoothly once the database function structure is corrected!
