
import React, { useState, useRef, useEffect } from 'react';
import type { Channel } from '../types';
import { MoreHorizontalIcon, PinIcon, RenameIcon, DuplicateIcon, TrashIcon } from './Icons';
import { useClickOutside } from '../lib/utils';

interface ChannelCardProps {
    channel: Channel;
    onSelect: () => void;
    onDelete: (id: string) => Promise<void>;
    onRename: (id: string, newName: string) => Promise<void>;
    onDuplicate: (id: string) => Promise<void>;
    onPin: (id: string, isPinned: boolean) => Promise<void>;
}

export const ChannelCard: React.FC<ChannelCardProps> = ({ channel, onSelect, onDelete, onRename, onDuplicate, onPin }) => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);
    
    // Action states
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [notification, setNotification] = useState<string | null>(null);
    const [isRenaming, setIsRenaming] = useState(false);
    const [editedName, setEditedName] = useState(channel.name);
    const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);
    
    useClickOutside(menuRef, () => setIsMenuOpen(false));

    useEffect(() => {
        if (!isMenuOpen) {
            setIsConfirmingDelete(false);
        }
    }, [isMenuOpen]);

    useEffect(() => {
        const clearTimer = (setter: React.Dispatch<React.SetStateAction<string | null>>) => {
            const timer = setTimeout(() => setter(null), 3000);
            return () => clearTimeout(timer);
        };
        if (error) clearTimer(setError);
        if (notification) clearTimer(setNotification);
    }, [error, notification]);

    const handleAction = async (action: Promise<any>, successCallback?: () => void) => {
        setIsLoading(true);
        setError(null);
        try {
            await action;
            if (successCallback) successCallback();
        } catch (err) {
            setError((err as Error).message);
        } finally {
            setIsLoading(false);
            setIsMenuOpen(false);
        }
    };

    // --- Action Handlers ---
    const handleRenameClick = () => {
        setIsMenuOpen(false);
        setEditedName(channel.name);
        setIsRenaming(true);
    };

    const handleSaveRename = () => {
        if (editedName.trim() && editedName.trim() !== channel.name) {
            handleAction(onRename(channel.id, editedName.trim()), () => {
                setIsRenaming(false);
            });
        } else {
            setIsRenaming(false);
        }
    };

    const handleDelete = () => handleAction(onDelete(channel.id));
    const handleDuplicate = () => handleAction(onDuplicate(channel.id));
    
    const handlePin = () => {
        handleAction(onPin(channel.id, !channel.isPinned));
    };
    
    const CardOverlay: React.FC<{ message: string; type: 'error' | 'loading' | 'notification' }> = ({ message, type }) => {
        let bgColor = 'bg-black/70';
        if (type === 'error') bgColor = 'bg-red-800/80';

        return (
            <div className={`absolute inset-0 ${bgColor} backdrop-blur-sm flex items-center justify-center text-center p-2 rounded-lg z-20`}>
                {type === 'loading' ? (
                     <div className="w-6 h-6 rounded-full animate-spin border-2 border-dashed border-white border-t-transparent"></div>
                ) : (
                    <p className="text-white text-sm font-medium">{message}</p>
                )}
            </div>
        )
    };
    
    if (isRenaming) {
        return (
             <div className="relative group bg-dark-card border border-accent rounded-lg p-4 h-28 flex flex-col justify-between">
                {isLoading && <CardOverlay message="" type="loading" />}
                {error && <CardOverlay message={error} type="error" />}
                
                <input
                    type="text"
                    value={editedName}
                    onChange={(e) => setEditedName(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSaveRename()}
                    className="w-full bg-[#1a1a1a] border border-gray-600 rounded-md px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-accent"
                    autoFocus
                />
                <div className="flex justify-end space-x-2 pt-2">
                    <button onClick={() => setIsRenaming(false)} className="px-3 py-1 text-sm rounded-md text-gray-300 hover:bg-gray-700">Cancel</button>
                    <button onClick={handleSaveRename} className="px-3 py-1 text-sm rounded-md bg-accent text-darkbg font-semibold hover:bg-accent-hover">Save</button>
                </div>
            </div>
        )
    }

    return (
        <div className="relative group">
            {isLoading && <CardOverlay message="" type="loading" />}
            {error && <CardOverlay message={error} type="error" />}
            {notification && <CardOverlay message={notification} type="notification" />}

            <div 
                onClick={onSelect}
                className={`bg-dark-card border rounded-lg p-4 h-28 flex flex-col justify-between cursor-pointer transition-all duration-300 hover:-translate-y-1 ${
                    channel.isPinned 
                    ? 'border-accent/70 hover:border-accent' 
                    : 'border-dark-border hover:border-accent/50'
                }`}
            >
                <div className="flex-1 flex items-start justify-between">
                    <span className="font-semibold text-white pr-4">{channel.name}</span>
                    {channel.isPinned && <PinIcon className="w-4 h-4 text-accent flex-shrink-0 mt-1" />}
                </div>
            </div>
            <div ref={menuRef} className="absolute top-2 right-2 z-10">
                <button
                    onClick={() => setIsMenuOpen(prev => !prev)}
                    className="p-1.5 rounded-full text-gray-400 bg-dark-card/50 opacity-0 group-hover:opacity-100 focus:opacity-100 hover:text-white hover:bg-gray-700/70 transition-opacity"
                    aria-label="Channel options"
                >
                    <MoreHorizontalIcon className="w-5 h-5" />
                </button>
                {isMenuOpen && (
                    <div className="absolute top-full right-0 mt-2 w-48 bg-[#2a2a2a] border border-dark-border rounded-lg shadow-xl z-10 overflow-hidden">
                        <ul className="text-sm text-gray-200">
                            <MenuItem icon={<PinIcon className="w-4 h-4" />} onClick={handlePin}>{channel.isPinned ? 'Unpin' : 'Pin'}</MenuItem>
                            <MenuItem icon={<RenameIcon className="w-4 h-4" />} onClick={handleRenameClick}>Rename</MenuItem>
                            <MenuItem icon={<DuplicateIcon className="w-4 h-4" />} onClick={handleDuplicate}>Duplicate</MenuItem>
                            <div className="my-1 h-px bg-dark-border" />
                            <MenuItem 
                                icon={<TrashIcon className="w-4 h-4" />} 
                                isDestructive 
                                onClick={() => {
                                    if (isConfirmingDelete) {
                                        handleDelete();
                                    } else {
                                        setIsConfirmingDelete(true);
                                    }
                                }}
                            >
                                {isConfirmingDelete ? 'Confirm Delete' : 'Delete'}
                            </MenuItem>
                        </ul>
                    </div>
                )}
            </div>
        </div>
    );
};

interface MenuItemProps {
    children: React.ReactNode;
    icon: React.ReactNode;
    onClick: () => void;
    isDestructive?: boolean;
}

const MenuItem: React.FC<MenuItemProps> = ({ children, icon, onClick, isDestructive = false }) => (
    <li
        onClick={onClick}
        className={`flex items-center px-4 py-2.5 cursor-pointer transition-colors duration-150 ${
            isDestructive ? 'text-red-400 hover:bg-red-500/10' : 'hover:bg-gray-700/50'
        }`}
    >
        <span className="mr-3">{icon}</span>
        <span>{children}</span>
    </li>
);
