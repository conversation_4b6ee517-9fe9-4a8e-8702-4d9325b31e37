
import React, { useState, useEffect, useCallback } from 'react';
import type { ChannelStats, Statistic } from '../types';
import { youtubeService } from '../services/youtubeService';
import { StatCard } from './StatCard';
import { HistoryChart } from './HistoryChart';
import { LoadingSpinner } from './LoadingSpinner';
import { SubscribersIcon, VideoIcon, ViewsIcon, BackArrowIcon } from './Icons';

interface DashboardProps {
    channelId: string;
    onBack: () => void;
}

export const Dashboard: React.FC<DashboardProps> = ({ channelId, onBack }) => {
    const [channelData, setChannelData] = useState<ChannelStats | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    const fetchStats = useCallback(async () => {
        if (!channelId) return;
        try {
            setLoading(true);
            const data = await youtubeService.getChannelStats(channelId);
            setChannelData(data);
            setError(null);
        } catch (err) {
            setError('Failed to fetch channel statistics.');
            console.error(err);
        } finally {
            setLoading(false);
        }
    }, [channelId]);

    useEffect(() => {
        fetchStats();

        const intervalId = setInterval(fetchStats, 30000); // Refresh data every 30 seconds

        return () => clearInterval(intervalId);
    }, [fetchStats]);

    if (loading && !channelData) {
        return <div className="flex-1 flex items-center justify-center"><LoadingSpinner /></div>;
    }

    if (error) {
        return <div className="flex-1 flex items-center justify-center text-red-500">{error}</div>;
    }

    if (!channelData) {
        return <div className="flex-1 flex items-center justify-center text-gray-500">No data available.</div>;
    }

    const latestStats: Statistic | undefined = channelData.statistics[channelData.statistics.length - 1];

    return (
        <div className="flex-1 p-4 sm:p-6 lg:p-8 space-y-8">
            <header className="flex items-center space-x-4">
                 <button onClick={onBack} className="p-2 rounded-full hover:bg-dark-card transition-colors" aria-label="Back to channels">
                    <BackArrowIcon className="w-6 h-6" />
                 </button>
                <img src={channelData.avatar} alt={channelData.name} className="w-16 h-16 rounded-full border-2 border-accent" />
                <div>
                    <h2 className="text-3xl font-bold text-white">{channelData.name}</h2>
                    <p className="text-gray-400">youtube.com/channel/{channelData.youtubeId}</p>
                </div>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <StatCard
                    title="Total Subscribers"
                    value={latestStats?.subscriberCount}
                    icon={<SubscribersIcon className="w-8 h-8 text-accent" />}
                    trendData={channelData.statistics.map(s => ({ value: s.subscriberCount }))}
                />
                <StatCard
                    title="Total Views"
                    value={latestStats?.viewCount}
                    icon={<ViewsIcon className="w-8 h-8 text-accent" />}
                    trendData={channelData.statistics.map(s => ({ value: s.viewCount }))}
                />
                <StatCard
                    title="Total Videos"
                    value={latestStats?.videoCount}
                    icon={<VideoIcon className="w-8 h-8 text-accent" />}
                    trendData={channelData.statistics.map(s => ({ value: s.videoCount }))}
                />
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                 <HistoryChart data={channelData.statistics} dataKey="subscriberCount" title="Subscribers Over Time" />
                 <HistoryChart data={channelData.statistics} dataKey="viewCount" title="Views Over Time" />
            </div>

        </div>
    );
};