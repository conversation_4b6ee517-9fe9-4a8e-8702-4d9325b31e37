# 🚀 VirSnapp Production Deployment Guide

This guide provides step-by-step instructions for deploying VirSnapp to production with full security implementation.

## 📋 Pre-Deployment Checklist

Run the pre-deployment check to ensure readiness:
```bash
npm run deploy:check
```

## 🌍 Environment Configuration

### Step 1: Production Environment Variables

Create these environment variables in your production hosting platform:

```env
# Required Production Variables
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key
NODE_ENV=production

# Optional (for enhanced security)
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=production
```

### Platform-Specific Setup:

#### Vercel
1. Go to Project Settings → Environment Variables
2. Add each variable with Production scope
3. Redeploy after adding variables

#### Netlify
1. Go to Site Settings → Environment Variables
2. Add each variable
3. Trigger new deploy

#### Custom Hosting
1. Set environment variables in your hosting platform
2. Ensure variables are available during build process

## 🗄️ Database Security Setup

### Step 1: Apply Security Policies

1. **Access Supabase Dashboard**:
   - Go to your Supabase project dashboard
   - Navigate to "SQL Editor" in the sidebar

2. **Apply Security Policies**:
   ```sql
   -- Copy and paste the entire contents of database/security-policies.sql
   -- This includes:
   -- - Row Level Security (RLS) policies
   -- - Data integrity constraints
   -- - Input validation functions
   -- - Sanitization triggers
   ```

3. **Verify RLS is Enabled**:
   ```sql
   SELECT schemaname, tablename, rowsecurity 
   FROM pg_tables 
   WHERE schemaname = 'public' 
   AND rowsecurity = true;
   ```

4. **Test Constraints**:
   ```sql
   -- Test name length constraint
   INSERT INTO channels (name) VALUES (REPEAT('a', 101)); -- Should fail
   
   -- Test API key format constraint
   INSERT INTO api_keys (name, key) VALUES ('test', 'invalid_key'); -- Should fail
   ```

### Step 2: Database Performance Optimization

Apply these indexes for better performance:
```sql
-- Performance indexes (already in security-policies.sql)
CREATE INDEX IF NOT EXISTS idx_channels_youtube_id ON channels(youtube_id);
CREATE INDEX IF NOT EXISTS idx_statistics_channel_id ON statistics(channel_id);
CREATE INDEX IF NOT EXISTS idx_saved_videos_folder_id ON saved_videos(folder_id);
CREATE INDEX IF NOT EXISTS idx_videos_channel_id ON videos(channel_id);
```

## 🏗️ Build and Deployment Process

### Step 1: Pre-Build Validation
```bash
# Run security validation
npm run security:validate

# Run pre-deployment check
npm run deploy:check
```

### Step 2: Build Application
```bash
# Install dependencies
npm ci

# Build for production
npm run build

# Test build locally (optional)
npm run preview
```

### Step 3: Deploy to Platform

#### Option A: Vercel
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy to production
vercel --prod
```

#### Option B: Netlify
```bash
# Install Netlify CLI
npm i -g netlify-cli

# Deploy to production
netlify deploy --prod --dir=dist
```

#### Option C: Custom Hosting
```bash
# Upload the dist/ folder to your web server
# Ensure your server serves index.html for all routes (SPA routing)
```

## 🔍 Post-Deployment Validation

### Step 1: Verify Environment Variables
```javascript
// In browser console on production site:
console.log('Environment check:', {
  hasSupabaseUrl: !!import.meta.env.VITE_SUPABASE_URL,
  hasSupabaseKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
  environment: import.meta.env.NODE_ENV
});
```

### Step 2: Test Core Functionality
1. **Database Connection**: Try loading the app
2. **API Key Management**: Add a test API key in Settings
3. **Channel Search**: Search for a YouTube channel
4. **Video Saving**: Save a test video
5. **Security Features**: Try invalid inputs to test validation

### Step 3: Security Health Check
```javascript
// In browser console:
import { performSecurityHealthCheck } from './lib/securityMonitoring.js';
const healthCheck = await performSecurityHealthCheck();
console.log('Security Status:', healthCheck);
```

## 🚨 Troubleshooting

### Common Issues:

#### Environment Variables Not Working
- **Symptom**: App shows "Supabase not configured" message
- **Solution**: Verify environment variables are set with `VITE_` prefix
- **Check**: Variables must be available during build time

#### Database Connection Errors
- **Symptom**: Database operations fail
- **Solution**: Verify Supabase URL and anon key are correct
- **Check**: Ensure RLS policies are applied correctly

#### Build Failures
- **Symptom**: `npm run build` fails
- **Solution**: Run `npm run security:validate` first
- **Check**: Ensure all TypeScript errors are resolved

#### API Key Encryption Errors
- **Symptom**: Cannot decrypt API keys
- **Solution**: API keys are encrypted per browser session
- **Note**: This is expected behavior for security

## 📊 Monitoring Setup

### Step 1: Enable Security Monitoring
Security monitoring is automatically initialized when the app starts. Monitor the browser console for security events.

### Step 2: Set Up Alerts (Recommended)
For production monitoring, consider integrating:
- **Sentry**: For error tracking
- **LogRocket**: For session replay and monitoring
- **Supabase Logs**: For database monitoring

### Step 3: Regular Health Checks
Set up automated health checks:
```bash
# Add to your CI/CD pipeline
curl -f https://your-app.com/health || exit 1
```

## 🔄 Rollback Procedure

If issues occur after deployment:

1. **Immediate Rollback**:
   - Revert to previous deployment in your hosting platform
   - Verify previous version is working

2. **Database Rollback** (if needed):
   - Restore database from backup
   - Reapply security policies if necessary

3. **Investigation**:
   - Check security logs
   - Review deployment changes
   - Test fixes in staging environment

## 📞 Support Contacts

- **Supabase Issues**: https://supabase.com/support
- **Hosting Platform**: Check your platform's support documentation
- **Security Concerns**: Review SECURITY.md for emergency procedures

## 🎯 Success Criteria

Your deployment is successful when:
- ✅ App loads without errors
- ✅ Database operations work correctly
- ✅ API key management functions properly
- ✅ Security validation passes
- ✅ All core features are functional
- ✅ No security warnings in console
