

import type { Channel, ChannelDetails, SearchResultChannel, ChannelStats, VideoDetails, ApiKey } from '../types';
import { supabase } from '../lib/supabase';
import { apiKeyService } from './apiKeyService';
import { API_USAGE_LIMITS } from '../lib/apiKeySecurity';
import { validateYouTubeChannelQuery, validateYouTubeVideoUrl, validateName } from '../lib/inputValidation';

// New regex specifically for handles, which is the most reliable modern identifier.
const YOUTUBE_HANDLE_URL_REGEX = /(?:https?:\/\/)?(?:www\.)?youtube\.com\/(@[\w.-]+)/;
// Old regex, now modified to handle legacy URL formats, excluding the handle format.
const YOUTUBE_CHANNEL_URL_REGEX = /(?:https?:\/\/)?(?:www\.)?youtube\.com\/(?:c\/|channel\/|user\/)([\w.-]+)/;
const YOUTUBE_API_BASE_URL = 'https://www.googleapis.com/youtube/v3';

// Regex to extract video ID from various YouTube URL formats.
const YOUTUBE_VIDEO_URL_REGEX = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:watch\?v=|embed\/)|youtu\.be\/)([\w-]{11})/;


// --- Helper functions for data mapping ---

const channelFromDb = (dbObj: any): Channel => {
    if (!dbObj) throw new Error('Invalid channel data');
    return {
        id: dbObj.id,
        name: dbObj.name,
        youtubeId: dbObj.youtube_id,
        createdAt: dbObj.created_at,
        updatedAt: dbObj.updated_at,
        avatar: dbObj.avatar,
        isPinned: dbObj.is_pinned || false,
    };
};

const videoDetailsToDbVideo = (video: VideoDetails) => ({
    id: video.id,
    title: video.title,
    thumbnail_url: video.thumbnailUrl,
    view_count: video.viewCount,
    published_at: video.publishedAt,
    duration: video.duration,
    channel_id: video.channelId,
    channel_title: video.channelTitle,
    channel_avatar: video.channelAvatar,
    subscriber_count: video.subscriberCount,
    synced_at: new Date().toISOString(),
});

const dbVideoToVideoDetails = (dbVideo: any): VideoDetails => ({
    id: dbVideo.id,
    title: dbVideo.title,
    thumbnailUrl: dbVideo.thumbnail_url,
    viewCount: dbVideo.view_count,
    publishedAt: dbVideo.published_at,
    duration: dbVideo.duration,
    channelId: dbVideo.channel_id,
    channelTitle: dbVideo.channel_title,
    channelAvatar: dbVideo.channel_avatar,
    subscriberCount: dbVideo.subscriber_count,
});

const youtubeApiFetch = async (url: string, activeKey: ApiKey, cost: number) => {
    const response = await fetch(url);
    if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }));
        throw new Error(`YouTube API Error: ${errorData.error?.message || response.statusText}`);
    }

    try {
        await apiKeyService.updateUsage(activeKey.id, activeKey.usage + cost);
        // Update in-memory for any sequential calls that might reuse the key object.
        // This helps mitigate some race conditions but not for parallel requests.
        activeKey.usage += cost;
    } catch (e) {
        console.error("Failed to update API key usage:", e);
        // Do not fail the main request if usage update fails.
    }

    return response.json();
}

// Helper to map a YouTube API channel item to our local SearchResultChannel type.
const mapYoutubeItemToSearchResult = (item: any): SearchResultChannel => {
    const handle = item.snippet.customUrl ? (item.snippet.customUrl.startsWith('@') ? item.snippet.customUrl : `@${item.snippet.customUrl}`) : undefined;
    const subscriberCount = item.statistics.hiddenSubscriberCount ? undefined : parseInt(item.statistics.subscriberCount, 10);

    return {
        youtubeId: item.id,
        name: item.snippet.title,
        avatar: item.snippet.thumbnails.default.url,
        handle: handle,
        subscriberCount: subscriberCount,
    };
};


// Updated function to first try fetching by handle, then fall back to general search.
const searchOrFetchChannelInfo = async (query: string): Promise<SearchResultChannel[]> => {
    // Validate input
    const validation = validateYouTubeChannelQuery(query);
    if (!validation.isValid) {
        throw new Error(`Invalid channel query: ${validation.error}`);
    }

    const sanitizedQuery = validation.sanitized!;
    if (!sanitizedQuery.trim()) return [];

    // Validate API operation before proceeding
    const apiValidation = await apiKeyService.validateOperation(API_USAGE_LIMITS.SEARCH_COST);
    if (!apiValidation.canProceed) {
        throw new Error(apiValidation.warning || 'API quota exceeded');
    }

    if (apiValidation.warning) {
        console.warn('API Usage Warning:', apiValidation.warning);
    }

    const activeKey = await apiKeyService.getActiveApiKey();
    const apiKey = activeKey.key;
    
    // --- Strategy 1: Fetch by Handle ---
    // This is the most direct and reliable method if the user provides a handle URL.
    const handleMatch = sanitizedQuery.match(YOUTUBE_HANDLE_URL_REGEX);
    if (handleMatch && handleMatch[1]) {
        const handle = handleMatch[1];
        const channelsUrl = `${YOUTUBE_API_BASE_URL}/channels?part=snippet,statistics&forHandle=${encodeURIComponent(handle)}&key=${apiKey}`;
        const channelsData = await youtubeApiFetch(channelsUrl, activeKey, 1);

        if (channelsData.items && channelsData.items.length > 0) {
            return channelsData.items.map(mapYoutubeItemToSearchResult);
        }
        // If handle fetch fails, we return empty instead of falling through to avoid ambiguous results.
        return [];
    }

    // --- Strategy 2: General Search (Fallback) ---
    // This handles search terms and legacy URLs.
    let searchQuery = sanitizedQuery;
    const legacyUrlMatch = sanitizedQuery.match(YOUTUBE_CHANNEL_URL_REGEX);
    if (legacyUrlMatch && legacyUrlMatch[1]) {
        searchQuery = legacyUrlMatch[1];
    }
    
    const searchUrl = `${YOUTUBE_API_BASE_URL}/search?part=snippet&q=${encodeURIComponent(searchQuery)}&type=channel&maxResults=5&key=${apiKey}`;
    const searchData = await youtubeApiFetch(searchUrl, activeKey, 100);
    
    if (!searchData.items || searchData.items.length === 0) {
        return [];
    }
    
    const channelIds = searchData.items.map((item: any) => item.id.channelId).filter(Boolean);
    if (channelIds.length === 0) return [];

    const channelsUrl = `${YOUTUBE_API_BASE_URL}/channels?part=snippet,statistics&id=${channelIds.join(',')}&key=${apiKey}`;
    const channelsData = await youtubeApiFetch(channelsUrl, activeKey, 1);

    if (!channelsData.items || channelsData.items.length === 0) {
        return [];
    }

    return channelsData.items.map(mapYoutubeItemToSearchResult);
};

// This function is now standalone to be callable by other helpers before youtubeService is defined.
const getChannelsInfoByIds = async (channelIds: string[]): Promise<ChannelDetails[]> => {
      if (channelIds.length === 0) return [];

      // Validate API operation
      const apiValidation = await apiKeyService.validateOperation(API_USAGE_LIMITS.CHANNELS_COST);
      if (!apiValidation.canProceed) {
        throw new Error(apiValidation.warning || 'API quota exceeded');
      }

      const activeKey = await apiKeyService.getActiveApiKey();
      const apiKey = activeKey.key;
      
      const channelsUrl = `${YOUTUBE_API_BASE_URL}/channels?part=snippet,statistics&id=${channelIds.join(',')}&key=${apiKey}`;
      const channelsData = await youtubeApiFetch(channelsUrl, activeKey, 1);

      if (!channelsData.items) return [];

      return channelsData.items.map((item: any): ChannelDetails => {
          const handle = item.snippet.customUrl ? (item.snippet.customUrl.startsWith('@') ? item.snippet.customUrl : `@${item.snippet.customUrl}`) : undefined;
          return {
              youtubeId: item.id,
              name: item.snippet.title,
              avatar: item.snippet.thumbnails.high.url,
              subscriberCount: item.statistics.hiddenSubscriberCount ? 0 : parseInt(item.statistics.subscriberCount, 10),
              viewCount: parseInt(item.statistics.viewCount, 10),
              videoCount: parseInt(item.statistics.videoCount, 10),
              publishedAt: item.snippet.publishedAt,
              handle: handle,
          };
      });
};

// Internal function to fetch video data from YouTube API
const _fetchPopularVideosFromApi = async (channelIds: string[]): Promise<VideoDetails[]> => {
    if (channelIds.length === 0) return [];

    const activeKey = await apiKeyService.getActiveApiKey();
    const apiKey = activeKey.key;

    // 1. Get all channel details, including uploads playlist ID
    const channelsUrl = `${YOUTUBE_API_BASE_URL}/channels?part=snippet,statistics,contentDetails&id=${channelIds.join(',')}&key=${apiKey}`;
    const channelsData = await youtubeApiFetch(channelsUrl, activeKey, 1);
    
    if (!channelsData.items) return [];

    const channelsInfo = channelsData.items.map((item: any) => ({
        youtubeId: item.id,
        name: item.snippet.title,
        avatar: item.snippet.thumbnails.high.url,
        subscriberCount: item.statistics.hiddenSubscriberCount ? 0 : parseInt(item.statistics.subscriberCount, 10),
        uploadsPlaylistId: item.contentDetails.relatedPlaylists.uploads,
    }));

    const channelInfoMap = new Map<string, { avatar: string, subscriberCount: number, name: string }>();
    channelsInfo.forEach((ch: any) => {
        channelInfoMap.set(ch.youtubeId, { avatar: ch.avatar, subscriberCount: ch.subscriberCount, name: ch.name });
    });

    // 2. For each channel, fetch all their video IDs from the uploads playlist
    const videoIdsPerChannelPromises = channelsInfo.map(async (channel: any) => {
        if (!channel.uploadsPlaylistId) return { channelId: channel.youtubeId, videoIds: [] };
        
        let videoIds: string[] = [];
        let nextPageToken: string | undefined = undefined;
        
        try {
            do {
                const playlistUrl = new URL(`${YOUTUBE_API_BASE_URL}/playlistItems`);
                playlistUrl.searchParams.set('part', 'contentDetails');
                playlistUrl.searchParams.set('playlistId', channel.uploadsPlaylistId);
                playlistUrl.searchParams.set('maxResults', '50');
                playlistUrl.searchParams.set('key', apiKey);
                if (nextPageToken) {
                    playlistUrl.searchParams.set('pageToken', nextPageToken);
                }
                
                const playlistData = await youtubeApiFetch(playlistUrl.toString(), activeKey, 1);
                
                if (playlistData.items) {
                    const ids = playlistData.items.map((item: any) => item.contentDetails.videoId).filter(Boolean);
                    videoIds.push(...ids);
                }
                nextPageToken = playlistData.nextPageToken;
            } while (nextPageToken);
        } catch (e) {
            console.error(`Could not fetch playlist items for channel ${channel.youtubeId}`, e);
        }

        return { channelId: channel.youtubeId, videoIds };
    });

    const videoIdsPerChannelResult = await Promise.all(videoIdsPerChannelPromises);
    const allVideoIds = videoIdsPerChannelResult.flatMap(c => c.videoIds);
    const uniqueVideoIds = [...new Set(allVideoIds)];

    if (uniqueVideoIds.length === 0) return [];

    // 3. Fetch full video details for all unique video IDs in batches
    const videoDetailsMap = new Map<string, any>();
    const videoApiPromises = [];
    for (let i = 0; i < uniqueVideoIds.length; i += 50) {
        const batchIds = uniqueVideoIds.slice(i, i + 50).join(',');
        const url = `${YOUTUBE_API_BASE_URL}/videos?part=snippet,statistics,contentDetails&id=${batchIds}&key=${apiKey}`;
        videoApiPromises.push(youtubeApiFetch(url, activeKey, 1));
    }

    const videoStatsResults = await Promise.all(videoApiPromises);
    videoStatsResults.forEach(result => {
        if (result.items) {
            result.items.forEach((item: any) => {
                videoDetailsMap.set(item.id, item);
            });
        }
    });

    // 4. For each channel, sort its videos by view count and take the top 50
    let topVideosFromAllChannels: VideoDetails[] = [];
    
    videoIdsPerChannelResult.forEach(({ channelId: _channelId, videoIds }: { channelId: string, videoIds: string[] }) => {
        const channelVideos = videoIds.map((videoId: string) => {
            const videoItem = videoDetailsMap.get(videoId);
            if (!videoItem) return null;

            const channelInfo = channelInfoMap.get(videoItem.snippet.channelId);

            return {
                id: videoItem.id,
                title: videoItem.snippet.title,
                thumbnailUrl: videoItem.snippet.thumbnails.high?.url || videoItem.snippet.thumbnails.default?.url,
                viewCount: videoItem.statistics?.viewCount ? parseInt(videoItem.statistics.viewCount, 10) : 0,
                publishedAt: videoItem.snippet.publishedAt,
                duration: videoItem.contentDetails?.duration || 'PT0S',
                channelId: videoItem.snippet.channelId,
                channelTitle: videoItem.snippet.channelTitle,
                channelAvatar: channelInfo?.avatar || '',
                subscriberCount: channelInfo?.subscriberCount || 0,
            };
        }).filter((v): v is VideoDetails => v !== null);

        const top50ForChannel = channelVideos
            .sort((a: VideoDetails, b: VideoDetails) => b.viewCount - a.viewCount)
            .slice(0, 50);
            
        topVideosFromAllChannels.push(...top50ForChannel);
    });
    
    // 5. Return the combined list of top videos
    return topVideosFromAllChannels;
};


export const youtubeService = {
  getChannelStats: async (channelId: string): Promise<ChannelStats> => {
    const { data: channelData, error: channelError } = await supabase
      .from('channels')
      .select('*')
      .eq('id', channelId)
      .single();

    if (channelError) throw new Error(channelError.message);
    if (!channelData) throw new Error(`Channel with id ${channelId} not found.`);

    const { data: statsData, error: statsError } = await supabase
      .from('statistics')
      .select('subscriberCount, viewCount, videoCount, recordedAt')
      .eq('channel_id', channelId)
      .order('recordedAt', { ascending: true });

    if (statsError) throw new Error(statsError.message);

    const channel = channelFromDb(channelData);

    if (!channel.id) {
      throw new Error('Channel ID is required for statistics');
    }

    return {
      ...channel,
      id: channel.id, // Ensure id is defined
      statistics: statsData || [],
    };
  },

  getChannels: async (): Promise<Channel[]> => {
    const { data, error } = await supabase.from('channels').select('*').order('created_at', { ascending: true });
    if (error) throw new Error(error.message);
    return data.map(channelFromDb);
  },

  getChannelsInfoByIds: getChannelsInfoByIds,
  
    getVideoDetailsByUrl: async (url: string): Promise<{ title: string; thumbnailUrl: string; channelName: string; }> => {
        // Validate input
        const validation = validateYouTubeVideoUrl(url);
        if (!validation.isValid) {
            throw new Error(`Invalid YouTube video URL: ${validation.error}`);
        }

        const sanitizedUrl = validation.sanitized!;
        const match = sanitizedUrl.match(YOUTUBE_VIDEO_URL_REGEX);
        const videoId = match ? match[1] : null;

        if (!videoId) {
            throw new Error("Could not extract video ID from URL.");
        }

        const activeKey = await apiKeyService.getActiveApiKey();
        const apiKey = activeKey.key;
        
        const apiUrl = `${YOUTUBE_API_BASE_URL}/videos?part=snippet&id=${videoId}&key=${apiKey}`;
        const data = await youtubeApiFetch(apiUrl, activeKey, 1);

        if (!data.items || data.items.length === 0) {
            throw new Error("Video not found or is private.");
        }

        const snippet = data.items[0].snippet;
        return {
            title: snippet.title,
            thumbnailUrl: snippet.thumbnails.high?.url || snippet.thumbnails.default?.url,
            channelName: snippet.channelTitle,
        };
    },

  syncAllPopularVideos: async (channels: Channel[]): Promise<void> => {
    const allYoutubeIds = channels.flatMap(c => c.youtubeId?.split(',') || []).filter(Boolean);
    const uniqueYoutubeIds = [...new Set(allYoutubeIds)];

    if (uniqueYoutubeIds.length === 0) return;

    const videos = await _fetchPopularVideosFromApi(uniqueYoutubeIds);

    if(videos.length === 0) {
        console.log("No videos found to sync.");
        return;
    }

    const videosForDb = videos.map(videoDetailsToDbVideo);
    
    // Using `upsert` will insert new videos or update existing ones based on the primary key `id`.
    const { error } = await supabase.from('videos').upsert(videosForDb);

    if (error) {
        console.error("Supabase upsert error:", error);
        throw new Error(`Failed to save videos to the database: ${error.message}`);
    }
  },

  getPopularVideosForListFromDb: async (list: Channel): Promise<VideoDetails[]> => {
    const channelIds = list.youtubeId?.split(',').filter(Boolean) || [];
    if (channelIds.length === 0) return [];

    const { data, error } = await supabase
        .from('videos')
        .select('*')
        .in('channel_id', channelIds);
    
    if (error) {
        console.error("Supabase fetch error:", error);
        throw new Error(`Failed to fetch videos from database: ${error.message}`);
    }

    return (data || []).map(dbVideoToVideoDetails);
  },
  
  syncPopularVideosForList: async (list: Channel): Promise<void> => {
    const channelIds = list.youtubeId?.split(',').filter(Boolean) || [];
    if (channelIds.length === 0) return;

    // This internal function already handles API calls and data mapping
    const videos = await _fetchPopularVideosFromApi(channelIds);

    if (videos.length === 0) {
        console.log("No new videos found to sync for this list.");
        return;
    }

    const videosForDb = videos.map(videoDetailsToDbVideo);
    
    // Upsert into the database
    const { error } = await supabase.from('videos').upsert(videosForDb);

    if (error) {
        console.error("Supabase upsert error during list sync:", error);
        throw new Error(`Failed to save videos for the list: ${error.message}`);
    }
  },

  createChannelList: async (listName: string, channelsToAdd: SearchResultChannel[]): Promise<Channel> => {
    // Validate list name
    const nameValidation = validateName(listName);
    if (!nameValidation.isValid) {
      throw new Error(`Invalid list name: ${nameValidation.error}`);
    }

    if (!channelsToAdd || channelsToAdd.length === 0) {
      throw new Error('No channels provided to add to the list.');
    }

    const sanitizedName = nameValidation.sanitized!;

    // Hack: Store comma-separated IDs in the youtube_id field
    const youtubeIdsString = channelsToAdd.map(c => c.youtubeId).join(',');

    const newChannelListData = {
        name: sanitizedName,
        youtube_id: youtubeIdsString,
        avatar: 'https://placehold.co/48x48/1a1a1a/00ff88/png?text=L' // Generic placeholder avatar
    };

    const { data, error } = await supabase
      .from('channels')
      .insert(newChannelListData)
      .select()
      .single();

    if (error) {
        if (error.code === '23505' && error.message.includes('youtube_id')) {
             throw new Error(`A list with this exact combination of channels might already exist.`);
        }
        throw new Error(error.message);
    }
    return channelFromDb(data);
  },
  
  addChannelsToList: async (listId: string, channelsToAdd: SearchResultChannel[]): Promise<Channel> => {
    if (!channelsToAdd || channelsToAdd.length === 0) {
        throw new Error('No channels provided to add.');
    }

    const { data: listData, error: fetchError } = await supabase
        .from('channels')
        .select('youtube_id')
        .eq('id', listId)
        .single();

    if (fetchError || !listData) {
        throw new Error(fetchError?.message || 'List not found.');
    }

    const existingIds = new Set(listData.youtube_id?.split(',').filter((id: string) => id) || []);
    const newIds = channelsToAdd.map(c => c.youtubeId);

    newIds.forEach(id => existingIds.add(id));

    const newYoutubeIdsString = Array.from(existingIds).join(',');

    const { data: updatedData, error: updateError } = await supabase
        .from('channels')
        .update({ youtube_id: newYoutubeIdsString, updated_at: new Date().toISOString() })
        .eq('id', listId)
        .select('*')
        .single();
    
    if (updateError) {
        throw new Error(updateError.message);
    }

    return channelFromDb(updatedData);
},


  deleteChannel: async (channelId: string): Promise<{ success: true }> => {
    const { error } = await supabase.from('channels').delete().eq('id', channelId);
    if (error) throw new Error(error.message);
    return { success: true };
  },
  
  renameChannel: async(channelId: string, newName: string): Promise<Channel> => {
    // Validate new name
    const nameValidation = validateName(newName);
    if (!nameValidation.isValid) {
      throw new Error(`Invalid channel name: ${nameValidation.error}`);
    }

    const sanitizedName = nameValidation.sanitized!;

    const { data, error } = await supabase
        .from('channels')
        .update({ name: sanitizedName, updated_at: new Date().toISOString() })
        .eq('id', channelId)
        .select()
        .single();

    if (error) {
        throw new Error(error.message);
    }
    return channelFromDb(data);
  },

  duplicateChannel: async(channelId: string): Promise<Channel> => {
    const { data: originalChannel, error: fetchError } = await supabase
        .from('channels')
        .select('*')
        .eq('id', channelId)
        .single();

    if (fetchError || !originalChannel) throw new Error(fetchError?.message || 'Original channel list not found');
    
    const newName = `Copy of ${originalChannel.name}`;
    
    const { data: duplicated, error: insertError } = await supabase
        .from('channels')
        .insert({
            name: newName,
            youtube_id: originalChannel.youtube_id,
            avatar: originalChannel.avatar,
            is_pinned: false // Duplicates should not be pinned
        })
        .select()
        .single();

    if(insertError) throw new Error(insertError.message);

    return channelFromDb(duplicated);
  },
  
  pinChannel: async(channelId: string, isPinned: boolean): Promise<Channel> => {
    const { data, error } = await supabase
        .from('channels')
        .update({ is_pinned: isPinned, updated_at: new Date().toISOString() })
        .eq('id', channelId)
        .select('*')
        .single();
    
    if (error) {
        throw new Error(error.message);
    }
    return channelFromDb(data);
  },

  removeChannelsFromList: async (listId: string, channelYoutubeIdsToRemove: string[]): Promise<Channel> => {
    if (channelYoutubeIdsToRemove.length === 0) {
        const {data: originalData} = await supabase.from('channels').select('*').eq('id', listId).single();
        if(!originalData) throw new Error("List not found");
        return channelFromDb(originalData);
    }

    const { data: listData, error: fetchError } = await supabase
        .from('channels')
        .select('youtube_id')
        .eq('id', listId)
        .single();

    if (fetchError || !listData) {
        throw new Error(fetchError?.message || 'List not found.');
    }
    
    const idsToRemoveSet = new Set(channelYoutubeIdsToRemove);
    const currentIds = listData.youtube_id?.split(',') || [];
    const newIds = currentIds.filter((id: string) => !idsToRemoveSet.has(id));
    
    const newYoutubeIdsString = newIds.join(',');

    const { data: updatedData, error: updateError } = await supabase
        .from('channels')
        .update({ youtube_id: newYoutubeIdsString, updated_at: new Date().toISOString() })
        .eq('id', listId)
        .select('*')
        .single();
    
    if (updateError) {
        throw new Error(updateError.message);
    }

    return channelFromDb(updatedData);
  },
  
  searchOrFetchChannelInfo: searchOrFetchChannelInfo,
};