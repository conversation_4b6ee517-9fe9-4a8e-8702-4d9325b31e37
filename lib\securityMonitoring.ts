/**
 * Security Monitoring and Logging Utilities
 * Provides functions to monitor and log security-related events
 */

import { supabase } from './supabase';

export interface SecurityEvent {
  event_type: 'api_key_added' | 'api_key_deleted' | 'quota_exceeded' | 'invalid_input' | 'suspicious_activity';
  description: string;
  metadata?: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
}

/**
 * Logs a security event (in production, this would go to a proper logging service)
 */
export const logSecurityEvent = async (event: Omit<SecurityEvent, 'timestamp'>): Promise<void> => {
  const securityEvent: SecurityEvent = {
    ...event,
    timestamp: new Date().toISOString()
  };
  
  // In development, log to console
  if (import.meta.env.NODE_ENV === 'development') {
    console.warn('🔒 Security Event:', securityEvent);
  }
  
  // In production, you would send this to a logging service like:
  // - Supabase Edge Functions
  // - External logging service (LogRocket, Sentry, etc.)
  // - Custom audit table
  
  // For now, we'll store in localStorage for debugging
  try {
    const existingLogs = JSON.parse(localStorage.getItem('security_logs') || '[]');
    existingLogs.push(securityEvent);
    
    // Keep only last 100 events
    if (existingLogs.length > 100) {
      existingLogs.splice(0, existingLogs.length - 100);
    }
    
    localStorage.setItem('security_logs', JSON.stringify(existingLogs));
  } catch (error) {
    console.error('Failed to store security log:', error);
  }
};

/**
 * Gets recent security events from local storage
 */
export const getSecurityLogs = (): SecurityEvent[] => {
  try {
    return JSON.parse(localStorage.getItem('security_logs') || '[]');
  } catch {
    return [];
  }
};

/**
 * Clears security logs
 */
export const clearSecurityLogs = (): void => {
  localStorage.removeItem('security_logs');
};

/**
 * Monitors API key usage patterns for suspicious activity
 */
export const monitorApiKeyUsage = async (): Promise<void> => {
  try {
    const { data: apiKeys, error } = await supabase
      .from('api_keys')
      .select('id, name, usage, created_at');
    
    if (error) {
      await logSecurityEvent({
        event_type: 'suspicious_activity',
        description: 'Failed to fetch API keys for monitoring',
        severity: 'medium',
        metadata: { error: error.message }
      });
      return;
    }
    
    for (const key of apiKeys) {
      // Check for unusual usage patterns
      if (key.usage > 8000) { // 80% of daily quota
        await logSecurityEvent({
          event_type: 'quota_exceeded',
          description: `API key "${key.name}" approaching quota limit`,
          severity: 'high',
          metadata: { 
            keyId: key.id, 
            keyName: key.name, 
            usage: key.usage 
          }
        });
      }
      
      // Check for very high usage (potential abuse)
      if (key.usage > 15000) {
        await logSecurityEvent({
          event_type: 'suspicious_activity',
          description: `API key "${key.name}" has unusually high usage`,
          severity: 'critical',
          metadata: { 
            keyId: key.id, 
            keyName: key.name, 
            usage: key.usage 
          }
        });
      }
    }
  } catch (error) {
    await logSecurityEvent({
      event_type: 'suspicious_activity',
      description: 'Error during API key usage monitoring',
      severity: 'medium',
      metadata: { error: String(error) }
    });
  }
};

/**
 * Validates database connection security
 */
export const validateDatabaseSecurity = async (): Promise<{
  isSecure: boolean;
  issues: string[];
  recommendations: string[];
}> => {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  try {
    // Check if RLS is enabled (this would require a custom function in Supabase)
    // For now, we'll do basic checks
    
    // Check for empty or default API keys
    const { data: apiKeys } = await supabase
      .from('api_keys')
      .select('name, key');
    
    if (apiKeys) {
      for (const key of apiKeys) {
        if (key.name.toLowerCase().includes('test') || key.name.toLowerCase().includes('default')) {
          issues.push(`API key "${key.name}" appears to be a test/default key`);
          recommendations.push('Remove test API keys from production');
        }
      }
    }
    
    // Check for suspicious data patterns
    const { data: channels } = await supabase
      .from('channels')
      .select('name')
      .limit(10);
    
    if (channels) {
      for (const channel of channels) {
        if (channel.name.length > 100) {
          issues.push('Found channel names exceeding recommended length');
          recommendations.push('Implement proper input validation');
          break;
        }
      }
    }
    
  } catch (error) {
    issues.push('Failed to validate database security');
    recommendations.push('Check database connection and permissions');
  }
  
  return {
    isSecure: issues.length === 0,
    issues,
    recommendations
  };
};

/**
 * Performs a security health check
 */
export const performSecurityHealthCheck = async (): Promise<{
  status: 'healthy' | 'warning' | 'critical';
  checks: Array<{
    name: string;
    status: 'pass' | 'fail' | 'warning';
    message: string;
  }>;
}> => {
  const checks: Array<{
    name: string;
    status: 'pass' | 'fail' | 'warning';
    message: string;
  }> = [];
  
  // Check 1: Environment variables
  const hasEnvVars = Boolean(
    import.meta.env.VITE_SUPABASE_URL && 
    import.meta.env.VITE_SUPABASE_ANON_KEY
  );
  
  checks.push({
    name: 'Environment Variables',
    status: hasEnvVars ? 'pass' : 'fail',
    message: hasEnvVars 
      ? 'Environment variables are properly configured'
      : 'Missing required environment variables'
  });
  
  // Check 2: API Key validation
  try {
    const { data: apiKeys } = await supabase.from('api_keys').select('count');
    checks.push({
      name: 'API Keys',
      status: apiKeys && apiKeys.length > 0 ? 'pass' : 'warning',
      message: apiKeys && apiKeys.length > 0 
        ? 'API keys are configured'
        : 'No API keys found - application may not function'
    });
  } catch {
    checks.push({
      name: 'API Keys',
      status: 'fail',
      message: 'Failed to check API key configuration'
    });
  }
  
  // Check 3: Database connectivity
  try {
    await supabase.from('channels').select('count').limit(1);
    checks.push({
      name: 'Database Connection',
      status: 'pass',
      message: 'Database connection is working'
    });
  } catch {
    checks.push({
      name: 'Database Connection',
      status: 'fail',
      message: 'Database connection failed'
    });
  }
  
  // Determine overall status
  const failedChecks = checks.filter(c => c.status === 'fail').length;
  const warningChecks = checks.filter(c => c.status === 'warning').length;
  
  let status: 'healthy' | 'warning' | 'critical';
  if (failedChecks > 0) {
    status = 'critical';
  } else if (warningChecks > 0) {
    status = 'warning';
  } else {
    status = 'healthy';
  }
  
  return { status, checks };
};

/**
 * Initializes security monitoring
 */
export const initializeSecurityMonitoring = (): void => {
  // Log application start
  logSecurityEvent({
    event_type: 'suspicious_activity',
    description: 'Application started',
    severity: 'low',
    metadata: {
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    }
  });
  
  // Set up periodic monitoring (every 5 minutes)
  setInterval(monitorApiKeyUsage, 5 * 60 * 1000);
  
  // Monitor for suspicious activity patterns
  window.addEventListener('beforeunload', () => {
    logSecurityEvent({
      event_type: 'suspicious_activity',
      description: 'Application closed',
      severity: 'low'
    });
  });
};
