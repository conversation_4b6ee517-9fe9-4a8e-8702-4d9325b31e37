
import React, { useState, useEffect, useCallback } from 'react';
import { EyeIcon, EyeOffIcon, PlusIcon, TrashIcon, RenameIcon, CheckIcon, CloseIcon } from './Icons';
import type { <PERSON>pi<PERSON><PERSON> } from '../types';
import { apiKeyService, ACTIVE_API_KEY_ID_STORAGE_KEY } from '../services/apiKeyService';
import { ApiKeyQuotaCard } from './ApiKeyQuotaCard';
import { QuotaManagementDashboard } from './QuotaManagementDashboard';

export const SettingsPage: React.FC = () => {
    const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
    const [activeKeyId, setActiveKeyId] = useState<string | null>(null);
    const [visibleKeyIds, setVisibleKeyIds] = useState<Set<string>>(new Set());
    const [editingKeyId, setEditingKeyId] = useState<string | null>(null);

    const [newKeyName, setNewKeyName] = useState('');
    const [newKeyValue, setNewKeyValue] = useState('');
    const [isAdding, setIsAdding] = useState(false);
    const [loading, setLoading] = useState(true);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const [showQuotaDashboard, setShowQuotaDashboard] = useState(false);

    const loadApiKeys = useCallback(async () => {
        try {
            setLoading(true);
            const keys = await apiKeyService.getApiKeys();
            setApiKeys(keys);

            const storedActiveId = localStorage.getItem(ACTIVE_API_KEY_ID_STORAGE_KEY);
            if (storedActiveId && keys.some(k => k.id === storedActiveId)) {
                setActiveKeyId(storedActiveId);
            } else if (keys.length > 0) {
                const firstKeyId = keys[0].id;
                setActiveKeyId(firstKeyId);
                localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, firstKeyId);
            }

        } catch (err) {
            setErrorMessage((err as Error).message);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        loadApiKeys();
    }, [loadApiKeys]);

    const displayError = (message: string) => {
        setErrorMessage(message);
        setTimeout(() => setErrorMessage(null), 4000);
    };

    const handleAddKey = async () => {
        const trimmedKeyName = newKeyName.trim();
        const trimmedKeyValue = newKeyValue.trim();
        
        if (!trimmedKeyName || !trimmedKeyValue) {
            displayError("Please provide both a name and a value for the API key.");
            return;
        }

        setIsAdding(true);
        setErrorMessage(null);

        try {
            const newKey = await apiKeyService.addApiKey(trimmedKeyName, trimmedKeyValue);
            const updatedKeys = [...apiKeys, newKey];
            setApiKeys(updatedKeys);
            
            if (!activeKeyId) {
                setActiveKeyId(newKey.id);
                localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, newKey.id);
            }
            
            setNewKeyName('');
            setNewKeyValue('');
        } catch (err) {
            displayError((err as Error).message);
        } finally {
            setIsAdding(false);
        }
    };

    const handleSetActive = (id: string) => {
        setActiveKeyId(id);
        localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, id);
    };

    const handleSaveRename = async (id: string, newName: string) => {
        try {
            const updatedKey = await apiKeyService.renameApiKey(id, newName);
            setApiKeys(apiKeys.map(k => k.id === id ? updatedKey : k));
        } catch (err) {
            displayError((err as Error).message);
        } finally {
            setEditingKeyId(null);
        }
    };
    
    const handleDeleteKey = async (id: string) => {
        try {
            await apiKeyService.deleteApiKey(id);
            const updatedKeys = apiKeys.filter(k => k.id !== id);
            setApiKeys(updatedKeys);

            if (activeKeyId === id) {
                const newActiveId = updatedKeys.length > 0 ? updatedKeys[0].id : null;
                setActiveKeyId(newActiveId);
                if (newActiveId) {
                     localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, newActiveId);
                } else {
                    localStorage.removeItem(ACTIVE_API_KEY_ID_STORAGE_KEY);
                }
            }
        } catch (err) {
            displayError((err as Error).message);
        }
    };
    
    const toggleVisibility = (id: string) => {
        setVisibleKeyIds(prev => {
            const newSet = new Set(prev);
            if (newSet.has(id)) {
                newSet.delete(id);
            } else {
                newSet.add(id);
            }
            return newSet;
        });
    };

    return (
        <div className="min-h-screen bg-[#0f0f0f]">
            <div className="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Page Header */}
                <header className="mb-10">
                    <h1 className="text-[32px] font-semibold text-white mb-2">Settings</h1>
                    <p className="text-gray-400 text-base">Manage your API keys and monitor quota usage</p>
                </header>

                {/* Navigation Tabs */}
                <div className="mb-6">
                    <div className="bg-[#1a1a1a] rounded-lg p-1 inline-flex">
                        <button
                            onClick={() => setShowQuotaDashboard(false)}
                            className={`px-6 py-3 text-sm font-medium rounded-md transition-all duration-200 ${
                                !showQuotaDashboard
                                    ? 'bg-[#10b981] text-white'
                                    : 'text-gray-400 hover:text-white'
                            }`}
                        >
                            API Keys
                        </button>
                        <button
                            onClick={() => setShowQuotaDashboard(true)}
                            className={`px-6 py-3 text-sm font-medium rounded-md transition-all duration-200 ${
                                showQuotaDashboard
                                    ? 'bg-[#10b981] text-white'
                                    : 'text-gray-400 hover:text-white'
                            }`}
                        >
                            Quota Management
                        </button>
                    </div>
                </div>

                {showQuotaDashboard ? (
                    <QuotaManagementDashboard />
                ) : (
                    <div className="space-y-6">
                        {/* Add New API Key Section */}
                        <div className="bg-[#1a1a1a] rounded-xl p-8">
                            <div className="mb-6">
                                <h2 className="text-xl font-semibold text-white mb-2">Add New API Key</h2>
                                <p className="text-gray-400 text-sm mb-2">Your API keys are stored securely in your Supabase database.</p>
                            </div>

                            {errorMessage && (
                                <div className="bg-red-500/10 border border-red-500/30 text-red-300 px-4 py-3 rounded-lg mb-6 text-sm" role="alert">
                                    {errorMessage}
                                </div>
                            )}

                            <div className="space-y-6">
                                <div className="grid grid-cols-[1fr_2fr] gap-4">
                                    <div>
                                        <label htmlFor="key-name" className="block text-xs font-medium text-gray-300 mb-2">
                                            Key Name
                                        </label>
                                        <input
                                            id="key-name"
                                            type="text"
                                            value={newKeyName}
                                            onChange={e => setNewKeyName(e.target.value)}
                                            placeholder="e.g., 'Personal Key'"
                                            className="w-full bg-[#0f0f0f] border border-gray-600 rounded-lg px-3 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#10b981] focus:border-[#10b981] transition-all duration-200"
                                        />
                                    </div>
                                    <div>
                                        <label htmlFor="key-value" className="block text-xs font-medium text-gray-300 mb-2">
                                            API Key Value
                                        </label>
                                        <input
                                            id="key-value"
                                            type="password"
                                            value={newKeyValue}
                                            onChange={e => setNewKeyValue(e.target.value)}
                                            placeholder="Enter your YouTube API key"
                                            className="w-full bg-[#0f0f0f] border border-gray-600 rounded-lg px-3 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#10b981] focus:border-[#10b981] transition-all duration-200"
                                        />
                                    </div>
                                </div>

                                <div className="flex justify-end mt-6">
                                    <button
                                        onClick={handleAddKey}
                                        disabled={isAdding || !newKeyName.trim() || !newKeyValue.trim()}
                                        className="flex items-center gap-2 px-6 py-3 rounded-lg bg-[#10b981] text-white font-medium hover:bg-[#059669] disabled:bg-gray-600 disabled:text-gray-400 transition-all duration-200"
                                    >
                                        <PlusIcon className="w-4 h-4"/>
                                        <span>{isAdding ? 'Adding...' : 'Add Key'}</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Your API Keys Section */}
                        <div>
                            <div className="flex items-center justify-between mb-4">
                                <h2 className="text-xl font-semibold text-white">Your API Keys</h2>
                                {apiKeys.length > 0 && (
                                    <div className="text-xs text-gray-400">
                                        {apiKeys.length} key{apiKeys.length !== 1 ? 's' : ''} total
                                    </div>
                                )}
                            </div>

                            <div className="space-y-6">
                                {loading ? (
                                    <div className="space-y-6">
                                        {[1, 2].map(i => (
                                            <div key={i} className="bg-[#1a1a1a] rounded-xl p-6 animate-pulse">
                                                <div className="flex items-center justify-between mb-3">
                                                    <div className="h-4 bg-gray-700 rounded w-1/4"></div>
                                                    <div className="h-6 bg-gray-700 rounded w-16"></div>
                                                </div>
                                                <div className="h-3 bg-gray-700 rounded w-3/4 mb-4"></div>
                                                <div className="h-2 bg-gray-700 rounded w-full"></div>
                                            </div>
                                        ))}
                                    </div>
                                ) : apiKeys.length > 0 ? (
                                    <div className="space-y-6">
                                        {apiKeys.map(apiKey => (
                                            <div key={apiKey.id} className="space-y-6">
                                                <div className={`bg-[#1a1a1a] rounded-xl p-6 ${activeKeyId === apiKey.id ? 'border-l-4 border-[#10b981]' : ''}`}>
                                                    <ApiKeyListItem
                                                        apiKey={apiKey}
                                                        isActive={activeKeyId === apiKey.id}
                                                        isVisible={visibleKeyIds.has(apiKey.id)}
                                                        isEditing={editingKeyId === apiKey.id}
                                                        onSetActive={() => handleSetActive(apiKey.id)}
                                                        onSetEditing={setEditingKeyId}
                                                        onSaveRename={handleSaveRename}
                                                        onDelete={() => handleDeleteKey(apiKey.id)}
                                                        onToggleVisibility={() => toggleVisibility(apiKey.id)}
                                                    />
                                                </div>
                                                <div className="bg-[#1a1a1a] rounded-xl p-8">
                                                    <ApiKeyQuotaCard
                                                        apiKey={apiKey}
                                                        showResetButton={true}
                                                        onQuotaReset={() => {
                                                            // Refresh API keys after quota reset
                                                            loadApiKeys();
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="bg-[#1a1a1a] rounded-xl p-12 text-center">
                                        <div className="w-16 h-16 mx-auto mb-6 bg-gray-700/30 rounded-full flex items-center justify-center">
                                            <svg className="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                                            </svg>
                                        </div>
                                        <h4 className="text-xl font-semibold text-white mb-2">No API keys found</h4>
                                        <p className="text-gray-400 mb-6 max-w-md mx-auto">Add your first YouTube API key above to start tracking channels and managing your quota.</p>
                                        <button
                                            onClick={() => document.getElementById('key-name')?.focus()}
                                            className="text-[#10b981] hover:text-[#059669] font-medium transition-colors"
                                        >
                                            Add your first API key →
                                        </button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

interface ApiKeyListItemProps {
    apiKey: ApiKey;
    isActive: boolean;
    isVisible: boolean;
    isEditing: boolean;
    onSetActive: () => void;
    onSaveRename: (id: string, newName: string) => void;
    onDelete: () => void;
    onToggleVisibility: () => void;
    onSetEditing: (id: string | null) => void;
}

const MAX_REQUESTS = 10000;

const ApiKeyListItem: React.FC<ApiKeyListItemProps> = ({ apiKey, isActive, isVisible, isEditing, onSetActive, onSaveRename, onDelete, onToggleVisibility, onSetEditing }) => {
    const [editedName, setEditedName] = useState(apiKey.name);
    const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);

    useEffect(() => {
        if (isEditing) {
            setEditedName(apiKey.name);
        }
    }, [isEditing, apiKey.name]);
    
    const handleSave = () => {
        const trimmedName = editedName.trim();
        if (trimmedName && trimmedName !== apiKey.name) {
            onSaveRename(apiKey.id, trimmedName);
        } else {
            onSetEditing(null); 
        }
    };
    
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') handleSave();
        if (e.key === 'Escape') onSetEditing(null);
    };

    const handleConfirmDelete = () => {
        onDelete();
        setIsConfirmingDelete(false);
    };

    const progressPercentage = (apiKey.usage / MAX_REQUESTS) * 100;
    let progressColorClass = 'bg-[#10b981]';
    if (progressPercentage > 80) {
        progressColorClass = 'bg-red-500';
    } else if (progressPercentage > 50) {
        progressColorClass = 'bg-yellow-500';
    }

    if (isEditing) {
        return (
            <div className="bg-transparent border-2 border-accent/50 rounded-xl p-6 shadow-lg ring-2 ring-accent/20">
                <div className="flex items-center gap-6">
                    <div className="flex-grow space-y-4">
                        <div>
                            <label className="block text-sm font-semibold text-gray-300 mb-2">Key Name</label>
                            <input
                                type="text"
                                value={editedName}
                                onChange={(e) => setEditedName(e.target.value)}
                                onKeyDown={handleKeyDown}
                                className="w-full bg-[#1a1a1a] border border-gray-600/50 rounded-xl px-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent transition-all duration-200"
                                autoFocus
                                onFocus={(e) => e.target.select()}
                                placeholder="Enter key name"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-semibold text-gray-300 mb-2">API Key</label>
                            <p className="text-sm text-gray-400 font-mono bg-[#1a1a1a] border border-gray-600/30 rounded-xl px-4 py-3">
                                {isVisible ? apiKey.key : '•'.repeat(20) + apiKey.key.slice(-4)}
                            </p>
                        </div>
                    </div>
                    <div className="flex flex-col gap-3 flex-shrink-0">
                        <button
                            onClick={handleSave}
                            className="flex items-center gap-2 px-4 py-2.5 text-green-400 hover:text-green-300 hover:bg-green-500/10 rounded-xl transition-all duration-200 font-medium"
                            title="Save changes"
                        >
                            <CheckIcon className="w-5 h-5" />
                            Save
                        </button>
                        <button
                            onClick={() => onSetEditing(null)}
                            className="flex items-center gap-2 px-4 py-2.5 text-gray-400 hover:text-white hover:bg-gray-700/30 rounded-xl transition-all duration-200 font-medium"
                            title="Cancel editing"
                        >
                            <CloseIcon className="w-5 h-5" />
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        );
    }
    
    return (
        <div>
            {/* Header Row */}
            <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                    <h3 className="text-base font-semibold text-white">{apiKey.name}</h3>
                    {isActive && (
                        <span className="bg-[#10b981] text-white text-xs px-2.5 py-1 rounded-full">
                            Active
                        </span>
                    )}
                </div>
                <div className="text-sm text-gray-400">
                    {new Date(apiKey.created_at).toLocaleDateString()}
                </div>
            </div>

            {/* Key Display Row */}
            <div className="mb-4">
                <div className="text-xs text-gray-400 uppercase mb-1">API KEY</div>
                <div className="flex items-center gap-4">
                    <p className="font-mono text-gray-300 flex-1">
                        {isVisible ? apiKey.key : '•'.repeat(20) + apiKey.key.slice(-4)}
                    </p>
                    <button
                        onClick={onToggleVisibility}
                        className="text-gray-400 hover:text-white transition-colors"
                        title={isVisible ? "Hide API key" : "Show API key"}
                    >
                        {isVisible ? <EyeOffIcon className="w-4 h-4" /> : <EyeIcon className="w-4 h-4" />}
                    </button>
                </div>
            </div>

            {/* Usage Statistics Section */}
            <div className="mb-4">
                <div className="text-sm font-medium text-white mb-3">Usage Statistics</div>
                <div className="flex justify-between items-center mb-2">
                    <span className="text-xs text-gray-400">{progressPercentage.toFixed(1)}% used</span>
                    <span className="text-base font-semibold text-white">
                        {apiKey.usage.toLocaleString()} / {MAX_REQUESTS.toLocaleString()}
                    </span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2 mb-2">
                    <div
                        className={`h-2 rounded-full transition-all duration-500 ${progressColorClass}`}
                        style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                    ></div>
                </div>
                <div className="flex justify-between items-center">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                        progressPercentage > 80 ? 'text-red-300 bg-red-500/20' :
                        progressPercentage > 50 ? 'text-yellow-300 bg-yellow-500/20' :
                        'text-green-300 bg-green-500/20'
                    }`}>
                        {progressPercentage > 80 ? 'High usage' : progressPercentage > 50 ? 'Moderate usage' : 'Low usage'}
                    </span>
                </div>
            </div>

            {/* Action Buttons Row */}
            <div className="flex justify-end gap-2 mt-4">
                {isConfirmingDelete ? (
                    <>
                        <span className="text-xs text-red-300 mr-2 self-center">Delete this key?</span>
                        <button
                            onClick={handleConfirmDelete}
                            className="px-2 py-2 text-xs bg-red-600 text-white rounded hover:bg-red-500 transition-colors"
                            title="Confirm Delete"
                        >
                            Yes
                        </button>
                        <button
                            onClick={() => setIsConfirmingDelete(false)}
                            className="px-2 py-2 text-xs bg-gray-600 text-white rounded hover:bg-gray-500 transition-colors"
                            title="Cancel Delete"
                        >
                            Cancel
                        </button>
                    </>
                ) : (
                    <>
                        {!isActive && (
                            <button
                                onClick={onSetActive}
                                className="px-2 py-2 text-xs bg-gray-600 text-white rounded hover:bg-gray-500 transition-colors"
                                title="Set as active key"
                            >
                                Set Active
                            </button>
                        )}
                        <button
                            onClick={() => onSetEditing(apiKey.id)}
                            className="px-2 py-2 text-xs bg-gray-600 text-white rounded hover:bg-gray-500 transition-colors"
                            title="Rename key"
                        >
                            Rename
                        </button>
                        <button
                            onClick={() => setIsConfirmingDelete(true)}
                            className="px-2 py-2 text-xs bg-red-600 text-white rounded hover:bg-red-500 transition-colors"
                            title="Delete key"
                        >
                            Delete
                        </button>
                    </>
                )}
            </div>
        </div>
    );
};
