
import React, { useState, useEffect, useCallback } from 'react';
import { EyeIcon, EyeOffIcon, PlusIcon, TrashIcon, RenameIcon, CheckIcon, CloseIcon } from './Icons';
import type { <PERSON>pi<PERSON><PERSON> } from '../types';
import { apiKeyService, ACTIVE_API_KEY_ID_STORAGE_KEY } from '../services/apiKeyService';
import { ApiKeyQuotaCard } from './ApiKeyQuotaCard';
import { QuotaManagementDashboard } from './QuotaManagementDashboard';

export const SettingsPage: React.FC = () => {
    const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
    const [activeKeyId, setActiveKeyId] = useState<string | null>(null);
    const [visibleKeyIds, setVisibleKeyIds] = useState<Set<string>>(new Set());
    const [editingKeyId, setEditingKeyId] = useState<string | null>(null);

    const [newKeyName, setNewKeyName] = useState('');
    const [newKeyValue, setNewKeyValue] = useState('');
    const [isAdding, setIsAdding] = useState(false);
    const [loading, setLoading] = useState(true);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const [showQuotaDashboard, setShowQuotaDashboard] = useState(false);

    const loadApiKeys = useCallback(async () => {
        try {
            setLoading(true);
            const keys = await apiKeyService.getApiKeys();
            setApiKeys(keys);

            const storedActiveId = localStorage.getItem(ACTIVE_API_KEY_ID_STORAGE_KEY);
            if (storedActiveId && keys.some(k => k.id === storedActiveId)) {
                setActiveKeyId(storedActiveId);
            } else if (keys.length > 0) {
                const firstKeyId = keys[0].id;
                setActiveKeyId(firstKeyId);
                localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, firstKeyId);
            }

        } catch (err) {
            setErrorMessage((err as Error).message);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        loadApiKeys();
    }, [loadApiKeys]);

    const displayError = (message: string) => {
        setErrorMessage(message);
        setTimeout(() => setErrorMessage(null), 4000);
    };

    const handleAddKey = async () => {
        const trimmedKeyName = newKeyName.trim();
        const trimmedKeyValue = newKeyValue.trim();
        
        if (!trimmedKeyName || !trimmedKeyValue) {
            displayError("Please provide both a name and a value for the API key.");
            return;
        }

        setIsAdding(true);
        setErrorMessage(null);

        try {
            const newKey = await apiKeyService.addApiKey(trimmedKeyName, trimmedKeyValue);
            const updatedKeys = [...apiKeys, newKey];
            setApiKeys(updatedKeys);
            
            if (!activeKeyId) {
                setActiveKeyId(newKey.id);
                localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, newKey.id);
            }
            
            setNewKeyName('');
            setNewKeyValue('');
        } catch (err) {
            displayError((err as Error).message);
        } finally {
            setIsAdding(false);
        }
    };

    const handleSetActive = (id: string) => {
        setActiveKeyId(id);
        localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, id);
    };

    const handleSaveRename = async (id: string, newName: string) => {
        try {
            const updatedKey = await apiKeyService.renameApiKey(id, newName);
            setApiKeys(apiKeys.map(k => k.id === id ? updatedKey : k));
        } catch (err) {
            displayError((err as Error).message);
        } finally {
            setEditingKeyId(null);
        }
    };
    
    const handleDeleteKey = async (id: string) => {
        try {
            await apiKeyService.deleteApiKey(id);
            const updatedKeys = apiKeys.filter(k => k.id !== id);
            setApiKeys(updatedKeys);

            if (activeKeyId === id) {
                const newActiveId = updatedKeys.length > 0 ? updatedKeys[0].id : null;
                setActiveKeyId(newActiveId);
                if (newActiveId) {
                     localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, newActiveId);
                } else {
                    localStorage.removeItem(ACTIVE_API_KEY_ID_STORAGE_KEY);
                }
            }
        } catch (err) {
            displayError((err as Error).message);
        }
    };
    
    const toggleVisibility = (id: string) => {
        setVisibleKeyIds(prev => {
            const newSet = new Set(prev);
            if (newSet.has(id)) {
                newSet.delete(id);
            } else {
                newSet.add(id);
            }
            return newSet;
        });
    };

    return (
        <div className="min-h-screen bg-darkbg">
            <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
                {/* Page Header */}
                <header className="mb-8">
                    <h2 className="text-3xl font-bold text-white mb-2">Settings</h2>
                    <p className="text-gray-400">Manage your API keys and monitor quota usage</p>
                </header>

                {/* Navigation Tabs */}
                <div className="mb-8">
                    <div className="flex space-x-1 bg-dark-card border border-dark-border rounded-xl p-1 max-w-sm">
                        <button
                            onClick={() => setShowQuotaDashboard(false)}
                            className={`flex-1 px-6 py-3 text-sm font-semibold rounded-lg transition-all duration-200 ${
                                !showQuotaDashboard
                                    ? 'bg-accent text-darkbg shadow-lg'
                                    : 'text-gray-400 hover:text-white hover:bg-gray-700/30'
                            }`}
                        >
                            API Keys
                        </button>
                        <button
                            onClick={() => setShowQuotaDashboard(true)}
                            className={`flex-1 px-6 py-3 text-sm font-semibold rounded-lg transition-all duration-200 ${
                                showQuotaDashboard
                                    ? 'bg-accent text-darkbg shadow-lg'
                                    : 'text-gray-400 hover:text-white hover:bg-gray-700/30'
                            }`}
                        >
                            Quota Management
                        </button>
                    </div>
                </div>

                {showQuotaDashboard ? (
                    <QuotaManagementDashboard />
                ) : (
                    <div className="space-y-8">
                        {/* Add New API Key Section */}
                        <div className="bg-gradient-to-br from-dark-card/90 to-dark-card/70 border border-dark-border/50 rounded-xl p-8 shadow-xl">
                            <div className="mb-6">
                                <h3 className="text-2xl font-bold text-white mb-3">Add New API Key</h3>
                                <p className="text-gray-400 leading-relaxed">Your API keys are stored securely in your Supabase database with encryption.</p>
                            </div>

                            {errorMessage && (
                                <div className="bg-red-500/10 border border-red-500/30 text-red-300 px-4 py-3 rounded-xl mb-6 text-sm flex items-start gap-3" role="alert">
                                    <svg className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                    </svg>
                                    <span>{errorMessage}</span>
                                </div>
                            )}

                            <div className="space-y-6">
                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                    <div className="lg:col-span-1">
                                        <label htmlFor="key-name" className="block text-sm font-semibold text-gray-300 mb-3">
                                            Key Name
                                        </label>
                                        <input
                                            id="key-name"
                                            type="text"
                                            value={newKeyName}
                                            onChange={e => setNewKeyName(e.target.value)}
                                            placeholder="e.g., 'Personal Key'"
                                            className="w-full bg-[#1a1a1a] border border-gray-600/50 rounded-xl px-4 py-3.5 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent transition-all duration-200"
                                        />
                                    </div>
                                    <div className="lg:col-span-2">
                                        <label htmlFor="key-value" className="block text-sm font-semibold text-gray-300 mb-3">
                                            API Key Value
                                        </label>
                                        <input
                                            id="key-value"
                                            type="password"
                                            value={newKeyValue}
                                            onChange={e => setNewKeyValue(e.target.value)}
                                            placeholder="Enter your YouTube API key"
                                            className="w-full bg-[#1a1a1a] border border-gray-600/50 rounded-xl px-4 py-3.5 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent transition-all duration-200"
                                        />
                                    </div>
                                </div>

                                <div className="flex justify-end pt-4">
                                    <button
                                        onClick={handleAddKey}
                                        disabled={isAdding || !newKeyName.trim() || !newKeyValue.trim()}
                                        className="flex items-center gap-3 px-6 py-3.5 rounded-xl bg-accent text-darkbg font-bold hover:bg-accent-hover disabled:bg-gray-600 disabled:text-gray-400 transition-all duration-200 shadow-lg hover:shadow-xl disabled:shadow-none"
                                    >
                                        <PlusIcon className="w-5 h-5"/>
                                        <span>{isAdding ? 'Adding...' : 'Add Key'}</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Your API Keys Section */}
                        <div className="space-y-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-2xl font-bold text-white mb-2">Your API Keys</h3>
                                    <p className="text-gray-400">Manage and monitor your YouTube API keys</p>
                                </div>
                                {apiKeys.length > 0 && (
                                    <div className="text-sm text-gray-400">
                                        {apiKeys.length} key{apiKeys.length !== 1 ? 's' : ''} total
                                    </div>
                                )}
                            </div>

                            <div className="space-y-6">
                                {loading ? (
                                    <div className="space-y-4">
                                        {[1, 2].map(i => (
                                            <div key={i} className="bg-dark-card border border-dark-border rounded-xl p-6 animate-pulse">
                                                <div className="flex items-center justify-between mb-4">
                                                    <div className="h-4 bg-gray-700 rounded w-1/4"></div>
                                                    <div className="h-6 bg-gray-700 rounded w-16"></div>
                                                </div>
                                                <div className="h-3 bg-gray-700 rounded w-3/4 mb-4"></div>
                                                <div className="h-2 bg-gray-700 rounded w-full"></div>
                                            </div>
                                        ))}
                                    </div>
                                ) : apiKeys.length > 0 ? (
                                    <div className="grid gap-6">
                                        {apiKeys.map(apiKey => (
                                            <div key={apiKey.id} className="bg-gradient-to-br from-dark-card/90 to-dark-card/70 border border-dark-border/50 rounded-xl overflow-hidden shadow-xl">
                                                <div className="p-6">
                                                    <ApiKeyListItem
                                                        apiKey={apiKey}
                                                        isActive={activeKeyId === apiKey.id}
                                                        isVisible={visibleKeyIds.has(apiKey.id)}
                                                        isEditing={editingKeyId === apiKey.id}
                                                        onSetActive={() => handleSetActive(apiKey.id)}
                                                        onSetEditing={setEditingKeyId}
                                                        onSaveRename={handleSaveRename}
                                                        onDelete={() => handleDeleteKey(apiKey.id)}
                                                        onToggleVisibility={() => toggleVisibility(apiKey.id)}
                                                    />
                                                </div>
                                                <div className="border-t border-dark-border/30 p-6 bg-dark-card/30">
                                                    <ApiKeyQuotaCard
                                                        apiKey={apiKey}
                                                        showResetButton={true}
                                                        onQuotaReset={() => {
                                                            // Refresh API keys after quota reset
                                                            loadApiKeys();
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="bg-gradient-to-br from-dark-card/50 to-dark-card/30 border border-dark-border/30 rounded-xl p-12 text-center">
                                        <div className="w-16 h-16 mx-auto mb-6 bg-gray-700/30 rounded-full flex items-center justify-center">
                                            <svg className="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                                            </svg>
                                        </div>
                                        <h4 className="text-xl font-semibold text-white mb-2">No API keys found</h4>
                                        <p className="text-gray-400 mb-6 max-w-md mx-auto">Add your first YouTube API key above to start tracking channels and managing your quota.</p>
                                        <button
                                            onClick={() => document.getElementById('key-name')?.focus()}
                                            className="text-accent hover:text-accent-hover font-medium transition-colors"
                                        >
                                            Add your first API key →
                                        </button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

interface ApiKeyListItemProps {
    apiKey: ApiKey;
    isActive: boolean;
    isVisible: boolean;
    isEditing: boolean;
    onSetActive: () => void;
    onSaveRename: (id: string, newName: string) => void;
    onDelete: () => void;
    onToggleVisibility: () => void;
    onSetEditing: (id: string | null) => void;
}

const MAX_REQUESTS = 10000;

const ApiKeyListItem: React.FC<ApiKeyListItemProps> = ({ apiKey, isActive, isVisible, isEditing, onSetActive, onSaveRename, onDelete, onToggleVisibility, onSetEditing }) => {
    const [editedName, setEditedName] = useState(apiKey.name);
    const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);

    useEffect(() => {
        if (isEditing) {
            setEditedName(apiKey.name);
        }
    }, [isEditing, apiKey.name]);
    
    const handleSave = () => {
        const trimmedName = editedName.trim();
        if (trimmedName && trimmedName !== apiKey.name) {
            onSaveRename(apiKey.id, trimmedName);
        } else {
            onSetEditing(null); 
        }
    };
    
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') handleSave();
        if (e.key === 'Escape') onSetEditing(null);
    };

    const handleConfirmDelete = () => {
        onDelete();
        setIsConfirmingDelete(false);
    };

    const progressPercentage = (apiKey.usage / MAX_REQUESTS) * 100;
    let progressColorClass = 'bg-green-500';
    if (progressPercentage > 80) {
        progressColorClass = 'bg-red-500';
    } else if (progressPercentage > 50) {
        progressColorClass = 'bg-yellow-500';
    }

    if (isEditing) {
        return (
            <div className="bg-transparent border-2 border-accent/50 rounded-xl p-6 shadow-lg ring-2 ring-accent/20">
                <div className="flex items-center gap-6">
                    <div className="flex-grow space-y-4">
                        <div>
                            <label className="block text-sm font-semibold text-gray-300 mb-2">Key Name</label>
                            <input
                                type="text"
                                value={editedName}
                                onChange={(e) => setEditedName(e.target.value)}
                                onKeyDown={handleKeyDown}
                                className="w-full bg-[#1a1a1a] border border-gray-600/50 rounded-xl px-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent transition-all duration-200"
                                autoFocus
                                onFocus={(e) => e.target.select()}
                                placeholder="Enter key name"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-semibold text-gray-300 mb-2">API Key</label>
                            <p className="text-sm text-gray-400 font-mono bg-[#1a1a1a] border border-gray-600/30 rounded-xl px-4 py-3">
                                {isVisible ? apiKey.key : '•'.repeat(20) + apiKey.key.slice(-4)}
                            </p>
                        </div>
                    </div>
                    <div className="flex flex-col gap-3 flex-shrink-0">
                        <button
                            onClick={handleSave}
                            className="flex items-center gap-2 px-4 py-2.5 text-green-400 hover:text-green-300 hover:bg-green-500/10 rounded-xl transition-all duration-200 font-medium"
                            title="Save changes"
                        >
                            <CheckIcon className="w-5 h-5" />
                            Save
                        </button>
                        <button
                            onClick={() => onSetEditing(null)}
                            className="flex items-center gap-2 px-4 py-2.5 text-gray-400 hover:text-white hover:bg-gray-700/30 rounded-xl transition-all duration-200 font-medium"
                            title="Cancel editing"
                        >
                            <CloseIcon className="w-5 h-5" />
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        );
    }
    
    return (
        <div className={`bg-transparent transition-all duration-300 ${isActive ? 'ring-2 ring-accent/40 bg-accent/5' : ''}`}>
            <div className="flex items-start justify-between gap-8">
                <div className="flex-grow space-y-5">
                    {/* Key Info Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <h4 className="text-2xl font-bold text-white tracking-tight">{apiKey.name}</h4>
                            {isActive && (
                                <span className="flex items-center gap-2 text-sm font-bold bg-accent text-darkbg px-4 py-2 rounded-full shadow-lg shadow-accent/20 border border-accent/20">
                                    <div className="w-2.5 h-2.5 bg-darkbg rounded-full animate-pulse"></div>
                                    Active
                                </span>
                            )}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-400 bg-gray-800/30 px-3 py-1.5 rounded-lg">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            {new Date(apiKey.created_at).toLocaleDateString()}
                        </div>
                    </div>

                    {/* API Key Display */}
                    <div className="bg-gradient-to-r from-gray-900/80 to-gray-800/60 border border-gray-600/40 rounded-xl px-5 py-4 shadow-inner">
                        <div className="flex items-center justify-between gap-4">
                            <div className="flex-1 min-w-0">
                                <div className="text-xs text-gray-400 uppercase tracking-wider font-semibold mb-2">API Key</div>
                                <p className="text-base text-gray-300 font-mono tracking-wider break-all">
                                    {isVisible ? apiKey.key : '•'.repeat(40) + apiKey.key.slice(-8)}
                                </p>
                            </div>
                            <button
                                onClick={onToggleVisibility}
                                className="flex items-center gap-2 px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-700/40 rounded-lg transition-all duration-200 flex-shrink-0"
                                title={isVisible ? "Hide API key" : "Show API key"}
                            >
                                {isVisible ? <EyeOffIcon className="w-4 h-4" /> : <EyeIcon className="w-4 h-4" />}
                                <span className="text-sm font-medium">{isVisible ? 'Hide' : 'Show'}</span>
                            </button>
                        </div>
                    </div>

                    {/* Usage Progress */}
                    <div className="bg-gradient-to-br from-gray-900/40 to-gray-800/20 border border-gray-700/30 rounded-xl p-5 space-y-4">
                        <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                                <span className="text-lg font-bold text-white">Usage Statistics</span>
                            </div>
                            <div className="text-right">
                                <div className="text-lg font-bold text-white">
                                    {apiKey.usage.toLocaleString()} <span className="text-gray-400 text-sm font-normal">/ {MAX_REQUESTS.toLocaleString()}</span>
                                </div>
                                <div className="text-xs text-gray-400">requests</div>
                            </div>
                        </div>

                        <div className="relative">
                            <div className="w-full bg-gray-700/60 rounded-full h-4 overflow-hidden shadow-inner">
                                <div
                                    className={`h-full rounded-full transition-all duration-700 ease-out ${progressColorClass} shadow-lg relative overflow-hidden`}
                                    style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                                >
                                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                                </div>
                            </div>
                            {progressPercentage > 0 && (
                                <div
                                    className="absolute top-0 h-4 w-0.5 bg-white/40 rounded-full transition-all duration-700 shadow-sm"
                                    style={{ left: `${Math.min(progressPercentage, 100)}%` }}
                                ></div>
                            )}
                        </div>

                        <div className="flex justify-between items-center">
                            <div className="flex items-center gap-2">
                                <div className={`w-3 h-3 rounded-full ${progressPercentage > 80 ? 'bg-red-400' : progressPercentage > 50 ? 'bg-yellow-400' : 'bg-green-400'} shadow-lg`}></div>
                                <span className="text-sm font-semibold text-gray-300">{progressPercentage.toFixed(1)}% used</span>
                            </div>
                            <span className={`text-sm font-bold px-3 py-1 rounded-full ${
                                progressPercentage > 80 ? 'text-red-300 bg-red-500/20 border border-red-500/30' :
                                progressPercentage > 50 ? 'text-yellow-300 bg-yellow-500/20 border border-yellow-500/30' :
                                'text-green-300 bg-green-500/20 border border-green-500/30'
                            }`}>
                                {progressPercentage > 80 ? 'High usage' : progressPercentage > 50 ? 'Moderate usage' : 'Low usage'}
                            </span>
                        </div>
                    </div>
                </div>
                {/* Action Buttons */}
                <div className="flex flex-col gap-3 flex-shrink-0 min-w-[140px]">
                    {isConfirmingDelete ? (
                        <div className="bg-gradient-to-br from-red-500/20 to-red-600/10 border border-red-500/40 rounded-xl p-5 space-y-4 shadow-lg">
                            <div className="text-center">
                                <div className="w-12 h-12 mx-auto mb-3 bg-red-500/20 rounded-full flex items-center justify-center">
                                    <svg className="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                    </svg>
                                </div>
                                <p className="text-sm text-red-300 font-bold mb-1">Confirm Delete</p>
                                <p className="text-xs text-red-400/80">This action cannot be undone</p>
                            </div>
                            <div className="flex flex-col gap-2">
                                <button
                                    onClick={handleConfirmDelete}
                                    className="flex items-center justify-center gap-2 px-4 py-3 text-white bg-red-500 hover:bg-red-600 rounded-xl transition-all duration-200 text-sm font-bold shadow-lg hover:shadow-xl"
                                    title="Confirm Delete"
                                >
                                    <CheckIcon className="w-4 h-4" />
                                    Delete Key
                                </button>
                                <button
                                    onClick={() => setIsConfirmingDelete(false)}
                                    className="flex items-center justify-center gap-2 px-4 py-3 text-gray-400 hover:text-white hover:bg-gray-700/40 rounded-xl transition-all duration-200 text-sm font-medium border border-gray-600/30"
                                    title="Cancel Delete"
                                >
                                    <CloseIcon className="w-4 h-4" />
                                    Cancel
                                </button>
                            </div>
                        </div>
                    ) : (
                        <div className="space-y-3">
                            {!isActive && (
                                <button
                                    onClick={onSetActive}
                                    className="w-full flex items-center gap-3 px-5 py-3 text-accent hover:text-accent-hover hover:bg-accent/15 rounded-xl transition-all duration-200 text-sm font-bold border border-accent/30 hover:border-accent/50 shadow-md hover:shadow-lg"
                                    title="Set as active key"
                                >
                                    <CheckIcon className="w-4 h-4" />
                                    Set Active
                                </button>
                            )}
                            <button
                                onClick={() => onSetEditing(apiKey.id)}
                                className="w-full flex items-center gap-3 px-5 py-3 text-gray-300 hover:text-white hover:bg-gray-700/40 rounded-xl transition-all duration-200 text-sm font-medium border border-gray-600/30 hover:border-gray-500/50"
                                title="Rename key"
                            >
                                <RenameIcon className="w-4 h-4" />
                                Rename
                            </button>
                            <button
                                onClick={() => setIsConfirmingDelete(true)}
                                className="w-full flex items-center gap-3 px-5 py-3 text-red-400 hover:text-red-300 hover:bg-red-500/15 rounded-xl transition-all duration-200 text-sm font-medium border border-red-500/30 hover:border-red-500/50"
                                title="Delete key"
                            >
                                <TrashIcon className="w-4 h-4" />
                                Delete
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};
