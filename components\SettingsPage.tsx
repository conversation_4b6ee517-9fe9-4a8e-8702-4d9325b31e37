
import React, { useState, useEffect, useCallback } from 'react';
import { EyeIcon, EyeOffIcon, PlusIcon, TrashIcon, RenameIcon, CheckIcon, CloseIcon } from './Icons';
import type { <PERSON>pi<PERSON>ey } from '../types';
import { apiKeyService, ACTIVE_API_KEY_ID_STORAGE_KEY } from '../services/apiKeyService';

export const SettingsPage: React.FC = () => {
    const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
    const [activeKeyId, setActiveKeyId] = useState<string | null>(null);
    const [visibleKeyIds, setVisibleKeyIds] = useState<Set<string>>(new Set());
    const [editingKeyId, setEditingKeyId] = useState<string | null>(null);

    const [newKeyName, setNewKeyName] = useState('');
    const [newKeyValue, setNewKeyValue] = useState('');
    const [isAdding, setIsAdding] = useState(false);
    const [loading, setLoading] = useState(true);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);

    const loadApiKeys = useCallback(async () => {
        try {
            setLoading(true);
            const keys = await apiKeyService.getApiKeys();
            setApiKeys(keys);

            const storedActiveId = localStorage.getItem(ACTIVE_API_KEY_ID_STORAGE_KEY);
            if (storedActiveId && keys.some(k => k.id === storedActiveId)) {
                setActiveKeyId(storedActiveId);
            } else if (keys.length > 0) {
                const firstKeyId = keys[0].id;
                setActiveKeyId(firstKeyId);
                localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, firstKeyId);
            }

        } catch (err) {
            setErrorMessage((err as Error).message);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        loadApiKeys();
    }, [loadApiKeys]);

    const displayError = (message: string) => {
        setErrorMessage(message);
        setTimeout(() => setErrorMessage(null), 4000);
    };

    const handleAddKey = async () => {
        const trimmedKeyName = newKeyName.trim();
        const trimmedKeyValue = newKeyValue.trim();
        
        if (!trimmedKeyName || !trimmedKeyValue) {
            displayError("Please provide both a name and a value for the API key.");
            return;
        }

        setIsAdding(true);
        setErrorMessage(null);

        try {
            const newKey = await apiKeyService.addApiKey(trimmedKeyName, trimmedKeyValue);
            const updatedKeys = [...apiKeys, newKey];
            setApiKeys(updatedKeys);
            
            if (!activeKeyId) {
                setActiveKeyId(newKey.id);
                localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, newKey.id);
            }
            
            setNewKeyName('');
            setNewKeyValue('');
        } catch (err) {
            displayError((err as Error).message);
        } finally {
            setIsAdding(false);
        }
    };

    const handleSetActive = (id: string) => {
        setActiveKeyId(id);
        localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, id);
    };

    const handleSaveRename = async (id: string, newName: string) => {
        try {
            const updatedKey = await apiKeyService.renameApiKey(id, newName);
            setApiKeys(apiKeys.map(k => k.id === id ? updatedKey : k));
        } catch (err) {
            displayError((err as Error).message);
        } finally {
            setEditingKeyId(null);
        }
    };
    
    const handleDeleteKey = async (id: string) => {
        try {
            await apiKeyService.deleteApiKey(id);
            const updatedKeys = apiKeys.filter(k => k.id !== id);
            setApiKeys(updatedKeys);

            if (activeKeyId === id) {
                const newActiveId = updatedKeys.length > 0 ? updatedKeys[0].id : null;
                setActiveKeyId(newActiveId);
                if (newActiveId) {
                     localStorage.setItem(ACTIVE_API_KEY_ID_STORAGE_KEY, newActiveId);
                } else {
                    localStorage.removeItem(ACTIVE_API_KEY_ID_STORAGE_KEY);
                }
            }
        } catch (err) {
            displayError((err as Error).message);
        }
    };
    
    const toggleVisibility = (id: string) => {
        setVisibleKeyIds(prev => {
            const newSet = new Set(prev);
            if (newSet.has(id)) {
                newSet.delete(id);
            } else {
                newSet.add(id);
            }
            return newSet;
        });
    };

    return (
        <div className="p-4 sm:p-6 lg:p-8 space-y-8">
            <header>
                <h2 className="text-3xl font-bold text-white">Setting</h2>
            </header>

            <div className="max-w-3xl mx-auto bg-dark-card border border-dark-border rounded-lg p-6">
                 <h3 className="text-xl font-semibold text-white mb-2">Add New API Key</h3>
                 <p className="text-gray-400 mb-6">Your API keys are stored securely in your Supabase database.</p>
                 
                 {errorMessage && (
                    <div className="bg-red-500/10 border border-red-500/30 text-red-300 px-4 py-3 rounded-lg mb-4 text-sm" role="alert">
                        {errorMessage}
                    </div>
                 )}
                 
                 <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                    <div className="md:col-span-1">
                        <label htmlFor="key-name" className="block text-sm font-medium text-gray-300 mb-2">Key Name</label>
                        <input
                            id="key-name"
                            type="text"
                            value={newKeyName}
                            onChange={e => setNewKeyName(e.target.value)}
                            placeholder="e.g., 'Personal Key'"
                            className="w-full bg-[#1a1a1a] border border-gray-600 rounded-lg px-4 py-2.5 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent"
                        />
                    </div>
                    <div className="md:col-span-2">
                        <label htmlFor="key-value" className="block text-sm font-medium text-gray-300 mb-2">API Key Value</label>
                        <input
                            id="key-value"
                            type="password"
                            value={newKeyValue}
                            onChange={e => setNewKeyValue(e.target.value)}
                            placeholder="Enter your API key"
                            className="w-full bg-[#1a1a1a] border border-gray-600 rounded-lg px-4 py-2.5 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent"
                        />
                    </div>
                 </div>
                 <div className="flex justify-end mt-4">
                     <button onClick={handleAddKey} disabled={isAdding || !newKeyName.trim() || !newKeyValue.trim()} className="flex items-center space-x-2 px-4 py-2.5 rounded-lg bg-accent text-darkbg font-semibold hover:bg-accent-hover disabled:bg-gray-600 disabled:text-gray-400 transition-colors">
                        <PlusIcon className="w-5 h-5"/>
                        <span>{isAdding ? 'Adding...' : 'Add Key'}</span>
                    </button>
                 </div>
            </div>

            <div className="max-w-3xl mx-auto">
                <h3 className="text-xl font-semibold text-white mb-4">Your API Keys</h3>
                <div className="space-y-3">
                    {loading ? (
                        <p className="text-gray-400">Loading keys...</p>
                    ) : apiKeys.length > 0 ? apiKeys.map(apiKey => (
                        <ApiKeyListItem
                            key={apiKey.id}
                            apiKey={apiKey}
                            isActive={activeKeyId === apiKey.id}
                            isVisible={visibleKeyIds.has(apiKey.id)}
                            isEditing={editingKeyId === apiKey.id}
                            onSetActive={() => handleSetActive(apiKey.id)}
                            onSetEditing={setEditingKeyId}
                            onSaveRename={handleSaveRename}
                            onDelete={() => handleDeleteKey(apiKey.id)}
                            onToggleVisibility={() => toggleVisibility(apiKey.id)}
                        />
                    )) : (
                        <div className="text-center py-10 bg-dark-card border border-dashed border-dark-border rounded-lg">
                            <p className="text-gray-500">No API keys have been added yet.</p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

interface ApiKeyListItemProps {
    apiKey: ApiKey;
    isActive: boolean;
    isVisible: boolean;
    isEditing: boolean;
    onSetActive: () => void;
    onSaveRename: (id: string, newName: string) => void;
    onDelete: () => void;
    onToggleVisibility: () => void;
    onSetEditing: (id: string | null) => void;
}

const MAX_REQUESTS = 10000;

const ApiKeyListItem: React.FC<ApiKeyListItemProps> = ({ apiKey, isActive, isVisible, isEditing, onSetActive, onSaveRename, onDelete, onToggleVisibility, onSetEditing }) => {
    const [editedName, setEditedName] = useState(apiKey.name);
    const [isConfirmingDelete, setIsConfirmingDelete] = useState(false);

    useEffect(() => {
        if (isEditing) {
            setEditedName(apiKey.name);
        }
    }, [isEditing, apiKey.name]);
    
    const handleSave = () => {
        const trimmedName = editedName.trim();
        if (trimmedName && trimmedName !== apiKey.name) {
            onSaveRename(apiKey.id, trimmedName);
        } else {
            onSetEditing(null); 
        }
    };
    
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') handleSave();
        if (e.key === 'Escape') onSetEditing(null);
    };

    const handleConfirmDelete = () => {
        onDelete();
        setIsConfirmingDelete(false);
    };

    const progressPercentage = (apiKey.usage / MAX_REQUESTS) * 100;
    let progressColorClass = 'bg-green-500';
    if (progressPercentage > 80) {
        progressColorClass = 'bg-red-500';
    } else if (progressPercentage > 50) {
        progressColorClass = 'bg-yellow-500';
    }

    if (isEditing) {
        return (
            <div className="p-4 rounded-lg flex items-center gap-4 bg-dark-card border border-accent ring-2 ring-accent/50">
                <div className="flex-grow space-y-1">
                    <input
                        type="text"
                        value={editedName}
                        onChange={(e) => setEditedName(e.target.value)}
                        onKeyDown={handleKeyDown}
                        className="w-full bg-[#1a1a1a] border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent"
                        autoFocus
                        onFocus={(e) => e.target.select()}
                    />
                    <p className="text-sm text-gray-400 font-mono">
                        {isVisible ? apiKey.key : '•'.repeat(20) + apiKey.key.slice(-4)}
                    </p>
                </div>
                <div className="flex items-center gap-2 flex-shrink-0">
                    <button onClick={handleSave} className="p-2 text-green-400 hover:text-green-300 hover:bg-green-500/10 rounded-md transition-colors" title="Save name">
                        <CheckIcon className="w-5 h-5" />
                    </button>
                    <button onClick={() => onSetEditing(null)} className="p-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded-md transition-colors" title="Cancel edit">
                        <CloseIcon className="w-5 h-5" />
                    </button>
                </div>
            </div>
        );
    }
    
    return (
        <div className={`p-4 rounded-lg flex items-center gap-4 transition-colors duration-200 ${isActive ? 'bg-accent/10 border border-accent/30' : 'bg-dark-card border border-dark-border'}`}>
            <div className="flex-grow space-y-2">
                <div>
                    <p className="font-semibold text-white flex items-center">
                        {apiKey.name}
                        {isActive && <span className="ml-2 text-xs font-semibold bg-accent text-darkbg px-2 py-0.5 rounded-full">Active</span>}
                    </p>
                    <p className="text-sm text-gray-400 font-mono">
                        {isVisible ? apiKey.key : '•'.repeat(20) + apiKey.key.slice(-4)}
                    </p>
                </div>
                <div>
                    <div className="flex justify-between items-center text-xs text-gray-400 mb-1">
                        <span>How much did you consume? quota</span>
                        <span>{apiKey.usage.toLocaleString()} / {MAX_REQUESTS.toLocaleString()}</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-1.5">
                        <div className={`h-1.5 rounded-full ${progressColorClass}`} style={{ width: `${progressPercentage}%` }}></div>
                    </div>
                </div>
            </div>
            <div className="flex items-center gap-2 flex-shrink-0">
                {isConfirmingDelete ? (
                     <>
                        <span className="text-sm text-red-300 animate-pulse mr-2">Delete?</span>
                        <button onClick={handleConfirmDelete} className="p-2 text-green-400 hover:text-green-300 hover:bg-green-500/10 rounded-md transition-colors" title="Confirm Delete">
                           <CheckIcon className="w-5 h-5" />
                        </button>
                        <button onClick={() => setIsConfirmingDelete(false)} className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-md transition-colors" title="Cancel Delete">
                            <CloseIcon className="w-5 h-5" />
                        </button>
                    </>
                ) : (
                    <>
                        {!isActive && (
                            <button onClick={onSetActive} className="p-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded-md transition-colors" title="Set as active key">
                                <CheckIcon className="w-5 h-5" />
                            </button>
                        )}
                         <button onClick={onToggleVisibility} className="p-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded-md transition-colors" title={isVisible ? 'Hide key' : 'Show key'}>
                            {isVisible ? <EyeOffIcon className="w-5 h-5" /> : <EyeIcon className="w-5 h-5" />}
                        </button>
                         <button onClick={() => onSetEditing(apiKey.id)} className="p-2 text-gray-300 hover:text-white hover:bg-gray-700/50 rounded-md transition-colors" title="Rename key">
                            <RenameIcon className="w-5 h-5" />
                        </button>
                         <button onClick={() => setIsConfirmingDelete(true)} className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-md transition-colors" title="Delete key">
                            <TrashIcon className="w-5 h-5" />
                        </button>
                    </>
                )}
            </div>
        </div>
    );
};
