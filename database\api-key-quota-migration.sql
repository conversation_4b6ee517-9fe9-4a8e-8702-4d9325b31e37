-- API Key Quota Management Migration
-- This script adds quota tracking fields to the api_keys table and sets up automatic daily reset functionality

-- 1. Add new columns to api_keys table for quota management
ALTER TABLE api_keys 
ADD COLUMN IF NOT EXISTS quota_limit INTEGER DEFAULT 10000,
ADD COLUMN IF NOT EXISTS last_reset_at TIMESTAMPTZ DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT NOW();

-- 2. Update existing records to have proper default values
UPDATE api_keys 
SET 
    quota_limit = 10000,
    last_reset_at = NOW(),
    created_at = NOW()
WHERE quota_limit IS NULL OR last_reset_at IS NULL OR created_at IS NULL;

-- 3. Add constraints to ensure data integrity
ALTER TABLE api_keys 
ADD CONSTRAINT check_quota_limit_positive CHECK (quota_limit > 0),
ADD CONSTRAINT check_usage_non_negative CHECK (usage >= 0),
ADD CONSTRAINT check_usage_within_limit CHECK (usage <= quota_limit * 2); -- Allow 2x limit for grace period

-- 4. Create index for efficient quota reset queries
CREATE INDEX IF NOT EXISTS idx_api_keys_last_reset_at ON api_keys(last_reset_at);
CREATE INDEX IF NOT EXISTS idx_api_keys_usage_quota ON api_keys(usage, quota_limit);

-- 5. Create function to reset API key quotas
CREATE OR REPLACE FUNCTION reset_api_key_quota(api_key_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE api_keys 
    SET 
        usage = 0,
        last_reset_at = NOW()
    WHERE id = api_key_id;
    
    -- Log the reset for monitoring
    INSERT INTO quota_reset_logs (api_key_id, reset_at, previous_usage)
    SELECT id, NOW(), usage 
    FROM api_keys 
    WHERE id = api_key_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Create function to reset all API key quotas (daily reset)
CREATE OR REPLACE FUNCTION reset_all_api_key_quotas()
RETURNS TABLE(reset_count INTEGER, total_keys INTEGER) AS $$
DECLARE
    reset_count INTEGER := 0;
    total_keys INTEGER := 0;
BEGIN
    -- Count total API keys
    SELECT COUNT(*) INTO total_keys FROM api_keys;
    
    -- Reset quotas for keys that need reset (older than 24 hours)
    WITH reset_keys AS (
        UPDATE api_keys 
        SET 
            usage = 0,
            last_reset_at = NOW()
        WHERE last_reset_at < NOW() - INTERVAL '24 hours'
        RETURNING id, usage
    )
    SELECT COUNT(*) INTO reset_count FROM reset_keys;
    
    -- Log the batch reset
    INSERT INTO quota_reset_logs (reset_at, batch_reset, keys_reset, total_keys)
    VALUES (NOW(), true, reset_count, total_keys);
    
    RETURN QUERY SELECT reset_count, total_keys;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Create quota reset logs table for monitoring
CREATE TABLE IF NOT EXISTS quota_reset_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    api_key_id UUID REFERENCES api_keys(id) ON DELETE CASCADE,
    reset_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    previous_usage INTEGER,
    batch_reset BOOLEAN DEFAULT false,
    keys_reset INTEGER,
    total_keys INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 8. Create index for efficient log queries
CREATE INDEX IF NOT EXISTS idx_quota_reset_logs_reset_at ON quota_reset_logs(reset_at);
CREATE INDEX IF NOT EXISTS idx_quota_reset_logs_api_key_id ON quota_reset_logs(api_key_id);

-- 9. Create function to check if quota reset is needed
CREATE OR REPLACE FUNCTION needs_quota_reset(api_key_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    last_reset TIMESTAMPTZ;
BEGIN
    SELECT last_reset_at INTO last_reset 
    FROM api_keys 
    WHERE id = api_key_id;
    
    RETURN (last_reset IS NULL OR last_reset < NOW() - INTERVAL '24 hours');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Create function to get quota status
CREATE OR REPLACE FUNCTION get_quota_status(api_key_id UUID)
RETURNS TABLE(
    current_usage INTEGER,
    quota_limit INTEGER,
    remaining_quota INTEGER,
    usage_percentage NUMERIC,
    last_reset_at TIMESTAMPTZ,
    next_reset_at TIMESTAMPTZ,
    needs_reset BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ak.usage,
        ak.quota_limit,
        GREATEST(0, ak.quota_limit - ak.usage) as remaining_quota,
        ROUND((ak.usage::NUMERIC / ak.quota_limit::NUMERIC) * 100, 2) as usage_percentage,
        ak.last_reset_at,
        ak.last_reset_at + INTERVAL '24 hours' as next_reset_at,
        (ak.last_reset_at < NOW() - INTERVAL '24 hours') as needs_reset
    FROM api_keys ak
    WHERE ak.id = api_key_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. Create trigger to automatically reset quota if needed on usage update
CREATE OR REPLACE FUNCTION auto_reset_quota_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if quota reset is needed before updating usage
    IF (OLD.last_reset_at < NOW() - INTERVAL '24 hours') THEN
        NEW.usage = 0;
        NEW.last_reset_at = NOW();
        
        -- Log the automatic reset
        INSERT INTO quota_reset_logs (api_key_id, reset_at, previous_usage)
        VALUES (NEW.id, NOW(), OLD.usage);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS auto_reset_quota_trigger ON api_keys;
CREATE TRIGGER auto_reset_quota_trigger
    BEFORE UPDATE OF usage ON api_keys
    FOR EACH ROW
    EXECUTE FUNCTION auto_reset_quota_trigger();

-- 12. Grant necessary permissions
GRANT EXECUTE ON FUNCTION reset_api_key_quota(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION reset_all_api_key_quotas() TO authenticated;
GRANT EXECUTE ON FUNCTION needs_quota_reset(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_quota_status(UUID) TO authenticated;

-- 13. Create RLS policies for quota_reset_logs
ALTER TABLE quota_reset_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own quota reset logs" ON quota_reset_logs
    FOR SELECT USING (true); -- Allow all authenticated users to view logs

CREATE POLICY "System can insert quota reset logs" ON quota_reset_logs
    FOR INSERT WITH CHECK (true); -- Allow system to insert logs

-- 14. Add helpful comments
COMMENT ON COLUMN api_keys.quota_limit IS 'Daily API quota limit (default: 10,000 requests)';
COMMENT ON COLUMN api_keys.last_reset_at IS 'Timestamp of last quota reset (resets every 24 hours)';
COMMENT ON COLUMN api_keys.usage IS 'Current usage count since last reset';
COMMENT ON TABLE quota_reset_logs IS 'Log of all quota resets for monitoring and debugging';
COMMENT ON FUNCTION reset_all_api_key_quotas() IS 'Resets quotas for all API keys older than 24 hours';
COMMENT ON FUNCTION get_quota_status(UUID) IS 'Returns comprehensive quota status for an API key';

-- 15. Success message
DO $$
BEGIN
    RAISE NOTICE '✅ API Key Quota Management Migration completed successfully!';
    RAISE NOTICE '   - Added quota_limit, last_reset_at, created_at columns';
    RAISE NOTICE '   - Created quota reset functions and triggers';
    RAISE NOTICE '   - Set up quota_reset_logs table for monitoring';
    RAISE NOTICE '   - Added data integrity constraints';
    RAISE NOTICE '   - Configured automatic daily quota resets';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 Next steps:';
    RAISE NOTICE '   1. Update your application code to use the new quota fields';
    RAISE NOTICE '   2. Set up a daily cron job to call reset_all_api_key_quotas()';
    RAISE NOTICE '   3. Monitor quota usage through the quota_reset_logs table';
END $$;
