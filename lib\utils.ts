

import { useState, useEffect, RefObject } from 'react';

export const formatNumber = (num: number | undefined | null): string => {
    if (num === undefined || num === null) return 'N/A';
    if (num >= 1_000_000_000) return `${(num / 1_000_000_000).toFixed(2)}B`;
    if (num >= 1_000_000) return `${(num / 1_000_000).toFixed(1)}M`;
    if (num >= 1_000) return `${(num / 1_000).toFixed(1)}K`;
    return num.toString();
};

export const parseFormattedNumber = (input: string): number | null => {
    const cleanedInput = input.trim().toLowerCase().replace(/,/g, '');
    if (!cleanedInput) return null;

    let multiplier = 1;
    let numberPartStr = cleanedInput;

    if (cleanedInput.endsWith('b')) {
        multiplier = 1_000_000_000;
        numberPartStr = cleanedInput.slice(0, -1);
    } else if (cleanedInput.endsWith('m')) {
        multiplier = 1_000_000;
        numberPartStr = cleanedInput.slice(0, -1);
    } else if (cleanedInput.endsWith('k')) {
        multiplier = 1_000;
        numberPartStr = cleanedInput.slice(0, -1);
    }

    const numberPart = parseFloat(numberPartStr);

    if (isNaN(numberPart)) {
        return null;
    }
    
    return Math.round(numberPart * multiplier);
};

export function useDebounce<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(handler);
        };
    }, [value, delay]);

    return debouncedValue;
}


export const formatTimeAgo = (isoDate: string): string => {
  if (!isoDate) return '';
  const date = new Date(isoDate);
  const now = new Date();
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (seconds < 60) return 'just now';
  
  const timeUnits = [
    { unit: 'year', seconds: 31536000 },
    { unit: 'month', seconds: 2592000 },
    { unit: 'day', seconds: 86400 },
    { unit: 'hour', seconds: 3600 },
    { unit: 'minute', seconds: 60 },
  ];

  for (const { unit, seconds: unitSeconds } of timeUnits) {
    const interval = Math.floor(seconds / unitSeconds);
    if (interval >= 1) {
      return `${interval} ${unit}${interval > 1 ? 's' : ''} ago`;
    }
  }
  return 'just now'; // Fallback
};

export const formatDuration = (isoDuration: string): string => {
  if (!isoDuration || isoDuration === 'P0D') return '';
  
  const match = isoDuration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
  if (!match) return '';

  const hours = parseInt(match[1]?.slice(0, -1) || '0', 10);
  const minutes = parseInt(match[2]?.slice(0, -1) || '0', 10);
  const seconds = parseInt(match[3]?.slice(0, -1) || '0', 10);

  const hStr = hours > 0 ? `${hours}:` : '';
  const mStr = hours > 0 ? minutes.toString().padStart(2, '0') : minutes.toString();
  const sStr = seconds.toString().padStart(2, '0');
  
  return `${hStr}${mStr}:${sStr}`;
};

export const formatChannelAge = (isoDate: string): string => {
    if (!isoDate) return '-';
    const now = new Date();
    const publishedDate = new Date(isoDate);
    const ageInYears = (now.getTime() - publishedDate.getTime()) / (1000 * 60 * 60 * 24 * 365.25);
  
    if (ageInYears < 1) {
      const ageInMonths = ageInYears * 12;
      if (ageInMonths < 1) return 'New';
      const roundedMonths = Math.floor(ageInMonths);
      return `${roundedMonths} month${roundedMonths > 1 ? 's' : ''}`;
    }
    
    const years = Math.floor(ageInYears);
    return `${years} year${years > 1 ? 's' : ''}`;
};


// Custom hook to detect clicks outside a ref's current element
export const useClickOutside = (ref: RefObject<HTMLElement>, handler: () => void) => {
    useEffect(() => {
        const listener = (event: MouseEvent | TouchEvent) => {
            if (!ref.current || ref.current.contains(event.target as Node)) {
                return;
            }
            handler();
        };
        document.addEventListener('mousedown', listener);
        document.addEventListener('touchstart', listener);
        return () => {
            document.removeEventListener('mousedown', listener);
            document.removeEventListener('touchstart', listener);
        };
    }, [ref, handler]);
};

export const parseISO8601DurationToMinutes = (isoDuration: string): number => {
  if (!isoDuration || isoDuration === 'P0D') return 0;
  
  // Regex to capture H, M, S components from strings like "PT1H2M3S"
  const match = isoDuration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  
  if (!match) return 0;

  const hours = parseInt(match[1] || '0', 10);
  const minutes = parseInt(match[2] || '0', 10);
  const seconds = parseInt(match[3] || '0', 10);
  
  const totalSeconds = (hours * 3600) + (minutes * 60) + seconds;
  
  // As per the prompt, round up to the nearest minute if there are any seconds.
  // e.g., PT5M30S (330s) becomes 6 minutes. PT5M0S (300s) becomes 5 minutes.
  return Math.ceil(totalSeconds / 60);
};
