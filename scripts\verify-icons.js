#!/usr/bin/env node

/**
 * Verification script for icon imports in VirSnapp
 * This script checks that all required icons are properly exported from the Icons component
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying icon imports for VirSnapp...\n');

// Required icons for TrackedChannelsPage
const requiredIcons = [
  'PlusIcon',
  'EllipsisVerticalIcon', 
  'StarIcon',
  'StarIconSolid',
  'PlayIcon',
  'EyeIcon'
];

// Read the Icons.tsx file
const iconsFilePath = path.join(__dirname, '../components/Icons.tsx');

try {
  const iconsFileContent = fs.readFileSync(iconsFilePath, 'utf8');
  
  console.log('✅ Icons.tsx file found and readable\n');
  
  // Check each required icon
  let allIconsFound = true;
  
  requiredIcons.forEach(iconName => {
    const exportPattern = new RegExp(`export const ${iconName}:`);
    const isExported = exportPattern.test(iconsFileContent);
    
    if (isExported) {
      console.log(`✅ ${iconName} - Exported correctly`);
    } else {
      console.log(`❌ ${iconName} - NOT FOUND`);
      allIconsFound = false;
    }
  });
  
  console.log('\n' + '='.repeat(50));
  
  if (allIconsFound) {
    console.log('🎉 SUCCESS: All required icons are properly exported!');
    console.log('\nNext steps:');
    console.log('1. Import TrackedChannelsPage in your app');
    console.log('2. All icons should work without Heroicons dependency');
    console.log('3. Run the IconsTest component to verify visually');
  } else {
    console.log('❌ FAILURE: Some icons are missing from exports');
    console.log('\nPlease check the Icons.tsx file and ensure all required icons are exported.');
  }
  
  // Check for react-icons imports
  console.log('\n📦 Checking react-icons imports...');
  
  const reactIconsImports = [
    'RiAddLine',      // PlusIcon
    'RiMore2Line',    // EllipsisVerticalIcon
    'RiStarLine',     // StarIcon
    'RiStarFill',     // StarIconSolid
    'RiPlayFill',     // PlayIcon
    'RiEyeLine'       // EyeIcon
  ];
  
  let allImportsFound = true;
  
  reactIconsImports.forEach(importName => {
    if (iconsFileContent.includes(importName)) {
      console.log(`✅ ${importName} - Imported correctly`);
    } else {
      console.log(`❌ ${importName} - NOT FOUND`);
      allImportsFound = false;
    }
  });
  
  if (allImportsFound) {
    console.log('\n✅ All react-icons imports are present');
  } else {
    console.log('\n❌ Some react-icons imports are missing');
  }
  
  // Check TrackedChannelsPage imports
  console.log('\n🔗 Checking TrackedChannelsPage imports...');
  
  const trackedChannelsPath = path.join(__dirname, '../components/TrackedChannelsPage.tsx');
  
  try {
    const trackedChannelsContent = fs.readFileSync(trackedChannelsPath, 'utf8');
    
    // Check for correct import statement
    const correctImport = /import\s*{\s*[^}]*}\s*from\s*['"]\.\/Icons['"];?/;
    const heroiconsImport = /import\s*{[^}]*}\s*from\s*['"]@heroicons\/react/;
    
    if (correctImport.test(trackedChannelsContent)) {
      console.log('✅ TrackedChannelsPage uses correct local Icons import');
    } else {
      console.log('❌ TrackedChannelsPage import statement needs fixing');
    }
    
    if (heroiconsImport.test(trackedChannelsContent)) {
      console.log('❌ TrackedChannelsPage still has Heroicons imports - needs cleanup');
    } else {
      console.log('✅ No Heroicons imports found in TrackedChannelsPage');
    }
    
  } catch (error) {
    console.log('❌ Could not read TrackedChannelsPage.tsx');
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('🔧 Icon Dependency Fix Summary:');
  console.log('• Replaced @heroicons/react with existing react-icons');
  console.log('• All icons mapped to equivalent react-icons components');
  console.log('• No new dependencies required');
  console.log('• Maintained visual consistency and functionality');
  console.log('\n📖 For detailed information, see: docs/heroicons-dependency-fix.md');
  
} catch (error) {
  console.error('❌ Error reading Icons.tsx file:', error.message);
  console.log('\nPlease ensure the Icons.tsx file exists in the components directory.');
  process.exit(1);
}

console.log('\n🎯 Verification complete!');
