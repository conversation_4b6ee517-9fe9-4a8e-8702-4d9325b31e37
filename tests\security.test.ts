/**
 * Security Test Suite
 * Tests for all security implementations
 * 
 * Note: This is a basic test suite. In production, you would use a proper testing framework
 * like <PERSON><PERSON>, <PERSON>ites<PERSON>, or <PERSON>press for comprehensive testing.
 */

import { 
  validateName, 
  validateYouTubeVideoUrl, 
  validateYouTubeChannelQuery,
  validateNotes,
  validateNumericInput,
  validateApiKey
} from '../lib/inputValidation';

import {
  validateApiKeyFormat,
  encryptApiKey,
  decryptApiKey,
  validateApiUsage,
  API_USAGE_LIMITS
} from '../lib/apiKeySecurity';

import { validateEnvironmentVariables } from '../lib/env';

// Test results interface
interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
}

/**
 * Runs all security tests
 */
export const runSecurityTests = (): TestResult[] => {
  const results: TestResult[] = [];
  
  // Input Validation Tests
  results.push(...testInputValidation());
  
  // API Key Security Tests
  results.push(...testApiKeySecurity());
  
  // Environment Variable Tests
  results.push(...testEnvironmentVariables());
  
  return results;
};

/**
 * Tests input validation functions
 */
const testInputValidation = (): TestResult[] => {
  const tests: TestResult[] = [];
  
  // Test name validation
  tests.push({
    testName: 'Name Validation - Valid Input',
    passed: validateName('Valid Channel Name').isValid
  });
  
  tests.push({
    testName: 'Name Validation - Empty Input',
    passed: !validateName('').isValid
  });
  
  tests.push({
    testName: 'Name Validation - Too Long',
    passed: !validateName('a'.repeat(101)).isValid
  });
  
  tests.push({
    testName: 'Name Validation - SQL Injection',
    passed: !validateName("'; DROP TABLE channels; --").isValid
  });
  
  tests.push({
    testName: 'Name Validation - XSS Attempt',
    passed: !validateName('<script>alert("xss")</script>').isValid
  });
  
  // Test YouTube URL validation
  tests.push({
    testName: 'YouTube URL - Valid Video URL',
    passed: validateYouTubeVideoUrl('https://www.youtube.com/watch?v=dQw4w9WgXcQ').isValid
  });
  
  tests.push({
    testName: 'YouTube URL - Valid Short URL',
    passed: validateYouTubeVideoUrl('https://youtu.be/dQw4w9WgXcQ').isValid
  });
  
  tests.push({
    testName: 'YouTube URL - Invalid URL',
    passed: !validateYouTubeVideoUrl('https://example.com/video').isValid
  });
  
  tests.push({
    testName: 'YouTube URL - Malicious URL',
    passed: !validateYouTubeVideoUrl('javascript:alert("xss")').isValid
  });
  
  // Test channel query validation
  tests.push({
    testName: 'Channel Query - Valid Handle',
    passed: validateYouTubeChannelQuery('@validhandle').isValid
  });
  
  tests.push({
    testName: 'Channel Query - Valid Channel URL',
    passed: validateYouTubeChannelQuery('https://www.youtube.com/c/validchannel').isValid
  });
  
  tests.push({
    testName: 'Channel Query - SQL Injection',
    passed: !validateYouTubeChannelQuery("'; DROP TABLE channels; --").isValid
  });
  
  // Test notes validation
  tests.push({
    testName: 'Notes - Valid Input',
    passed: validateNotes('This is a valid note').isValid
  });
  
  tests.push({
    testName: 'Notes - Too Long',
    passed: !validateNotes('a'.repeat(2001)).isValid
  });
  
  tests.push({
    testName: 'Notes - SQL Injection',
    passed: !validateNotes("'; DROP TABLE saved_videos; --").isValid
  });
  
  // Test numeric input validation
  tests.push({
    testName: 'Numeric - Valid Number',
    passed: validateNumericInput('123').isValid
  });
  
  tests.push({
    testName: 'Numeric - Invalid Number',
    passed: !validateNumericInput('not-a-number').isValid
  });
  
  tests.push({
    testName: 'Numeric - Range Check',
    passed: !validateNumericInput('150', 0, 100).isValid
  });
  
  return tests;
};

/**
 * Tests API key security functions
 */
const testApiKeySecurity = (): TestResult[] => {
  const tests: TestResult[] = [];
  
  // Test API key format validation
  tests.push({
    testName: 'API Key Format - Valid Key',
    passed: validateApiKeyFormat('AIzaSyDummyKeyForTesting1234567890123456')
  });
  
  tests.push({
    testName: 'API Key Format - Invalid Key',
    passed: !validateApiKeyFormat('invalid-api-key')
  });
  
  tests.push({
    testName: 'API Key Format - Wrong Length',
    passed: !validateApiKeyFormat('AIzaShortKey')
  });
  
  tests.push({
    testName: 'API Key Format - Wrong Prefix',
    passed: !validateApiKeyFormat('BIzaSyDummyKeyForTesting1234567890123456')
  });
  
  // Test API key encryption/decryption
  try {
    const testKey = 'AIzaSyDummyKeyForTesting1234567890123456';
    const encrypted = encryptApiKey(testKey);
    const decrypted = decryptApiKey(encrypted);
    
    tests.push({
      testName: 'API Key Encryption/Decryption',
      passed: decrypted === testKey
    });
  } catch (error) {
    tests.push({
      testName: 'API Key Encryption/Decryption',
      passed: false,
      error: String(error)
    });
  }
  
  // Test usage validation
  tests.push({
    testName: 'Usage Validation - Within Limits',
    passed: validateApiUsage(5000, 100)
  });
  
  tests.push({
    testName: 'Usage Validation - Exceeds Limits',
    passed: !validateApiUsage(9950, 100)
  });
  
  tests.push({
    testName: 'Usage Validation - At Limit',
    passed: !validateApiUsage(API_USAGE_LIMITS.DAILY_QUOTA, 1)
  });
  
  return tests;
};

/**
 * Tests environment variable validation
 */
const testEnvironmentVariables = (): TestResult[] => {
  const tests: TestResult[] = [];
  
  try {
    validateEnvironmentVariables();
    tests.push({
      testName: 'Environment Variables - Validation',
      passed: true
    });
  } catch (error) {
    tests.push({
      testName: 'Environment Variables - Validation',
      passed: false,
      error: String(error)
    });
  }
  
  return tests;
};

/**
 * Runs penetration testing scenarios
 */
export const runPenetrationTests = (): TestResult[] => {
  const tests: TestResult[] = [];
  
  // SQL Injection attempts
  const sqlInjectionPayloads = [
    "'; DROP TABLE channels; --",
    "' OR '1'='1",
    "'; INSERT INTO channels (name) VALUES ('hacked'); --",
    "' UNION SELECT * FROM api_keys --",
    "'; DELETE FROM saved_videos; --"
  ];
  
  for (const payload of sqlInjectionPayloads) {
    tests.push({
      testName: `SQL Injection Protection - ${payload.substring(0, 20)}...`,
      passed: !validateName(payload).isValid
    });
  }
  
  // XSS attempts
  const xssPayloads = [
    '<script>alert("xss")</script>',
    '<img src="x" onerror="alert(1)">',
    'javascript:alert("xss")',
    '<svg onload="alert(1)">',
    '"><script>alert("xss")</script>'
  ];
  
  for (const payload of xssPayloads) {
    tests.push({
      testName: `XSS Protection - ${payload.substring(0, 20)}...`,
      passed: !validateName(payload).isValid
    });
  }
  
  // Path traversal attempts
  const pathTraversalPayloads = [
    '../../../etc/passwd',
    '..\\..\\..\\windows\\system32\\config\\sam',
    '....//....//....//etc/passwd',
    '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd'
  ];
  
  for (const payload of pathTraversalPayloads) {
    tests.push({
      testName: `Path Traversal Protection - ${payload.substring(0, 20)}...`,
      passed: !validateName(payload).isValid
    });
  }
  
  return tests;
};

/**
 * Displays test results in console
 */
export const displayTestResults = (results: TestResult[]): void => {
  console.log('\n🔒 Security Test Results');
  console.log('========================');
  
  const passed = results.filter(r => r.passed).length;
  const failed = results.filter(r => !r.passed).length;
  
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${results.length}`);
  
  if (failed > 0) {
    console.log('\n❌ Failed Tests:');
    results.filter(r => !r.passed).forEach(test => {
      console.log(`  - ${test.testName}`);
      if (test.error) {
        console.log(`    Error: ${test.error}`);
      }
    });
  }
  
  console.log('\n' + '='.repeat(50));
};

/**
 * Runs all security tests and displays results
 */
export const runAllSecurityTests = (): void => {
  console.log('🔒 Running Security Test Suite...\n');
  
  const basicTests = runSecurityTests();
  const penetrationTests = runPenetrationTests();
  
  const allTests = [...basicTests, ...penetrationTests];
  
  displayTestResults(allTests);
  
  const failedCount = allTests.filter(t => !t.passed).length;
  if (failedCount === 0) {
    console.log('🎉 All security tests passed!');
  } else {
    console.log(`⚠️  ${failedCount} security tests failed. Please review and fix.`);
  }
};
