
import React, { useState, useEffect, useCallback } from 'react';
import { parseFormattedNumber, useDebounce } from '../lib/utils';
import { CloseIcon } from './Icons';

interface ViewsFilterProps {
  onFilterChange: (minViews: number, maxViews: number | null) => void;
  listId: string; // To scope localStorage
}

const getInitialValue = (key: string, fallback: any): string => {
    try {
        const item = window.localStorage.getItem(key);
        return item !== null ? JSON.parse(item) : String(fallback || '');
    } catch (error) {
        console.warn("Error reading from localStorage", error);
        return String(fallback || '');
    }
};

export const ViewsFilter: React.FC<ViewsFilterProps> = ({
  onFilterChange,
  listId
}) => {
  const minStorageKey = `view_filter_min_${listId}`;
  const maxStorageKey = `view_filter_max_${listId}`;

  const [minInput, setMinInput] = useState(() => getInitialValue(minStorageKey, ''));
  const [maxInput, setMaxInput] = useState(() => getInitialValue(maxStorageKey, ''));
  const [error, setError] = useState<string | null>(null);

  const debouncedMin = useDebounce(minInput, 500);
  const debouncedMax = useDebounce(maxInput, 500);

  useEffect(() => {
    const min = parseFormattedNumber(debouncedMin);
    const max = parseFormattedNumber(debouncedMax);
    const finalMin = min ?? 0;
    const finalMax = max;

    try {
        window.localStorage.setItem(minStorageKey, JSON.stringify(debouncedMin));
        window.localStorage.setItem(maxStorageKey, JSON.stringify(debouncedMax));
    } catch (error) {
        console.warn("Error writing to localStorage", error);
    }

    if (finalMax !== null && finalMin > finalMax) {
      setError('Min views cannot be greater than max.');
      return; 
    }
    
    setError(null);
    onFilterChange(finalMin, finalMax);

  }, [debouncedMin, debouncedMax, onFilterChange, minStorageKey, maxStorageKey]);

  const handleReset = useCallback(() => {
      setMinInput('');
      setMaxInput('');
      setError(null);
  }, []);

  const hasValue = minInput.length > 0 || maxInput.length > 0;

  return (
    <div className="flex flex-col items-start">
        <div className={`flex items-center gap-2 bg-[#1a1a1a] border p-1 rounded-lg ${error ? 'border-red-500/50' : 'border-dark-border'}`}>
            <span className="text-sm font-semibold text-gray-400 pl-2">Views</span>
            <input
                type="text"
                value={minInput}
                onChange={(e) => setMinInput(e.target.value)}
                placeholder="0"
                className="w-20 bg-dark-card text-white text-center rounded-md p-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-accent transition-shadow"
                aria-label="Minimum views"
            />
            <span className="text-gray-400 font-medium text-sm">TO</span>
             <input
                type="text"
                value={maxInput}
                onChange={(e) => setMaxInput(e.target.value)}
                placeholder="1B+"
                className="w-20 bg-dark-card text-white text-center rounded-md p-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-accent transition-shadow"
                aria-label="Maximum views"
            />
            {hasValue && (
                 <button onClick={handleReset} className="p-1 rounded-full text-gray-400 hover:bg-gray-700 hover:text-white transition-colors" aria-label="Clear filter">
                    <CloseIcon className="w-4 h-4"/>
                </button>
            )}
        </div>
        {error && <p className="text-red-400 text-xs mt-1.5">{error}</p>}
    </div>
  );
};
