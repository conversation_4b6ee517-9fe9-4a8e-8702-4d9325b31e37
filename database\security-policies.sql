-- Supabase Row Level Security (RLS) Policies
-- These policies should be applied to your Supabase database to secure data access

-- Enable RLS on all tables
ALTER TABLE channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE statistics ENABLE ROW LEVEL SECURITY;
ALTER TABLE folders ENABLE ROW LEVEL SECURITY;
ALTER TABLE tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE saved_videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE saved_video_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;

-- Note: Since this is a single-user application without authentication,
-- we'll create policies that allow all operations for now.
-- In a multi-user environment, you would restrict based on user_id.

-- Channels table policies
CREATE POLICY "Allow all operations on channels" ON channels
    FOR ALL USING (true) WITH CHECK (true);

-- Statistics table policies
CREATE POLICY "Allow all operations on statistics" ON statistics
    FOR ALL USING (true) WITH CHECK (true);

-- Folders table policies
CREATE POLICY "Allow all operations on folders" ON folders
    FOR ALL USING (true) WITH CHECK (true);

-- Tags table policies
CREATE POLICY "Allow all operations on tags" ON tags
    FOR ALL USING (true) WITH CHECK (true);

-- API Keys table policies (most sensitive)
CREATE POLICY "Allow all operations on api_keys" ON api_keys
    FOR ALL USING (true) WITH CHECK (true);

-- Saved Videos table policies
CREATE POLICY "Allow all operations on saved_videos" ON saved_videos
    FOR ALL USING (true) WITH CHECK (true);

-- Saved Video Tags junction table policies
CREATE POLICY "Allow all operations on saved_video_tags" ON saved_video_tags
    FOR ALL USING (true) WITH CHECK (true);

-- Videos table policies (cached YouTube data)
CREATE POLICY "Allow all operations on videos" ON videos
    FOR ALL USING (true) WITH CHECK (true);

-- Additional security measures:

-- 1. Create indexes for better performance and security
CREATE INDEX IF NOT EXISTS idx_channels_youtube_id ON channels(youtube_id);
CREATE INDEX IF NOT EXISTS idx_statistics_channel_id ON statistics(channel_id);
CREATE INDEX IF NOT EXISTS idx_saved_videos_folder_id ON saved_videos(folder_id);
CREATE INDEX IF NOT EXISTS idx_saved_video_tags_video_id ON saved_video_tags(saved_video_id);
CREATE INDEX IF NOT EXISTS idx_saved_video_tags_tag_id ON saved_video_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_videos_channel_id ON videos(channel_id);

-- 2. Add constraints for data integrity
ALTER TABLE channels ADD CONSTRAINT channels_name_length CHECK (char_length(name) <= 100);
ALTER TABLE folders ADD CONSTRAINT folders_name_length CHECK (char_length(name) <= 100);
ALTER TABLE tags ADD CONSTRAINT tags_name_length CHECK (char_length(name) <= 100);
ALTER TABLE api_keys ADD CONSTRAINT api_keys_name_length CHECK (char_length(name) <= 100);
ALTER TABLE api_keys ADD CONSTRAINT api_keys_notes_length CHECK (notes IS NULL OR char_length(notes) <= 500);
ALTER TABLE saved_videos ADD CONSTRAINT saved_videos_notes_length CHECK (char_length(notes) <= 2000);

-- 3. Add check constraints for API usage limits
ALTER TABLE api_keys ADD CONSTRAINT api_keys_usage_positive CHECK (usage >= 0);
ALTER TABLE api_keys ADD CONSTRAINT api_keys_usage_reasonable CHECK (usage <= 50000); -- Allow some buffer above daily limit

-- 4. Ensure required fields are not null where appropriate
ALTER TABLE channels ALTER COLUMN name SET NOT NULL;
ALTER TABLE folders ALTER COLUMN name SET NOT NULL;
ALTER TABLE tags ALTER COLUMN name SET NOT NULL;
ALTER TABLE api_keys ALTER COLUMN name SET NOT NULL;
ALTER TABLE api_keys ALTER COLUMN key SET NOT NULL;
ALTER TABLE saved_videos ALTER COLUMN folder_id SET NOT NULL;
ALTER TABLE saved_videos ALTER COLUMN video_url SET NOT NULL;

-- 5. Create a function to validate YouTube API key format
CREATE OR REPLACE FUNCTION validate_youtube_api_key(key_value TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- YouTube API keys start with 'AIza' and are 39 characters long
    RETURN key_value ~ '^AIza[0-9A-Za-z\-_]{35}$';
END;
$$ LANGUAGE plpgsql;

-- 6. Add constraint to validate API key format
ALTER TABLE api_keys ADD CONSTRAINT api_keys_valid_format 
    CHECK (validate_youtube_api_key(key));

-- 7. Create a function to sanitize text input
CREATE OR REPLACE FUNCTION sanitize_text(input_text TEXT)
RETURNS TEXT AS $$
BEGIN
    IF input_text IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Remove control characters and limit length
    RETURN LEFT(
        REGEXP_REPLACE(
            TRIM(input_text), 
            '[\x00-\x1F\x7F]', 
            '', 
            'g'
        ), 
        1000
    );
END;
$$ LANGUAGE plpgsql;

-- 8. Create triggers to automatically sanitize input
CREATE OR REPLACE FUNCTION sanitize_before_insert_update()
RETURNS TRIGGER AS $$
BEGIN
    -- Sanitize name fields
    IF TG_TABLE_NAME IN ('channels', 'folders', 'tags', 'api_keys') THEN
        NEW.name = sanitize_text(NEW.name);
    END IF;

    -- Sanitize notes field for saved_videos
    IF TG_TABLE_NAME = 'saved_videos' AND NEW.notes IS NOT NULL THEN
        NEW.notes = sanitize_text(NEW.notes);
    END IF;

    -- Sanitize notes field for api_keys (if present)
    IF TG_TABLE_NAME = 'api_keys' AND NEW.notes IS NOT NULL THEN
        NEW.notes = sanitize_text(NEW.notes);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply sanitization triggers
CREATE TRIGGER sanitize_channels_trigger
    BEFORE INSERT OR UPDATE ON channels
    FOR EACH ROW EXECUTE FUNCTION sanitize_before_insert_update();

CREATE TRIGGER sanitize_folders_trigger
    BEFORE INSERT OR UPDATE ON folders
    FOR EACH ROW EXECUTE FUNCTION sanitize_before_insert_update();

CREATE TRIGGER sanitize_tags_trigger
    BEFORE INSERT OR UPDATE ON tags
    FOR EACH ROW EXECUTE FUNCTION sanitize_before_insert_update();

CREATE TRIGGER sanitize_api_keys_trigger
    BEFORE INSERT OR UPDATE ON api_keys
    FOR EACH ROW EXECUTE FUNCTION sanitize_before_insert_update();

CREATE TRIGGER sanitize_saved_videos_trigger
    BEFORE INSERT OR UPDATE ON saved_videos
    FOR EACH ROW EXECUTE FUNCTION sanitize_before_insert_update();
