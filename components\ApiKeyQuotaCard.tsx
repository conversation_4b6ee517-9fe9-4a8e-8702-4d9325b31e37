import React, { useState, useEffect } from 'react';
import { quotaService, type QuotaStatus } from '../services/quotaService';
import type { ApiKey } from '../types';

interface ApiKeyQuotaCardProps {
    apiKey: ApiKey;
    onQuotaReset?: (apiKeyId: string) => void;
    showResetButton?: boolean;
}

export const ApiKeyQuotaCard: React.FC<ApiKeyQuotaCardProps> = ({
    apiKey,
    onQuotaReset,
    showResetButton = false
}) => {
    const [quotaStatus, setQuotaStatus] = useState<QuotaStatus | null>(null);
    const [loading, setLoading] = useState(true);
    const [resetting, setResetting] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        loadQuotaStatus();
    }, [apiKey.id]);

    const loadQuotaStatus = async () => {
        try {
            setLoading(true);
            setError(null);
            const status = await quotaService.getQuotaStatus(apiKey.id);
            setQuotaStatus(status);
        } catch (err) {
            setError((err as Error).message);
        } finally {
            setLoading(false);
        }
    };

    const handleResetQuota = async () => {
        try {
            setResetting(true);
            await quotaService.resetApiKeyQuota(apiKey.id);
            await loadQuotaStatus(); // Refresh status
            onQuotaReset?.(apiKey.id);
        } catch (err) {
            setError((err as Error).message);
        } finally {
            setResetting(false);
        }
    };

    const getUsageColor = (percentage: number): string => {
        if (percentage >= 90) return 'text-red-400';
        if (percentage >= 80) return 'text-yellow-400';
        if (percentage >= 60) return 'text-orange-400';
        return 'text-green-400';
    };

    const getProgressBarColor = (percentage: number): string => {
        if (percentage >= 90) return 'bg-red-500';
        if (percentage >= 80) return 'bg-yellow-500';
        if (percentage >= 60) return 'bg-orange-500';
        return 'bg-[#10b981]';
    };

    const formatTimeUntilReset = (nextResetAt: string): string => {
        const now = new Date();
        const resetTime = new Date(nextResetAt);
        const diffMs = resetTime.getTime() - now.getTime();
        
        if (diffMs <= 0) return 'Reset available now';
        
        const hours = Math.floor(diffMs / (1000 * 60 * 60));
        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        
        if (hours > 0) {
            return `Resets in ${hours}h ${minutes}m`;
        } else {
            return `Resets in ${minutes}m`;
        }
    };

    if (loading) {
        return (
            <div className="bg-dark-card border border-dark-border rounded-lg p-4">
                <div className="animate-pulse">
                    <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-700 rounded w-1/2"></div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-dark-card border border-red-500/30 rounded-lg p-4">
                <div className="text-red-400 text-sm">
                    Failed to load quota status: {error}
                </div>
                <button 
                    onClick={loadQuotaStatus}
                    className="mt-2 text-xs text-accent hover:text-white transition-colors"
                >
                    Retry
                </button>
            </div>
        );
    }

    if (!quotaStatus) return null;

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center gap-3">
                <div className="w-3 h-3 rounded-full bg-[#10b981]"></div>
                <h3 className="text-xl font-semibold text-white">Quota Status</h3>
            </div>

            {/* Two-column layout for current usage vs remaining */}
            <div className="grid grid-cols-2 gap-6">
                <div>
                    <div className="text-xs text-gray-400 uppercase mb-2">CURRENT USAGE</div>
                    <div className={`text-2xl font-bold ${getUsageColor(quotaStatus.usage_percentage)} mb-1`}>
                        {quotaStatus.current_usage.toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-400">requests used</div>
                </div>
                <div>
                    <div className="text-xs text-gray-400 uppercase mb-2">REMAINING</div>
                    <div className="text-2xl font-bold text-white mb-1">
                        {quotaStatus.remaining_quota.toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-400">requests left</div>
                </div>
            </div>

            {/* Progress bar spanning full width */}
            <div className="space-y-3">
                <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                        className={`h-2 rounded-full transition-all duration-500 ${getProgressBarColor(quotaStatus.usage_percentage)}`}
                        style={{ width: `${Math.min(quotaStatus.usage_percentage, 100)}%` }}
                    ></div>
                </div>
                <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-400">
                        {quotaStatus.usage_percentage.toFixed(1)}% used
                    </span>
                    <span className="text-xs text-gray-400">
                        {quotaStatus.quota_limit.toLocaleString()} total
                    </span>
                </div>
            </div>

            {/* Status indicator */}
            <div className="flex justify-center">
                <span className={`text-xs px-3 py-1 rounded-full ${
                    quotaStatus.usage_percentage > 80 ? 'text-red-300 bg-red-500/20' :
                    quotaStatus.usage_percentage > 50 ? 'text-yellow-300 bg-yellow-500/20' :
                    'text-green-300 bg-green-500/20'
                }`}>
                    {quotaStatus.usage_percentage > 80 ? 'High Usage' :
                     quotaStatus.usage_percentage > 50 ? 'Moderate Usage' : 'Healthy Usage'}
                </span>
            </div>

            {/* Reset timer at bottom with clock icon */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-sm text-gray-400">
                        Next reset: {formatTimeUntilReset(quotaStatus.next_reset_at)}
                    </span>
                </div>
                {showResetButton && quotaStatus.needs_reset && (
                    <button
                        onClick={handleResetQuota}
                        disabled={resetting}
                        className="px-3 py-1.5 text-xs bg-[#10b981] text-white rounded hover:bg-[#059669] disabled:bg-gray-600 disabled:text-gray-400 transition-colors"
                    >
                        {resetting ? 'Resetting...' : 'Reset Now'}
                    </button>
                )}
            </div>

            {/* Warning Messages */}
            {quotaStatus.usage_percentage >= 90 && (
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
                    <div className="flex items-center gap-2">
                        <svg className="w-4 h-4 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <span className="text-red-400 text-xs font-medium">
                            Critical: Quota nearly exhausted
                        </span>
                    </div>
                </div>
            )}
            
            {quotaStatus.usage_percentage >= 80 && quotaStatus.usage_percentage < 90 && (
                <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
                    <div className="flex items-center gap-2">
                        <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <span className="text-yellow-400 text-xs font-medium">
                            Warning: High quota usage
                        </span>
                    </div>
                </div>
            )}
        </div>
    );
};
