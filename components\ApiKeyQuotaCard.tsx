import React, { useState, useEffect } from 'react';
import { quotaService, type QuotaStatus } from '../services/quotaService';
import type { ApiKey } from '../types';

interface ApiKeyQuotaCardProps {
    apiKey: ApiKey;
    onQuotaReset?: (apiKeyId: string) => void;
    showResetButton?: boolean;
}

export const ApiKeyQuotaCard: React.FC<ApiKeyQuotaCardProps> = ({
    apiKey,
    onQuotaReset,
    showResetButton = false
}) => {
    const [quotaStatus, setQuotaStatus] = useState<QuotaStatus | null>(null);
    const [loading, setLoading] = useState(true);
    const [resetting, setResetting] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        loadQuotaStatus();
    }, [apiKey.id]);

    const loadQuotaStatus = async () => {
        try {
            setLoading(true);
            setError(null);
            const status = await quotaService.getQuotaStatus(apiKey.id);
            setQuotaStatus(status);
        } catch (err) {
            setError((err as Error).message);
        } finally {
            setLoading(false);
        }
    };

    const handleResetQuota = async () => {
        try {
            setResetting(true);
            await quotaService.resetApiKeyQuota(apiKey.id);
            await loadQuotaStatus(); // Refresh status
            onQuotaReset?.(apiKey.id);
        } catch (err) {
            setError((err as Error).message);
        } finally {
            setResetting(false);
        }
    };

    const getUsageColor = (percentage: number): string => {
        if (percentage >= 90) return 'text-red-400';
        if (percentage >= 80) return 'text-yellow-400';
        if (percentage >= 60) return 'text-orange-400';
        return 'text-green-400';
    };

    const getProgressBarColor = (percentage: number): string => {
        if (percentage >= 90) return 'bg-red-500';
        if (percentage >= 80) return 'bg-yellow-500';
        if (percentage >= 60) return 'bg-orange-500';
        return 'bg-green-500';
    };

    const formatTimeUntilReset = (nextResetAt: string): string => {
        const now = new Date();
        const resetTime = new Date(nextResetAt);
        const diffMs = resetTime.getTime() - now.getTime();
        
        if (diffMs <= 0) return 'Reset available now';
        
        const hours = Math.floor(diffMs / (1000 * 60 * 60));
        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        
        if (hours > 0) {
            return `Resets in ${hours}h ${minutes}m`;
        } else {
            return `Resets in ${minutes}m`;
        }
    };

    if (loading) {
        return (
            <div className="bg-dark-card border border-dark-border rounded-lg p-4">
                <div className="animate-pulse">
                    <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-700 rounded w-1/2"></div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-dark-card border border-red-500/30 rounded-lg p-4">
                <div className="text-red-400 text-sm">
                    Failed to load quota status: {error}
                </div>
                <button 
                    onClick={loadQuotaStatus}
                    className="mt-2 text-xs text-accent hover:text-white transition-colors"
                >
                    Retry
                </button>
            </div>
        );
    }

    if (!quotaStatus) return null;

    return (
        <div className="bg-gradient-to-br from-dark-card/80 to-dark-card/60 border border-dark-border/50 rounded-xl p-5 space-y-4">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <div className="w-3 h-3 rounded-full bg-accent"></div>
                    <h4 className="font-semibold text-white">Quota Status</h4>
                </div>
                {quotaStatus.needs_reset && (
                    <div className="px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded-full text-xs font-medium border border-yellow-500/30">
                        Reset Available
                    </div>
                )}
            </div>

            {/* Usage Statistics */}
            <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                    <div className="text-xs text-gray-400 uppercase tracking-wide">Current Usage</div>
                    <div className={`text-lg font-bold ${getUsageColor(quotaStatus.usage_percentage)}`}>
                        {quotaStatus.current_usage.toLocaleString()}
                    </div>
                </div>
                <div className="space-y-1">
                    <div className="text-xs text-gray-400 uppercase tracking-wide">Remaining</div>
                    <div className="text-lg font-bold text-white">
                        {quotaStatus.remaining_quota.toLocaleString()}
                    </div>
                </div>
            </div>

            {/* Progress Bar */}
            <div className="space-y-2">
                <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-400">
                        {quotaStatus.usage_percentage.toFixed(1)}% used
                    </span>
                    <span className="text-xs text-gray-400">
                        {quotaStatus.quota_limit.toLocaleString()} total
                    </span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                        className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor(quotaStatus.usage_percentage)}`}
                        style={{ width: `${Math.min(quotaStatus.usage_percentage, 100)}%` }}
                    ></div>
                </div>
            </div>

            {/* Reset Information */}
            <div className="flex items-center justify-between pt-2 border-t border-gray-700/50">
                <div className="text-xs text-gray-400">
                    {formatTimeUntilReset(quotaStatus.next_reset_at)}
                </div>
                {showResetButton && (
                    <button
                        onClick={handleResetQuota}
                        disabled={resetting || !quotaStatus.needs_reset}
                        className={`px-3 py-1 rounded-md text-xs font-medium transition-all duration-200 ${
                            quotaStatus.needs_reset
                                ? 'bg-accent/20 text-accent hover:bg-accent/30 border border-accent/30'
                                : 'bg-gray-700/50 text-gray-500 cursor-not-allowed'
                        }`}
                    >
                        {resetting ? 'Resetting...' : 'Reset Now'}
                    </button>
                )}
            </div>

            {/* Warning Messages */}
            {quotaStatus.usage_percentage >= 90 && (
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
                    <div className="flex items-center gap-2">
                        <svg className="w-4 h-4 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <span className="text-red-400 text-xs font-medium">
                            Critical: Quota nearly exhausted
                        </span>
                    </div>
                </div>
            )}
            
            {quotaStatus.usage_percentage >= 80 && quotaStatus.usage_percentage < 90 && (
                <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
                    <div className="flex items-center gap-2">
                        <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <span className="text-yellow-400 text-xs font-medium">
                            Warning: High quota usage
                        </span>
                    </div>
                </div>
            )}
        </div>
    );
};
