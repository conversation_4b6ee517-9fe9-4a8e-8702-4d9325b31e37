#!/usr/bin/env node

/**
 * Security Monitoring Script
 * Automated security monitoring and alerting for production environments
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  const timestamp = new Date().toISOString();
  console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
}

// Configuration
const MONITORING_CONFIG = {
  CHECK_INTERVAL: 5 * 60 * 1000, // 5 minutes
  API_QUOTA_WARNING_THRESHOLD: 8000,
  API_QUOTA_CRITICAL_THRESHOLD: 9500,
  MAX_LOG_ENTRIES: 1000,
  ALERT_COOLDOWN: 30 * 60 * 1000, // 30 minutes
};

// Alert tracking to prevent spam
const alertHistory = new Map();

function shouldSendAlert(alertType) {
  const lastAlert = alertHistory.get(alertType);
  const now = Date.now();
  
  if (!lastAlert || (now - lastAlert) > MONITORING_CONFIG.ALERT_COOLDOWN) {
    alertHistory.set(alertType, now);
    return true;
  }
  
  return false;
}

async function checkApiKeyUsage() {
  log('🔑 Checking API key usage...', colors.blue);
  
  try {
    // This would need to be adapted to work with your actual API
    // For now, we'll simulate the check
    
    const mockApiKeys = [
      { id: '1', name: 'Production Key', usage: 7500 },
      { id: '2', name: 'Backup Key', usage: 2000 }
    ];
    
    const alerts = [];
    
    for (const key of mockApiKeys) {
      const usagePercent = (key.usage / 10000) * 100;
      
      if (key.usage >= MONITORING_CONFIG.API_QUOTA_CRITICAL_THRESHOLD) {
        if (shouldSendAlert(`quota_critical_${key.id}`)) {
          alerts.push({
            severity: 'critical',
            type: 'quota_exceeded',
            message: `API key "${key.name}" has critical usage: ${key.usage}/10000 (${usagePercent.toFixed(1)}%)`,
            key: key.name,
            usage: key.usage
          });
        }
      } else if (key.usage >= MONITORING_CONFIG.API_QUOTA_WARNING_THRESHOLD) {
        if (shouldSendAlert(`quota_warning_${key.id}`)) {
          alerts.push({
            severity: 'warning',
            type: 'quota_warning',
            message: `API key "${key.name}" approaching quota limit: ${key.usage}/10000 (${usagePercent.toFixed(1)}%)`,
            key: key.name,
            usage: key.usage
          });
        }
      }
    }
    
    return alerts;
    
  } catch (error) {
    log(`❌ Error checking API key usage: ${error.message}`, colors.red);
    return [{
      severity: 'high',
      type: 'monitoring_error',
      message: `Failed to check API key usage: ${error.message}`
    }];
  }
}

async function checkSecurityLogs() {
  log('📋 Checking security logs...', colors.blue);
  
  try {
    // Read security logs from localStorage simulation
    // In production, this would read from your logging service
    
    const mockSecurityEvents = [
      {
        event_type: 'invalid_input',
        severity: 'medium',
        timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
        description: 'SQL injection attempt detected'
      },
      {
        event_type: 'api_key_added',
        severity: 'low',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        description: 'New API key added'
      }
    ];
    
    const alerts = [];
    const recentEvents = mockSecurityEvents.filter(event => {
      const eventTime = new Date(event.timestamp).getTime();
      const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
      return eventTime > fiveMinutesAgo;
    });
    
    // Check for suspicious patterns
    const invalidInputEvents = recentEvents.filter(e => e.event_type === 'invalid_input');
    if (invalidInputEvents.length > 5) {
      if (shouldSendAlert('suspicious_activity')) {
        alerts.push({
          severity: 'high',
          type: 'suspicious_activity',
          message: `High number of invalid input attempts: ${invalidInputEvents.length} in last 5 minutes`,
          count: invalidInputEvents.length
        });
      }
    }
    
    // Check for critical events
    const criticalEvents = recentEvents.filter(e => e.severity === 'critical');
    for (const event of criticalEvents) {
      if (shouldSendAlert(`critical_event_${event.timestamp}`)) {
        alerts.push({
          severity: 'critical',
          type: 'security_event',
          message: `Critical security event: ${event.description}`,
          event: event
        });
      }
    }
    
    return alerts;
    
  } catch (error) {
    log(`❌ Error checking security logs: ${error.message}`, colors.red);
    return [{
      severity: 'medium',
      type: 'monitoring_error',
      message: `Failed to check security logs: ${error.message}`
    }];
  }
}

async function checkApplicationHealth() {
  log('🏥 Checking application health...', colors.blue);
  
  try {
    // Simulate health checks
    // In production, this would make actual HTTP requests to your app
    
    const healthChecks = [
      { name: 'Database Connection', status: 'healthy', responseTime: 150 },
      { name: 'API Endpoints', status: 'healthy', responseTime: 300 },
      { name: 'Authentication', status: 'healthy', responseTime: 100 }
    ];
    
    const alerts = [];
    
    for (const check of healthChecks) {
      if (check.status !== 'healthy') {
        if (shouldSendAlert(`health_${check.name}`)) {
          alerts.push({
            severity: 'high',
            type: 'health_check_failed',
            message: `Health check failed: ${check.name} is ${check.status}`,
            check: check.name,
            status: check.status
          });
        }
      } else if (check.responseTime > 5000) {
        if (shouldSendAlert(`slow_response_${check.name}`)) {
          alerts.push({
            severity: 'warning',
            type: 'slow_response',
            message: `Slow response time: ${check.name} took ${check.responseTime}ms`,
            check: check.name,
            responseTime: check.responseTime
          });
        }
      }
    }
    
    return alerts;
    
  } catch (error) {
    log(`❌ Error checking application health: ${error.message}`, colors.red);
    return [{
      severity: 'critical',
      type: 'monitoring_error',
      message: `Failed to check application health: ${error.message}`
    }];
  }
}

async function sendAlert(alert) {
  const severityColors = {
    low: colors.blue,
    medium: colors.yellow,
    warning: colors.yellow,
    high: colors.red,
    critical: colors.red
  };
  
  const color = severityColors[alert.severity] || colors.reset;
  const icon = alert.severity === 'critical' ? '🚨' : 
               alert.severity === 'high' ? '⚠️' : 
               alert.severity === 'warning' ? '⚠️' : 'ℹ️';
  
  log(`${icon} ALERT [${alert.severity.toUpperCase()}]: ${alert.message}`, color);
  
  // In production, send alerts to external services
  // Examples:
  
  // Slack webhook
  // await fetch(process.env.SLACK_WEBHOOK_URL, {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify({
  //     text: `VirSnapp Security Alert: ${alert.message}`,
  //     color: alert.severity === 'critical' ? 'danger' : 'warning'
  //   })
  // });
  
  // Email notification
  // await sendEmail({
  //   to: process.env.ALERT_EMAIL,
  //   subject: `VirSnapp Security Alert - ${alert.severity}`,
  //   body: alert.message
  // });
  
  // SMS notification for critical alerts
  // if (alert.severity === 'critical') {
  //   await sendSMS({
  //     to: process.env.ALERT_PHONE,
  //     message: `CRITICAL: VirSnapp security alert - ${alert.message}`
  //   });
  // }
}

async function runMonitoringCycle() {
  log('🔍 Starting monitoring cycle...', colors.cyan);
  
  try {
    // Run all monitoring checks
    const [apiAlerts, securityAlerts, healthAlerts] = await Promise.all([
      checkApiKeyUsage(),
      checkSecurityLogs(),
      checkApplicationHealth()
    ]);
    
    // Combine all alerts
    const allAlerts = [...apiAlerts, ...securityAlerts, ...healthAlerts];
    
    if (allAlerts.length === 0) {
      log('✅ All systems healthy - no alerts', colors.green);
    } else {
      log(`📊 Found ${allAlerts.length} alerts to process`, colors.yellow);
      
      // Send alerts
      for (const alert of allAlerts) {
        await sendAlert(alert);
      }
    }
    
    // Log summary
    const criticalCount = allAlerts.filter(a => a.severity === 'critical').length;
    const highCount = allAlerts.filter(a => a.severity === 'high').length;
    const warningCount = allAlerts.filter(a => a.severity === 'warning' || a.severity === 'medium').length;
    
    log(`📈 Monitoring Summary: ${criticalCount} critical, ${highCount} high, ${warningCount} warnings`, colors.blue);
    
  } catch (error) {
    log(`❌ Error in monitoring cycle: ${error.message}`, colors.red);
    
    // Send critical alert about monitoring failure
    await sendAlert({
      severity: 'critical',
      type: 'monitoring_failure',
      message: `Security monitoring system failure: ${error.message}`
    });
  }
  
  log('✅ Monitoring cycle complete', colors.cyan);
}

async function startMonitoring() {
  log('🚀 Starting VirSnapp Security Monitor', colors.bold);
  log(`📊 Check interval: ${MONITORING_CONFIG.CHECK_INTERVAL / 1000 / 60} minutes`, colors.blue);
  log(`⚠️ API quota warning threshold: ${MONITORING_CONFIG.API_QUOTA_WARNING_THRESHOLD}`, colors.blue);
  log(`🚨 API quota critical threshold: ${MONITORING_CONFIG.API_QUOTA_CRITICAL_THRESHOLD}`, colors.blue);
  
  // Run initial check
  await runMonitoringCycle();
  
  // Set up recurring monitoring
  setInterval(runMonitoringCycle, MONITORING_CONFIG.CHECK_INTERVAL);
  
  log('✅ Security monitoring active', colors.green);
}

async function runOnceAndExit() {
  log('🔍 Running single monitoring check...', colors.cyan);
  await runMonitoringCycle();
  process.exit(0);
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log('🛑 Shutting down security monitor...', colors.yellow);
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('🛑 Shutting down security monitor...', colors.yellow);
  process.exit(0);
});

// Main execution
const isMainModule = import.meta.url === `file://${process.argv[1]}` || 
                     import.meta.url.endsWith(process.argv[1]) ||
                     process.argv[1].includes('security-monitor.js');

if (isMainModule) {
  const args = process.argv.slice(2);
  
  if (args.includes('--once') || args.includes('-o')) {
    await runOnceAndExit();
  } else {
    await startMonitoring();
  }
}

export { runMonitoringCycle, startMonitoring };
