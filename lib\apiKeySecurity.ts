/**
 * API Key Security Utilities
 * Provides client-side encryption and validation for YouTube API keys
 */

import type { <PERSON><PERSON><PERSON><PERSON> } from '../types';

// Simple encryption key derived from browser fingerprint
// Note: This is obfuscation, not true security - real security requires server-side validation
const getEncryptionKey = (): string => {
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width,
    screen.height,
    new Date().getTimezoneOffset()
  ].join('|');
  
  // Simple hash function for encryption key
  let hash = 0;
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
};

// Simple XOR encryption/decryption
const xorEncrypt = (text: string, key: string): string => {
  let result = '';
  for (let i = 0; i < text.length; i++) {
    result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length));
  }
  return btoa(result); // Base64 encode
};

const xorDecrypt = (encrypted: string, key: string): string => {
  try {
    const decoded = atob(encrypted); // Base64 decode
    let result = '';
    for (let i = 0; i < decoded.length; i++) {
      result += String.fromCharCode(decoded.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return result;
  } catch {
    throw new Error('Failed to decrypt API key');
  }
};

// API Key validation patterns
const YOUTUBE_API_KEY_PATTERN = /^AIza[0-9A-Za-z-_]{35}$/;

/**
 * Validates YouTube API key format
 */
export const validateApiKeyFormat = (key: string): boolean => {
  return YOUTUBE_API_KEY_PATTERN.test(key.trim());
};

/**
 * Checks if a stored key appears to be encrypted (base64) or plain text
 */
export const isKeyEncrypted = (key: string): boolean => {
  // If it matches the YouTube API key pattern, it's likely plain text
  if (validateApiKeyFormat(key)) {
    return false;
  }

  // Check if it looks like base64 (encrypted keys are base64 encoded)
  try {
    const decoded = atob(key);
    // If base64 decode succeeds and the result is not a valid API key, it's likely encrypted
    return !validateApiKeyFormat(decoded);
  } catch {
    // If base64 decode fails, it's likely plain text but malformed
    return false;
  }
};

/**
 * Encrypts an API key for storage
 */
export const encryptApiKey = (key: string): string => {
  if (!validateApiKeyFormat(key)) {
    throw new Error('Invalid YouTube API key format');
  }
  
  const encryptionKey = getEncryptionKey();
  return xorEncrypt(key, encryptionKey);
};

/**
 * Decrypts an API key from storage with fallback for legacy plain text keys
 */
export const decryptApiKey = (encryptedKey: string): string => {
  // First, check if the key is already in plain text (legacy format)
  if (validateApiKeyFormat(encryptedKey)) {
    console.warn('⚠️ API key appears to be stored in plain text. For better security, consider re-adding it in Settings to enable encryption.');
    return encryptedKey;
  }

  // Try to decrypt the key
  try {
    const encryptionKey = getEncryptionKey();
    const decrypted = xorDecrypt(encryptedKey, encryptionKey);

    if (!validateApiKeyFormat(decrypted)) {
      throw new Error('Decrypted API key has invalid format. The key may be corrupted or was encrypted with a different method.');
    }

    return decrypted;
  } catch (error) {
    // If decryption fails, provide helpful error message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    // Check if it might be a base64 issue
    if (errorMessage.includes('Failed to decrypt API key')) {
      throw new Error('Unable to decrypt API key. This may be due to a corrupted key or incompatible encryption. Please try re-adding your API key in Settings.');
    }

    throw new Error(`Failed to decrypt API key: ${errorMessage}`);
  }
};

/**
 * Checks if an API key needs to be migrated from plain text to encrypted format
 */
export const needsKeyMigration = (storedKey: string): boolean => {
  return validateApiKeyFormat(storedKey) && !isKeyEncrypted(storedKey);
};

/**
 * Provides a user-friendly error message for API key issues
 */
export const getApiKeyErrorMessage = (error: Error, keyName?: string): string => {
  const keyRef = keyName ? `"${keyName}"` : 'API key';

  if (error.message.includes('invalid format')) {
    return `${keyRef} appears to be corrupted or invalid. Please check that you've entered a valid YouTube API key and try re-adding it in Settings.`;
  }

  if (error.message.includes('Failed to decrypt')) {
    return `Unable to decrypt ${keyRef}. This may be due to a corrupted key or encryption issue. Please try re-adding your API key in Settings.`;
  }

  return `Error with ${keyRef}: ${error.message}`;
};

/**
 * API usage limits and validation
 */
export const API_USAGE_LIMITS = {
  DAILY_QUOTA: 10000, // YouTube API daily quota limit
  SEARCH_COST: 100,   // Cost per search operation
  CHANNELS_COST: 1,   // Cost per channels API call
  VIDEOS_COST: 1,     // Cost per videos API call
  PLAYLIST_COST: 1,   // Cost per playlist items call
} as const;

/**
 * Validates if an API operation can be performed within usage limits
 */
export const validateApiUsage = (currentUsage: number, operationCost: number): boolean => {
  return (currentUsage + operationCost) <= API_USAGE_LIMITS.DAILY_QUOTA;
};

/**
 * Calculates remaining API quota
 */
export const getRemainingQuota = (currentUsage: number): number => {
  return Math.max(0, API_USAGE_LIMITS.DAILY_QUOTA - currentUsage);
};

/**
 * Estimates API cost for bulk operations
 */
export const estimateBulkOperationCost = (channelCount: number): number => {
  // Estimate based on typical operations per channel
  const searchCost = API_USAGE_LIMITS.SEARCH_COST; // Initial search
  const channelsCost = Math.ceil(channelCount / 50) * API_USAGE_LIMITS.CHANNELS_COST; // Batch channel info
  const videosCost = channelCount * 2 * API_USAGE_LIMITS.VIDEOS_COST; // Estimate 2 video calls per channel
  
  return searchCost + channelsCost + videosCost;
};

/**
 * Security warning for API key exposure
 */
export const getSecurityWarning = (): string => {
  return `
⚠️ SECURITY NOTICE:
YouTube API keys are stored client-side with basic obfuscation.
For production use, consider:
1. Implementing a server-side API proxy
2. Using OAuth 2.0 for user authentication
3. Implementing proper rate limiting on the server
4. Regular API key rotation
  `.trim();
};

/**
 * Validates API key and checks for common security issues
 */
export const performSecurityCheck = (apiKey: ApiKey): string[] => {
  const warnings: string[] = [];
  
  // Check usage patterns
  if (apiKey.usage > API_USAGE_LIMITS.DAILY_QUOTA * 0.8) {
    warnings.push('API key usage is approaching daily limit');
  }
  
  // Check key age (if we had creation date)
  // This would require adding created_at to ApiKey type
  
  // Check for suspicious usage patterns
  if (apiKey.usage > API_USAGE_LIMITS.DAILY_QUOTA) {
    warnings.push('API key has exceeded daily quota - this may indicate abuse');
  }
  
  return warnings;
};
