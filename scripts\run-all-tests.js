#!/usr/bin/env node

/**
 * Comprehensive Test Runner
 * Runs all security tests, validation checks, and manual test guides
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Import test functions (these would need to be adapted for Node.js environment)
async function runSecurityTests() {
  log('\n🔒 SECURITY UNIT TESTS', colors.bold);
  log('=' .repeat(50), colors.blue);
  
  // Since we can't directly import the browser-based test functions,
  // we'll provide instructions for running them
  
  log('📋 To run security unit tests:', colors.cyan);
  log('1. Open your application in a browser');
  log('2. Open browser developer console (F12)');
  log('3. Run the following commands:');
  log('');
  log('// Import and run security tests', colors.yellow);
  log('import { runAllSecurityTests } from "./tests/security.test.ts";', colors.yellow);
  log('runAllSecurityTests();', colors.yellow);
  log('');
  log('Expected: All tests should pass (100% success rate)');
  
  return true;
}

async function runInputValidationTests() {
  log('\n🛡️ INPUT VALIDATION TESTS', colors.bold);
  log('=' .repeat(50), colors.blue);
  
  log('📋 Manual Input Validation Tests:', colors.cyan);
  
  const testCases = [
    {
      category: 'SQL Injection Protection',
      tests: [
        "'; DROP TABLE channels; --",
        "' OR '1'='1",
        "'; INSERT INTO channels (name) VALUES ('hacked'); --"
      ]
    },
    {
      category: 'XSS Protection',
      tests: [
        '<script>alert("xss")</script>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert("xss")'
      ]
    },
    {
      category: 'YouTube URL Validation',
      tests: [
        'https://www.youtube.com/watch?v=dQw4w9WgXcQ (should pass)',
        'https://youtu.be/dQw4w9WgXcQ (should pass)',
        'https://example.com/video (should fail)',
        'javascript:alert("xss") (should fail)'
      ]
    },
    {
      category: 'Name Validation',
      tests: [
        'Valid Channel Name (should pass)',
        '' + 'a'.repeat(101) + ' (should fail - too long)',
        '<script>alert(1)</script> (should fail - XSS)',
        "'; DROP TABLE channels; -- (should fail - SQL injection)"
      ]
    }
  ];
  
  testCases.forEach(category => {
    log(`\n${category.category}:`, colors.yellow);
    category.tests.forEach(test => {
      log(`  • Test: ${test}`, colors.cyan);
    });
  });
  
  log('\n📋 How to test:', colors.cyan);
  log('1. Go to your application');
  log('2. Try entering the test cases above in various forms');
  log('3. Verify that malicious inputs are rejected');
  log('4. Check that valid inputs are accepted');
  
  return true;
}

async function runApiKeyTests() {
  log('\n🔑 API KEY SECURITY TESTS', colors.bold);
  log('=' .repeat(50), colors.blue);
  
  log('📋 API Key Security Tests:', colors.cyan);
  
  log('\n1. API Key Format Validation:', colors.yellow);
  log('   • Valid: AIzaSyDummyKeyForTesting1234567890123456');
  log('   • Invalid: invalid-api-key');
  log('   • Invalid: AIzaShortKey');
  log('   • Invalid: BIzaSyDummyKeyForTesting1234567890123456');
  
  log('\n2. API Key Encryption/Decryption:', colors.yellow);
  log('   • Add an API key in Settings');
  log('   • Verify it appears encrypted in database');
  log('   • Verify it decrypts correctly when displayed');
  
  log('\n3. Usage Quota Validation:', colors.yellow);
  log('   • Test with high usage values');
  log('   • Verify quota warnings appear');
  log('   • Test quota exceeded scenarios');
  
  log('\n📋 How to test:', colors.cyan);
  log('1. Go to Settings page in your application');
  log('2. Try adding API keys with the test cases above');
  log('3. Check browser console for security warnings');
  log('4. Verify database storage is encrypted');
  
  return true;
}

async function runDatabaseTests() {
  log('\n🗄️ DATABASE SECURITY TESTS', colors.bold);
  log('=' .repeat(50), colors.blue);
  
  log('📋 Database Security Tests:', colors.cyan);
  
  log('\n1. Row Level Security (RLS):', colors.yellow);
  log('   • Verify RLS is enabled on all tables');
  log('   • Test data access permissions');
  
  log('\n2. Input Sanitization:', colors.yellow);
  log('   • Test database triggers sanitize input');
  log('   • Verify malicious input is cleaned');
  
  log('\n3. Constraints:', colors.yellow);
  log('   • Test length constraints on names');
  log('   • Test API key format constraints');
  log('   • Test required field constraints');
  
  log('\n📋 SQL Commands to test in Supabase SQL Editor:', colors.cyan);
  log('');
  log('-- Test RLS is enabled', colors.yellow);
  log('SELECT schemaname, tablename, rowsecurity FROM pg_tables WHERE schemaname = \'public\';', colors.yellow);
  log('');
  log('-- Test length constraint', colors.yellow);
  log('INSERT INTO channels (name) VALUES (REPEAT(\'a\', 101)); -- Should fail', colors.yellow);
  log('');
  log('-- Test API key format constraint', colors.yellow);
  log('INSERT INTO api_keys (name, key) VALUES (\'test\', \'invalid_key\'); -- Should fail', colors.yellow);
  
  return true;
}

async function runPerformanceTests() {
  log('\n⚡ PERFORMANCE TESTS', colors.bold);
  log('=' .repeat(50), colors.blue);
  
  log('📋 Performance Test Areas:', colors.cyan);
  
  log('\n1. API Quota Usage:', colors.yellow);
  log('   • Monitor YouTube API quota consumption');
  log('   • Test with multiple channels');
  log('   • Verify quota warnings work');
  
  log('\n2. Database Query Performance:', colors.yellow);
  log('   • Test with large datasets');
  log('   • Monitor query execution times');
  log('   • Verify indexes are being used');
  
  log('\n3. Application Load Time:', colors.yellow);
  log('   • Measure initial page load');
  log('   • Test with slow network conditions');
  log('   • Monitor bundle size');
  
  log('\n📋 How to test:', colors.cyan);
  log('1. Use browser DevTools Performance tab');
  log('2. Test with Network throttling enabled');
  log('3. Monitor API usage in Settings page');
  log('4. Check Supabase dashboard for query performance');
  
  return true;
}

async function generateTestReport() {
  log('\n📊 TEST EXECUTION SUMMARY', colors.bold);
  log('=' .repeat(60), colors.magenta);
  
  const testSuites = [
    { name: 'Security Unit Tests', status: 'Manual', description: 'Run in browser console' },
    { name: 'Input Validation Tests', status: 'Manual', description: 'Test malicious inputs' },
    { name: 'API Key Security Tests', status: 'Manual', description: 'Test encryption/validation' },
    { name: 'Database Security Tests', status: 'Manual', description: 'Test RLS and constraints' },
    { name: 'Performance Tests', status: 'Manual', description: 'Monitor performance metrics' }
  ];
  
  log('\n📋 Test Suite Status:', colors.blue);
  testSuites.forEach(suite => {
    log(`  ${suite.status === 'Manual' ? '📋' : '✅'} ${suite.name}: ${suite.status}`, 
        suite.status === 'Manual' ? colors.yellow : colors.green);
    log(`     ${suite.description}`, colors.cyan);
  });
  
  log('\n🎯 Testing Priorities:', colors.blue);
  log('1. 🔴 CRITICAL: Run security unit tests first');
  log('2. 🟡 HIGH: Test input validation thoroughly');
  log('3. 🟡 HIGH: Verify API key encryption works');
  log('4. 🟢 MEDIUM: Test database constraints');
  log('5. 🟢 MEDIUM: Monitor performance metrics');
  
  log('\n📋 Automated Testing Recommendations:', colors.blue);
  log('For production applications, consider implementing:');
  log('• Jest/Vitest for unit testing');
  log('• Cypress/Playwright for E2E testing');
  log('• Continuous integration testing');
  log('• Automated security scanning');
  
  log('\n' + '=' .repeat(60), colors.bold);
}

async function runAllTests() {
  log('🧪 VIRSNAPP COMPREHENSIVE TEST SUITE', colors.bold);
  log('=' .repeat(60), colors.magenta);
  
  // Change to project root
  process.chdir(path.join(__dirname, '..'));
  
  await runSecurityTests();
  await runInputValidationTests();
  await runApiKeyTests();
  await runDatabaseTests();
  await runPerformanceTests();
  await generateTestReport();
  
  log('\n✅ Test suite execution complete!', colors.green);
  log('Follow the manual testing instructions above to validate security.', colors.cyan);
  
  return true;
}

// Main execution
const isMainModule = import.meta.url === `file://${process.argv[1]}` || 
                     import.meta.url.endsWith(process.argv[1]) ||
                     process.argv[1].includes('run-all-tests.js');

if (isMainModule) {
  await runAllTests();
  process.exit(0);
}

export { runAllTests };
