<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tag Styling Test - VirSnapp</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              darkbg: '#1a1a1a',
              'dark-card': '#242424',
              'dark-border': '#3a3a3a',
              accent: '#00ff88',
              'accent-hover': '#00e67a',
            },
            animation: {
              'tag-select': 'tag-select 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)',
              'tag-glow': 'tag-glow 2s ease-in-out infinite alternate',
            },
            keyframes: {
              'tag-select': {
                '0%': {
                  transform: 'scale(1)',
                  boxShadow: '0 0 0 0 rgba(0, 255, 136, 0.4)'
                },
                '50%': {
                  transform: 'scale(1.05)',
                  boxShadow: '0 0 0 8px rgba(0, 255, 136, 0.1)'
                },
                '100%': {
                  transform: 'scale(1)',
                  boxShadow: '0 0 0 0 rgba(0, 255, 136, 0)'
                }
              },
              'tag-glow': {
                '0%': {
                  boxShadow: '0 0 5px rgba(0, 255, 136, 0.2), 0 0 10px rgba(0, 255, 136, 0.1)'
                },
                '100%': {
                  boxShadow: '0 0 10px rgba(0, 255, 136, 0.4), 0 0 20px rgba(0, 255, 136, 0.2)'
                }
              }
            }
          }
        }
      }
    </script>
</head>
<body class="bg-darkbg min-h-screen p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-2xl font-bold text-white mb-8">Tag Styling Test</h1>
        
        <!-- Tag Selection Area (Similar to Save Video Modal) -->
        <div class="bg-dark-card border border-dark-border rounded-xl p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
                <div class="flex items-center gap-3">
                    <h3 class="font-bold text-white text-lg">Select tags</h3>
                    <div class="flex items-center justify-center w-6 h-6 bg-accent text-darkbg rounded-full text-xs font-bold">
                        3
                    </div>
                </div>
                <button class="text-sm text-accent hover:text-white transition-all duration-200 font-medium hover:scale-105 px-2 py-1 rounded-md hover:bg-accent/10">
                    Manage
                </button>
            </div>
            
            <div class="bg-gradient-to-br from-[#1a1a1a]/80 via-[#1e1e1e]/60 to-[#1a1a1a]/80 backdrop-blur-sm border border-gray-700/30 p-5 rounded-xl min-h-[10rem] shadow-inner">
                <div class="flex flex-wrap gap-3">
                    <!-- Selected Tag -->
                    <button class="group relative overflow-hidden inline-flex items-center rounded-full transition-all duration-300 ease-out cursor-pointer transform-gpu will-change-transform hover:scale-110 focus:scale-110 focus:outline-none px-4 py-2.5 text-sm font-semibold tracking-wide bg-gradient-to-r from-accent via-accent to-accent-hover text-darkbg shadow-xl shadow-accent/30 border-2 border-accent ring-2 ring-accent/20 ring-offset-2 ring-offset-darkbg">
                        <span class="relative z-10 flex items-center gap-1.5">
                            <svg class="w-3 h-3 text-current" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                            Title
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-white/5 via-white/10 to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </button>
                    
                    <!-- Unselected Tags -->
                    <button class="group relative overflow-hidden inline-flex items-center rounded-full transition-all duration-300 ease-out cursor-pointer transform-gpu will-change-transform hover:scale-110 focus:scale-110 focus:outline-none px-4 py-2.5 text-sm font-semibold tracking-wide bg-gradient-to-r from-[#2a2a2a] via-[#2d2d2d] to-[#2a2a2a] text-gray-300 border-2 border-gray-600/50 hover:from-[#333333] hover:via-[#363636] hover:to-[#333333] hover:text-white hover:border-gray-500/70 hover:shadow-lg hover:shadow-gray-400/10 active:scale-95">
                        <span class="relative z-10 flex items-center gap-1.5">
                            Content
                        </span>
                    </button>
                    
                    <button class="group relative overflow-hidden inline-flex items-center rounded-full transition-all duration-300 ease-out cursor-pointer transform-gpu will-change-transform hover:scale-110 focus:scale-110 focus:outline-none px-4 py-2.5 text-sm font-semibold tracking-wide bg-gradient-to-r from-[#2a2a2a] via-[#2d2d2d] to-[#2a2a2a] text-gray-300 border-2 border-gray-600/50 hover:from-[#333333] hover:via-[#363636] hover:to-[#333333] hover:text-white hover:border-gray-500/70 hover:shadow-lg hover:shadow-gray-400/10 active:scale-95">
                        <span class="relative z-10 flex items-center gap-1.5">
                            Hook
                        </span>
                    </button>
                    
                    <button class="group relative overflow-hidden inline-flex items-center rounded-full transition-all duration-300 ease-out cursor-pointer transform-gpu will-change-transform hover:scale-110 focus:scale-110 focus:outline-none px-4 py-2.5 text-sm font-semibold tracking-wide bg-gradient-to-r from-[#2a2a2a] via-[#2d2d2d] to-[#2a2a2a] text-gray-300 border-2 border-gray-600/50 hover:from-[#333333] hover:via-[#363636] hover:to-[#333333] hover:text-white hover:border-gray-500/70 hover:shadow-lg hover:shadow-gray-400/10 active:scale-95">
                        <span class="relative z-10 flex items-center gap-1.5">
                            Idea
                        </span>
                    </button>
                    
                    <button class="group relative overflow-hidden inline-flex items-center rounded-full transition-all duration-300 ease-out cursor-pointer transform-gpu will-change-transform hover:scale-110 focus:scale-110 focus:outline-none px-4 py-2.5 text-sm font-semibold tracking-wide bg-gradient-to-r from-[#2a2a2a] via-[#2d2d2d] to-[#2a2a2a] text-gray-300 border-2 border-gray-600/50 hover:from-[#333333] hover:via-[#363636] hover:to-[#333333] hover:text-white hover:border-gray-500/70 hover:shadow-lg hover:shadow-gray-400/10 active:scale-95">
                        <span class="relative z-10 flex items-center gap-1.5">
                            Structure
                        </span>
                    </button>
                    
                    <button class="group relative overflow-hidden inline-flex items-center rounded-full transition-all duration-300 ease-out cursor-pointer transform-gpu will-change-transform hover:scale-110 focus:scale-110 focus:outline-none px-4 py-2.5 text-sm font-semibold tracking-wide bg-gradient-to-r from-[#2a2a2a] via-[#2d2d2d] to-[#2a2a2a] text-gray-300 border-2 border-gray-600/50 hover:from-[#333333] hover:via-[#363636] hover:to-[#333333] hover:text-white hover:border-gray-500/70 hover:shadow-lg hover:shadow-gray-400/10 active:scale-95">
                        <span class="relative z-10 flex items-center gap-1.5">
                            Thumbnail
                        </span>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Instructions -->
        <div class="bg-dark-card/50 border border-dark-border/50 rounded-lg p-4">
            <h3 class="text-white font-semibold mb-2">Test Instructions:</h3>
            <ul class="text-gray-300 text-sm space-y-1">
                <li>• Hover over tags to see smooth scaling and color transitions</li>
                <li>• Selected tags show checkmark icons and accent coloring</li>
                <li>• Unselected tags have subtle gradients and hover effects</li>
                <li>• All tags have improved typography and spacing</li>
                <li>• The container has a modern gradient background with inner shadow</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Add click functionality for testing
        document.querySelectorAll('button[class*="bg-gradient-to-r"]').forEach(button => {
            if (!button.textContent.includes('Manage')) {
                button.addEventListener('click', function() {
                    const isSelected = this.classList.contains('from-accent');
                    
                    if (isSelected) {
                        // Deselect
                        this.className = this.className.replace(/from-accent.*?ring-offset-darkbg/g, 'from-[#2a2a2a] via-[#2d2d2d] to-[#2a2a2a] text-gray-300 border-2 border-gray-600/50 hover:from-[#333333] hover:via-[#363636] hover:to-[#333333] hover:text-white hover:border-gray-500/70 hover:shadow-lg hover:shadow-gray-400/10 active:scale-95');
                        this.querySelector('svg')?.remove();
                    } else {
                        // Select
                        this.className = this.className.replace(/from-\[#2a2a2a\].*?active:scale-95/g, 'from-accent via-accent to-accent-hover text-darkbg shadow-xl shadow-accent/30 border-2 border-accent ring-2 ring-accent/20 ring-offset-2 ring-offset-darkbg');
                        const span = this.querySelector('span');
                        if (span && !span.querySelector('svg')) {
                            span.innerHTML = `<svg class="w-3 h-3 text-current" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>` + span.textContent;
                        }
                    }
                    
                    // Update counter
                    const counter = document.querySelector('.bg-accent.text-darkbg.rounded-full');
                    const selectedCount = document.querySelectorAll('button[class*="from-accent"]').length - 1; // -1 for manage button
                    counter.textContent = selectedCount;
                    counter.style.display = selectedCount > 0 ? 'flex' : 'none';
                });
            }
        });
    </script>
</body>
</html>
