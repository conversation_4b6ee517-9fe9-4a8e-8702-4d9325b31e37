
import React from 'react';
import { LineChart, Line, ResponsiveContainer, Tooltip } from 'recharts';
import { formatNumber } from '../lib/utils';

interface StatCardProps {
    title: string;
    value: number | undefined;
    icon: React.ReactNode;
    trendData: { value: number }[];
}

export const StatCard: React.FC<StatCardProps> = ({ title, value, icon, trendData }) => {
    const isTrendingUp = trendData.length > 1 && trendData[trendData.length - 1].value >= trendData[0].value;

    return (
        <div className="bg-dark-card border border-dark-border rounded-xl p-6 flex flex-col justify-between hover:bg-gray-800/50 hover:border-accent/50 transition-all duration-300 transform hover:-translate-y-1">
            <div className="flex justify-between items-start">
                <div className="space-y-1">
                    <p className="text-gray-400 text-sm">{title}</p>
                    <p className="text-3xl font-bold text-white">{formatNumber(value)}</p>
                </div>
                {icon}
            </div>
            <div className="h-16 mt-4 -mb-4 -mx-2">
                 <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={trendData}>
                        <defs>
                            <linearGradient id={isTrendingUp ? "trendUp" : "trendDown"} x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor={isTrendingUp ? "#00ff88" : "#ff4d4d"} stopOpacity={0.4}/>
                                <stop offset="95%" stopColor={isTrendingUp ? "#00ff88" : "#ff4d4d"} stopOpacity={0}/>
                            </linearGradient>
                        </defs>
                        <Tooltip
                            contentStyle={{
                                background: 'rgba(30, 30, 30, 0.8)',
                                border: '1px solid #444',
                                borderRadius: '8px',
                                color: '#fff',
                                backdropFilter: 'blur(5px)',
                            }}
                            labelStyle={{ display: 'none' }}
                            formatter={(val: number) => [formatNumber(val), null]}
                        />
                        <Line
                            type="monotone"
                            dataKey="value"
                            stroke={isTrendingUp ? "#00ff88" : "#ff4d4d"}
                            strokeWidth={2}
                            dot={false}
                        />
                    </LineChart>
                </ResponsiveContainer>
            </div>
        </div>
    );
};
