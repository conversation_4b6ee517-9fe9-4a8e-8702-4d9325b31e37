


import React, { useState } from 'react';
import type { Folder, Tag } from '../types';
import { CloseIcon } from './Icons';
import { SelectableTag } from './Tag';

interface SaveVideoModalProps {
    folders: Folder[];
    tags: Tag[];
    onClose: () => void;
    onSave: (data: {url: string, folderId: string, tagIds: string[], notes: string}) => Promise<void>;
    onManageFolders: () => void;
    onManageTags: () => void;
    prefilledUrl?: string | null;
}

export const SaveVideoModal: React.FC<SaveVideoModalProps> = ({ folders, tags, onClose, onSave, onManageFolders, onManageTags, prefilledUrl }) => {
    const [url, setUrl] = useState(prefilledUrl || '');
    const [notes, setNotes] = useState('');
    const [selectedFolderId, setSelectedFolderId] = useState<string | null>(folders.length > 0 ? folders[0].id : null);
    const [selectedTagIds, setSelectedTagIds] = useState<Set<string>>(new Set());
    const [isSaving, setIsSaving] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const toggleTag = (tagId: string) => {
        setSelectedTagIds(prev => {
            const newSet = new Set(prev);
            if (newSet.has(tagId)) {
                newSet.delete(tagId);
            } else {
                newSet.add(tagId);
            }
            return newSet;
        });
    };

    const handleSave = async () => {
        if(!url.trim() || !selectedFolderId) {
            setError('Please provide a video URL and select a folder.');
            return;
        }
        setError(null);
        setIsSaving(true);
        try {
            await onSave({
                url,
                folderId: selectedFolderId,
                tagIds: Array.from(selectedTagIds),
                notes
            });
            // The parent will close the modal on success.
        } catch (err) {
            setError((err as Error).message);
        } finally {
            setIsSaving(false);
        }
    }

    return (
        <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4" aria-modal="true" role="dialog">
            <div className="fixed inset-0" onClick={onClose} aria-hidden="true"></div>
            <div className="relative bg-dark-card border border-dark-border rounded-xl w-full max-w-2xl p-8">
                <div className="flex justify-between items-center mb-2">
                    <h2 className="text-xl font-bold text-white">Save video</h2>
                    <button onClick={onClose} className="p-1 rounded-full hover:bg-gray-700">
                        <CloseIcon className="w-6 h-6 text-gray-400" />
                    </button>
                </div>
                <p className="text-gray-400 mb-6">The video title and thumbnail will be fetched automatically.</p>

                <div className="space-y-6">
                    <div>
                        <label htmlFor="video-url" className="block text-sm font-medium text-gray-300 mb-2">URL*</label>
                        <input
                            id="video-url"
                            type="text"
                            value={url}
                            onChange={(e) => setUrl(e.target.value)}
                            placeholder="Paste video link"
                            className="w-full bg-[#1a1a1a] border border-gray-600 rounded-lg px-4 py-2.5 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent disabled:bg-gray-800/50 disabled:cursor-not-allowed"
                            disabled={!!prefilledUrl}
                        />
                    </div>
                     <div>
                        <label htmlFor="video-notes" className="block text-sm font-medium text-gray-300 mb-2">Notes</label>
                        <textarea
                            id="video-notes"
                            value={notes}
                            onChange={(e) => setNotes(e.target.value)}
                            placeholder="Add personal notes or a summary..."
                            rows={3}
                            className="w-full bg-[#1a1a1a] border border-gray-600 rounded-lg px-4 py-2.5 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-accent"
                        />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <div className="flex justify-between items-center mb-4">
                                <div className="flex items-center gap-3">
                                    <h3 className="font-bold text-white text-lg">Select a folder</h3>
                                    <div className="flex items-center justify-center px-2 py-1 bg-red-500/20 text-red-400 rounded-full text-xs font-bold border border-red-500/30">
                                        Required
                                    </div>
                                </div>
                                <button
                                    onClick={onManageFolders}
                                    className="text-sm text-accent hover:text-white transition-all duration-200 font-medium hover:scale-105 px-2 py-1 rounded-md hover:bg-accent/10"
                                >
                                    Manage
                                </button>
                            </div>
                            <div className="bg-gradient-to-br from-[#1a1a1a]/80 via-[#1e1e1e]/60 to-[#1a1a1a]/80 backdrop-blur-sm border border-gray-700/30 p-5 rounded-xl max-h-44 overflow-y-auto shadow-inner">
                                <div className="space-y-3">
                                    {folders.map(folder => {
                                        const isSelected = selectedFolderId === folder.id;
                                        return (
                                            <label
                                                key={folder.id}
                                                className={`group relative flex items-center gap-4 cursor-pointer p-4 rounded-xl transition-all duration-300 transform hover:scale-[1.02] ${
                                                    isSelected
                                                        ? 'bg-gradient-to-r from-accent/20 via-accent/15 to-accent/20 border-2 border-accent/40 shadow-lg shadow-accent/20 ring-2 ring-accent/10'
                                                        : 'bg-gradient-to-r from-gray-800/60 via-gray-700/40 to-gray-800/60 border-2 border-gray-600/30 hover:from-gray-700/70 hover:via-gray-600/50 hover:to-gray-700/70 hover:border-gray-500/50 hover:shadow-md hover:shadow-gray-400/10'
                                                }`}
                                            >
                                                <div className="relative">
                                                    <input
                                                        type="radio"
                                                        name="folder"
                                                        checked={isSelected}
                                                        onChange={() => setSelectedFolderId(folder.id)}
                                                        className="sr-only"
                                                    />
                                                    <div className={`w-5 h-5 rounded-full border-2 transition-all duration-200 flex items-center justify-center ${
                                                        isSelected
                                                            ? 'border-accent bg-accent shadow-lg shadow-accent/30'
                                                            : 'border-gray-500 bg-transparent group-hover:border-gray-400'
                                                    }`}>
                                                        {isSelected && (
                                                            <div className="w-2 h-2 bg-darkbg rounded-full"></div>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="flex items-center gap-3 flex-1">
                                                    <div className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                                                        isSelected ? 'bg-accent' : 'bg-gray-500 group-hover:bg-gray-400'
                                                    }`}></div>
                                                    <span className={`font-semibold transition-colors duration-200 ${
                                                        isSelected ? 'text-white' : 'text-gray-300 group-hover:text-white'
                                                    }`}>
                                                        {folder.name}
                                                    </span>
                                                </div>
                                                {isSelected && (
                                                    <div className="flex items-center justify-center w-6 h-6 bg-accent/20 rounded-full">
                                                        <svg className="w-3 h-3 text-accent" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                        </svg>
                                                    </div>
                                                )}
                                                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                                            </label>
                                        );
                                    })}
                                </div>
                                {folders.length === 0 && (
                                    <div className="flex flex-col items-center justify-center h-24 text-gray-500">
                                        <div className="w-8 h-8 mb-2 opacity-50">
                                            <svg fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                                            </svg>
                                        </div>
                                        <p className="text-sm font-medium">No folders available</p>
                                        <p className="text-xs mt-1">Create a folder to get started</p>
                                    </div>
                                )}
                            </div>
                        </div>

                        <div>
                            <div className="flex justify-between items-center mb-4">
                                <div className="flex items-center gap-3">
                                    <h3 className="font-bold text-white text-lg">Select tags</h3>
                                    {selectedTagIds.size > 0 && (
                                        <div className="flex items-center justify-center w-6 h-6 bg-accent text-darkbg rounded-full text-xs font-bold">
                                            {selectedTagIds.size}
                                        </div>
                                    )}
                                </div>
                                <button
                                    onClick={onManageTags}
                                    className="text-sm text-accent hover:text-white transition-all duration-200 font-medium hover:scale-105 px-2 py-1 rounded-md hover:bg-accent/10"
                                >
                                    Manage
                                </button>
                            </div>
                            <div className="bg-gradient-to-br from-[#1a1a1a]/80 via-[#1e1e1e]/60 to-[#1a1a1a]/80 backdrop-blur-sm border border-gray-700/30 p-5 rounded-xl min-h-[10rem] max-h-44 overflow-y-auto shadow-inner">
                                <div className="flex flex-wrap gap-3">
                                    {tags.map(tag => (
                                        <SelectableTag
                                            key={tag.id}
                                            isSelected={selectedTagIds.has(tag.id)}
                                            onClick={() => toggleTag(tag.id)}
                                            size="md"
                                        >
                                            {tag.name}
                                        </SelectableTag>
                                    ))}
                                </div>
                                {tags.length === 0 && (
                                    <div className="flex flex-col items-center justify-center h-24 text-gray-500">
                                        <div className="w-8 h-8 mb-2 opacity-50">
                                            <svg fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M9.243 3.03a1 1 0 01.727 1.213L9.53 6h2.94l.56-2.243a1 1 0 111.94.486L14.53 6H17a1 1 0 110 2h-2.97l-1 4H15a1 1 0 110 2h-2.47l-.56 2.242a1 1 0 11-1.94-.485L10.47 14H7.53l-.56 2.242a1 1 0 11-1.94-.485L5.47 14H3a1 1 0 110-2h2.97l1-4H5a1 1 0 110-2h2.47l.56-2.243a1 1 0 011.213-.727zM9.03 8l-1 4h2.94l1-4H9.03z" clipRule="evenodd" />
                                            </svg>
                                        </div>
                                        <p className="text-sm font-medium">No tags available</p>
                                        <p className="text-xs mt-1">Create some tags to get started</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {error && <p className="text-red-400 text-sm text-center -mb-2">{error}</p>}

                    <div className="pt-2 flex justify-end">
                        <button onClick={handleSave} disabled={isSaving} className="px-6 py-2.5 rounded-lg bg-white text-darkbg font-semibold hover:bg-gray-200 transition-colors w-32 text-center disabled:bg-gray-400">
                           {isSaving ? <div className="w-5 h-5 mx-auto border-2 border-darkbg border-t-transparent rounded-full animate-spin"></div> : 'Save video'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};